/**
 * Plugin to add support for serializing as JavaScript.
 *
 * @this {Processor<undefined, undefined, undefined, Program, string>}
 *   Processor instance.
 * @param {Readonly<Options> | null | undefined} [options]
 *   Configuration (optional).
 * @returns {undefined}
 *   Nothing.
 */
export default function recmaStringify(this: Processor<undefined, undefined, undefined, Program, string>, options?: Readonly<Options> | null | undefined): undefined;
export default class recmaStringify {
    /**
     * Plugin to add support for serializing as JavaScript.
     *
     * @this {Processor<undefined, undefined, undefined, Program, string>}
     *   Processor instance.
     * @param {Readonly<Options> | null | undefined} [options]
     *   Configuration (optional).
     * @returns {undefined}
     *   Nothing.
     */
    constructor(this: Processor<undefined, undefined, undefined, Program, string>, options?: Readonly<Options> | null | undefined);
    compiler: (tree: Program, file: VFile) => string;
}
import type { Options } from 'recma-stringify';
import type { Program } from 'estree';
import type { Processor } from 'unified';
import type { VFile } from 'vfile';
//# sourceMappingURL=index.d.ts.map