/**
 * Plugin to build JSX.
 *
 * @param {Options | null | undefined} [options]
 *   Configuration (optional).
 * @returns
 *   Transform.
 */
export default function recmaJsx(options?: Options | null | undefined): (tree: Program, file: VFile) => undefined;
import type { Options } from 'recma-build-jsx';
import type { Program } from 'estree';
import type { VFile } from 'vfile';
//# sourceMappingURL=index.d.ts.map