/**
 * Transform a hast tree to preact, react, solid, svelte, vue, etc.,
 * with an automatic JSX runtime.
 *
 * @param {Nodes} tree
 *   Tree to transform.
 * @param {Options} options
 *   Configuration (required).
 * @returns {JsxElement}
 *   JSX element.
 */
export function toJsxRuntime(tree: Nodes, options: Options): JsxElement;
import type { Nodes } from 'hast';
import type { Options } from 'hast-util-to-jsx-runtime';
import type { JsxElement } from './types.js';
//# sourceMappingURL=index.d.ts.map