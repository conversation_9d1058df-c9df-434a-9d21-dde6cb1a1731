{"author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "bugs": "https://github.com/remarkjs/remark-rehype/issues", "contributors": ["<PERSON> <<EMAIL>> (https://johno.com)", "<PERSON> <<EMAIL>> (https://wooorm.com)"], "dependencies": {"@types/hast": "^3.0.0", "@types/mdast": "^4.0.0", "mdast-util-to-hast": "^13.0.0", "unified": "^11.0.0", "vfile": "^6.0.0"}, "description": "remark plugin that turns markdown into HTML to support rehype", "devDependencies": {"@types/node": "^22.0.0", "c8": "^10.0.0", "prettier": "^3.0.0", "rehype-slug": "^6.0.0", "rehype-stringify": "^10.0.0", "remark-cli": "^12.0.0", "remark-parse": "^11.0.0", "remark-preset-wooorm": "^11.0.0", "remark-stringify": "^11.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.60.0"}, "exports": "./index.js", "files": ["index.d.ts.map", "index.d.ts", "index.js", "lib/"], "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "keywords": ["hast", "html", "markdown", "mdast", "plugin", "rehype-plugin", "rehype", "remark-plugin", "remark", "unified"], "license": "MIT", "name": "remark-rehype", "prettier": {"bracketSpacing": false, "singleQuote": true, "semi": false, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "repository": "remarkjs/remark-rehype", "scripts": {"build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark --frail --output --quiet -- . && prettier --log-level warn --write -- . && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --100 --reporter lcov -- npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "sideEffects": false, "typeCoverage": {"atLeast": 100, "strict": true}, "type": "module", "version": "11.1.2", "xo": {"prettier": true}}