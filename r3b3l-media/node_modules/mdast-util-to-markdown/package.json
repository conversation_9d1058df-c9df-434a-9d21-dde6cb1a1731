{"author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "bugs": "https://github.com/syntax-tree/mdast-util-to-markdown/issues", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "dependencies": {"@types/mdast": "^4.0.0", "@types/unist": "^3.0.0", "longest-streak": "^3.0.0", "mdast-util-phrasing": "^4.0.0", "mdast-util-to-string": "^4.0.0", "micromark-util-classify-character": "^2.0.0", "micromark-util-decode-string": "^2.0.0", "unist-util-visit": "^5.0.0", "zwitch": "^2.0.0"}, "description": "mdast utility to serialize markdown", "devDependencies": {"@types/node": "^22.0.0", "c8": "^10.0.0", "mdast-util-from-markdown": "^2.0.0", "prettier": "^3.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^10.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "unist-util-remove-position": "^5.0.0", "xo": "^0.59.0"}, "exports": "./index.js", "files": ["lib/", "index.d.ts", "index.js"], "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "keywords": ["ast", "compile", "markdown", "markup", "mdast-util", "mdast", "serialize", "stringify", "syntax", "tree", "unist", "utility", "util"], "license": "MIT", "name": "mdast-util-to-markdown", "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "repository": "syntax-tree/mdast-util-to-markdown", "version": "2.1.2", "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . -qfo && prettier . -w --log-level warn && xo --fix", "test-api": "node --conditions development test/index.js", "test-coverage": "c8 --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "sideEffects": false, "typeCoverage": {"atLeast": 100, "ignoreFiles": ["lib/types.d.ts"], "strict": true}, "type": "module", "xo": {"overrides": [{"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/array-type": ["error", {"default": "generic"}], "@typescript-eslint/ban-types": ["error", {"extendDefaults": true}], "@typescript-eslint/consistent-type-definitions": ["error", "interface"]}}, {"files": ["test/**/*.js"], "rules": {"max-depth": "off", "no-await-in-loop": "off"}}], "prettier": true, "rules": {"complexity": "off", "unicorn/prefer-at": "off", "unicorn/prefer-code-point": "off"}}}