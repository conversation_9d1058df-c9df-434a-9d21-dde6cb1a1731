export namespace handlers {
    export { blockquote };
    export { hardBreak as break };
    export { code };
    export { strikethrough as delete };
    export { emphasis };
    export { footnoteReference };
    export { heading };
    export { html };
    export { imageReference };
    export { image };
    export { inlineCode };
    export { linkReference };
    export { link };
    export { listItem };
    export { list };
    export { paragraph };
    export { root };
    export { strong };
    export { table };
    export { tableCell };
    export { tableRow };
    export { text };
    export { thematicBreak };
    export { ignore as toml };
    export { ignore as yaml };
    export { ignore as definition };
    export { ignore as footnoteDefinition };
}
import { blockquote } from './blockquote.js';
import { hardBreak } from './break.js';
import { code } from './code.js';
import { strikethrough } from './delete.js';
import { emphasis } from './emphasis.js';
import { footnoteReference } from './footnote-reference.js';
import { heading } from './heading.js';
import { html } from './html.js';
import { imageReference } from './image-reference.js';
import { image } from './image.js';
import { inlineCode } from './inline-code.js';
import { linkReference } from './link-reference.js';
import { link } from './link.js';
import { listItem } from './list-item.js';
import { list } from './list.js';
import { paragraph } from './paragraph.js';
import { root } from './root.js';
import { strong } from './strong.js';
import { table } from './table.js';
import { tableCell } from './table-cell.js';
import { tableRow } from './table-row.js';
import { text } from './text.js';
import { thematicBreak } from './thematic-break.js';
declare function ignore(): undefined;
export {};
