{"author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "bugs": "https://github.com/mdx-js/recma/issues", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "description": "recma plugin to transform HTML (hast) to JS (estree)", "dependencies": {"@types/estree": "^1.0.0", "@types/hast": "^3.0.0", "hast-util-to-estree": "^3.0.0"}, "exports": "./index.js", "files": ["lib/", "index.d.ts", "index.js"], "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "homepage": "https://github.com/mdx-js/recma", "keywords": ["abstract", "ast", "compile", "estree", "hast", "html", "javascript", "jsx", "plugin", "recma-plugin", "recma", "rehype-plugin", "rehype", "syntax", "tree", "unified"], "license": "MIT", "name": "rehype-recma", "repository": "https://github.com/mdx-js/recma/tree/main/packages/rehype-recma", "scripts": {}, "sideEffects": false, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true}, "type": "module", "version": "1.0.0"}