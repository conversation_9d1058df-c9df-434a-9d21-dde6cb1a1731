/**
 * Plugin to transform HTML (hast) to JS (estree).
 *
 * @param {Options | null | undefined} [options]
 *   Configuration (optional).
 * @returns
 *   Transform.
 */
export default function rehypeRecma(options?: Options | null | undefined): (tree: Root) => Program;
import type { Options } from 'rehype-recma';
import type { Root } from 'hast';
import type { Program } from 'estree';
//# sourceMappingURL=index.d.ts.map