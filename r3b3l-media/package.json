{"name": "r3b3l-media", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.3.3", "@prisma/client": "^6.8.2", "@types/bcryptjs": "^2.4.6", "@types/mdx": "^2.0.13", "bcryptjs": "^3.0.2", "gray-matter": "^4.0.3", "next": "15.3.3", "next-auth": "^4.24.11", "openai": "^5.0.1", "prisma": "^6.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}