(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[116],{3792:(e,s,r)=>{Promise.resolve().then(r.bind(r,5430))},5430:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d});var t=r(5155),a=r(2115),l=r(2108),n=r(5695);function d(){let[e,s]=(0,a.useState)(""),[r,d]=(0,a.useState)(""),[i,o]=(0,a.useState)(""),[c,u]=(0,a.useState)(!1),m=(0,n.useRouter)(),x=async s=>{s.preventDefault(),u(!0),o("");try{let s=await (0,l.signIn)("credentials",{email:e,password:r,redirect:!1});if(null==s?void 0:s.error)o("Invalid credentials");else{var t;let e=await (0,l.getSession)();(null==e||null==(t=e.user)?void 0:t.role)==="admin"?m.push("/admin/dashboard"):o("Access denied")}}catch(e){o("Login failed")}finally{u(!1)}};return(0,t.jsx)("div",{className:"min-h-screen bg-black flex items-center justify-center px-4",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("h1",{className:"text-4xl font-bold mb-2",children:[(0,t.jsx)("span",{className:"text-red-500",children:"R3B3L"})," ",(0,t.jsx)("span",{className:"text-cyan-400",children:"M3D14"})]}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"ADMIN ACCESS"}),(0,t.jsx)("p",{className:"text-gray-400",children:"Ghost King Command Center"}),(0,t.jsx)("div",{className:"w-full h-px bg-gradient-to-r from-transparent via-red-500 to-transparent mt-4"})]}),(0,t.jsxs)("form",{onSubmit:x,className:"mt-8 space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300 mb-2",children:"Admin Email"}),(0,t.jsx)("input",{id:"email",name:"email",type:"email",required:!0,value:e,onChange:e=>s(e.target.value),className:"w-full px-4 py-3 bg-gray-900 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent",placeholder:"Enter admin email"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-300 mb-2",children:"Password"}),(0,t.jsx)("input",{id:"password",name:"password",type:"password",required:!0,value:r,onChange:e=>d(e.target.value),className:"w-full px-4 py-3 bg-gray-900 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent",placeholder:"Enter password"})]})]}),i&&(0,t.jsx)("div",{className:"bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded-lg",children:i}),(0,t.jsx)("button",{type:"submit",disabled:c,className:"w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-bold py-3 px-4 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:c?(0,t.jsxs)("div",{className:"flex items-center justify-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"ACCESSING..."]}):"ACCESS COMMAND CENTER"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"\uD83D\uDD25 FLAME-Licensed Content Management System \uD83D\uDD25"}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"For the Empire • For the ReBeLuTioN"})]})]})})}},5695:(e,s,r)=>{"use strict";var t=r(8999);r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}})}},e=>{var s=s=>e(e.s=s);e.O(0,[108,441,684,358],()=>s(3792)),_N_E=e.O()}]);