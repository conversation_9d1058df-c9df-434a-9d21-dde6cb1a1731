(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},2926:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var r=s(5155),n=s(2108);function i(e){let{children:t}=e;return(0,r.jsx)(n.<PERSON>,{children:t})}},5494:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var r=s(5155),n=s(6874),i=s.n(n),o=s(2115);function d(){let[e,t]=(0,o.useState)(!1);return(0,r.jsx)("nav",{className:"bg-black/90 backdrop-blur-sm border-b border-gray-800 sticky top-0 z-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(i(),{href:"/",className:"text-2xl font-bold neon-text",children:"R3B3L M3D14"})}),(0,r.jsx)("div",{className:"hidden md:block",children:(0,r.jsxs)("div",{className:"ml-10 flex items-baseline space-x-4",children:[(0,r.jsx)(i(),{href:"/",className:"text-gray-300 hover:text-white hover:neon-text px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Home"}),(0,r.jsx)(i(),{href:"/articles",className:"text-gray-300 hover:text-white hover:neon-text px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Articles"}),(0,r.jsx)(i(),{href:"/about",className:"text-gray-300 hover:text-white hover:neon-text px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"About"}),(0,r.jsx)("div",{className:"glitch-text px-3 py-2 text-sm font-medium",children:"\uD83D\uDD25 FLAME-LICENSED"})]})}),(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsx)("button",{onClick:()=>t(!e),className:"text-gray-400 hover:text-white hover:bg-gray-700 px-2 py-1 rounded-md",children:(0,r.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e?(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),e&&(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-800",children:[(0,r.jsx)(i(),{href:"/",className:"text-gray-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium",onClick:()=>t(!1),children:"Home"}),(0,r.jsx)(i(),{href:"/articles",className:"text-gray-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium",onClick:()=>t(!1),children:"Articles"}),(0,r.jsx)(i(),{href:"/about",className:"text-gray-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium",onClick:()=>t(!1),children:"About"})]})})]})})}},7185:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.t.bind(s,347,23)),Promise.resolve().then(s.bind(s,5494)),Promise.resolve().then(s.bind(s,2926))}},e=>{var t=t=>e(e.s=t);e.O(0,[690,874,108,441,684,358],()=>t(7185)),_N_E=e.O()}]);