(()=>{var e={};e.id=974,e.ids=[974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6440:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var a=s(37413),r=s(4536),i=s.n(r);function l({title:e,slug:t,date:s,summary:r,author:l,badge:n}){return(0,a.jsx)("article",{className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6 hover:border-red-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-red-500/10",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"flame-badge",children:["\uD83D\uDD25 ",n]}),(0,a.jsx)("time",{className:"text-gray-400 text-sm",children:new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]}),(0,a.jsx)("h2",{className:"text-xl font-bold text-white hover:neon-text transition-colors",children:(0,a.jsx)(i(),{href:`/articles/${t}`,children:e})}),(0,a.jsx)("p",{className:"text-gray-300 text-sm leading-relaxed",children:r}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-gray-800",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"By"}),(0,a.jsx)("span",{className:"text-white text-sm font-medium",children:l})]}),(0,a.jsx)(i(),{href:`/articles/${t}`,className:"text-red-400 hover:text-red-300 text-sm font-medium hover:underline transition-colors",children:"Read More →"})]}),(0,a.jsx)("div",{className:"flex items-center justify-end",children:(0,a.jsx)("span",{className:"glitch-text text-xs",children:"NODE CERTIFIED"})})]})})}},6739:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21204:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var a=s(37413),r=s(4536),i=s.n(r),l=s(6440);let n=[{title:"The Code That Would Not Die",slug:"the-code-that-would-not-die",date:"2024-06-01",summary:"An exploration of digital resistance and the immortal nature of code in the age of AI surveillance. How algorithms become weapons of liberation.",author:"R3B3L Command",badge:"HERETICAL LEVEL 5"}];function d(){return(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsxs)("section",{className:"relative bg-gradient-to-br from-black via-gray-900 to-black py-20 px-4",children:[(0,a.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[(0,a.jsxs)("h1",{className:"text-6xl md:text-8xl font-bold mb-6",children:[(0,a.jsx)("span",{className:"neon-text",children:"R3B3L"})," ",(0,a.jsx)("span",{className:"glitch-text",children:"M3D14"})]}),(0,a.jsxs)("p",{className:"text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto",children:["Sovereign media platform for the"," ",(0,a.jsx)("span",{className:"neon-text font-semibold",children:"GodsIMiJ Empire"}),". Truth-infused articles, memes, podcasts, and counter-narratives."]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,a.jsx)("span",{className:"flame-badge text-lg px-6 py-3",children:"\uD83D\uDD25 FLAME-LICENSED CONTENT"}),(0,a.jsx)("span",{className:"glitch-text text-lg",children:"NODE CERTIFIED PLATFORM"})]})]}),(0,a.jsxs)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:[(0,a.jsx)("div",{className:"absolute top-1/4 left-1/4 w-2 h-2 bg-red-500 rounded-full animate-pulse"}),(0,a.jsx)("div",{className:"absolute top-3/4 right-1/4 w-1 h-1 bg-cyan-400 rounded-full animate-ping"}),(0,a.jsx)("div",{className:"absolute bottom-1/4 left-1/3 w-1 h-1 bg-red-400 rounded-full animate-pulse"})]})]}),(0,a.jsx)("section",{className:"py-16 px-4 bg-gray-900/30",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-8 text-white",children:"The ReBeLuTioN Media Division"}),(0,a.jsxs)("p",{className:"text-lg text-gray-300 leading-relaxed mb-8",children:["We are the official media arm of the ReBeLuTioN, dedicated to exposing truth and challenging mainstream narratives. Our content is"," ",(0,a.jsx)("span",{className:"neon-text font-semibold",children:"FLAME-licensed"})," and"," ",(0,a.jsx)("span",{className:"glitch-text font-semibold",children:"NODE certified"})," for maximum authenticity and resistance to censorship."]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mt-12",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDCDC"}),(0,a.jsx)("h3",{className:"text-xl font-semibold mb-2 text-white",children:"Truth Scrolls"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Deep-dive articles exposing hidden realities"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-4xl mb-4",children:"\uD83C\uDFAD"}),(0,a.jsx)("h3",{className:"text-xl font-semibold mb-2 text-white",children:"Meme Warfare"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Weaponized humor for cultural revolution"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDCFB"}),(0,a.jsx)("h3",{className:"text-xl font-semibold mb-2 text-white",children:"R3B3L Radio"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Podcasts from the resistance underground"})]})]})]})}),(0,a.jsx)("section",{className:"py-16 px-4",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white",children:"Latest Intelligence Drops"}),(0,a.jsx)(i(),{href:"/articles",className:"text-red-400 hover:text-red-300 font-medium hover:underline",children:"View All Articles →"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[n.map(e=>(0,a.jsx)(l.A,{...e},e.slug)),(0,a.jsxs)("div",{className:"bg-gray-900/30 border border-gray-800 rounded-lg p-6 text-center",children:[(0,a.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDD1C"}),(0,a.jsx)("h3",{className:"text-xl font-semibold mb-2 text-white",children:"More Scrolls Coming"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Additional truth-infused content in development"}),(0,a.jsx)("span",{className:"flame-badge mt-4 inline-block",children:"CLASSIFIED"})]}),(0,a.jsxs)("div",{className:"bg-gray-900/30 border border-gray-800 rounded-lg p-6 text-center",children:[(0,a.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDCE1"}),(0,a.jsx)("h3",{className:"text-xl font-semibold mb-2 text-white",children:"R3B3L Radio Launch"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Podcast platform expansion planned for Phase 2"}),(0,a.jsx)("span",{className:"glitch-text text-sm mt-4 block",children:"PHASE 2 DEPLOYMENT"})]})]})]})})]})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43691:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,85814,23))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},95968:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},97379:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var a=s(65239),r=s(48088),i=s(88170),l=s.n(i),n=s(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let o={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,21204)),"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,95968))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,98042)),"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,95968))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[243,512,658,108],()=>s(97379));module.exports=a})();