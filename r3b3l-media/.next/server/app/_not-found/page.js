(()=>{var e={};e.id=492,e.ids=[492],e.modules={4:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/components/Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/components/Navbar.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1591:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var s=r(5239),i=r(8088),n=r(8170),o=r.n(n),a=r(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,8042)),"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=[],m={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2032:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5184:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},5597:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,5814,23)),Promise.resolve().then(r.bind(r,9190))},8042:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var s=r(7413);r(1135);var i=r(4),n=r(4536),o=r.n(n);function a(){return(0,s.jsxs)("footer",{className:"bg-black/90 border-t border-gray-800 mt-auto",children:[(0,s.jsxs)("div",{className:"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-xl font-bold neon-text",children:"R3B3L M3D14"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Sovereign media platform for the GodsIMiJ Empire. Truth-infused content for the ReBeLuTioN."}),(0,s.jsx)("div",{className:"glitch-text text-sm",children:"\uD83D\uDD25 FLAME-LICENSED CONTENT"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white",children:"Navigation"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:(0,s.jsx)(o(),{href:"/",className:"text-gray-400 hover:text-white hover:neon-text transition-colors",children:"Home"})}),(0,s.jsx)("li",{children:(0,s.jsx)(o(),{href:"/articles",className:"text-gray-400 hover:text-white hover:neon-text transition-colors",children:"Articles"})}),(0,s.jsx)("li",{children:(0,s.jsx)(o(),{href:"/about",className:"text-gray-400 hover:text-white hover:neon-text transition-colors",children:"About"})})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white",children:"The Empire"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{className:"text-gray-400",children:"GodsIMiJ Empire"}),(0,s.jsx)("li",{className:"text-gray-400",children:"ReBeLuTioN Media Division"}),(0,s.jsx)("li",{className:"text-gray-400",children:"Anti-Mainstream Intelligence"}),(0,s.jsx)("li",{className:"glitch-text",children:"NODE Certified"})]})]})]}),(0,s.jsxs)("div",{className:"mt-8 pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center",children:[(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"\xa9 2024 R3B3L M3D14. All rights reserved to the Empire."}),(0,s.jsxs)("div",{className:"mt-4 md:mt-0 flex space-x-4 text-sm",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Powered by"}),(0,s.jsx)("span",{className:"neon-text",children:"FLAME"}),(0,s.jsx)("span",{className:"text-gray-400",children:"•"}),(0,s.jsx)("span",{className:"glitch-text",children:"NODE SEALED"})]})]})]}),(0,s.jsx)("div",{className:"node-seal",children:"NODE CERTIFIED • FLAME LICENSED"})]})}let l={title:"R3B3L M3D14 | Sovereign Media Platform",description:"Truth-infused articles, memes, podcasts, and counter-narratives. Official media arm of the GodsIMiJ Empire ReBeLuTioN.",keywords:["rebel media","alternative news","counter-narrative","flame licensed","node certified"],authors:[{name:"R3B3L M3D14 Team"}],creator:"GodsIMiJ Empire",publisher:"R3B3L M3D14",robots:"index, follow",openGraph:{title:"R3B3L M3D14 | Sovereign Media Platform",description:"Truth-infused content for the ReBeLuTioN",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"R3B3L M3D14",description:"Sovereign media platform for truth-seekers"}};function d({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsxs)("body",{className:"min-h-screen flex flex-col bg-black text-white",children:[(0,s.jsx)(i.default,{}),(0,s.jsx)("main",{className:"flex-grow",children:e}),(0,s.jsx)(a,{})]})})}},9101:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,4))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9190:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var s=r(687),i=r(5814),n=r.n(i),o=r(3210);function a(){let[e,t]=(0,o.useState)(!1);return(0,s.jsx)("nav",{className:"bg-black/90 backdrop-blur-sm border-b border-gray-800 sticky top-0 z-50",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(n(),{href:"/",className:"text-2xl font-bold neon-text",children:"R3B3L M3D14"})}),(0,s.jsx)("div",{className:"hidden md:block",children:(0,s.jsxs)("div",{className:"ml-10 flex items-baseline space-x-4",children:[(0,s.jsx)(n(),{href:"/",className:"text-gray-300 hover:text-white hover:neon-text px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Home"}),(0,s.jsx)(n(),{href:"/articles",className:"text-gray-300 hover:text-white hover:neon-text px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Articles"}),(0,s.jsx)(n(),{href:"/about",className:"text-gray-300 hover:text-white hover:neon-text px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"About"}),(0,s.jsx)("div",{className:"glitch-text px-3 py-2 text-sm font-medium",children:"\uD83D\uDD25 FLAME-LICENSED"})]})}),(0,s.jsx)("div",{className:"md:hidden",children:(0,s.jsx)("button",{onClick:()=>t(!e),className:"text-gray-400 hover:text-white hover:bg-gray-700 px-2 py-1 rounded-md",children:(0,s.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e?(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),e&&(0,s.jsx)("div",{className:"md:hidden",children:(0,s.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-800",children:[(0,s.jsx)(n(),{href:"/",className:"text-gray-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium",onClick:()=>t(!1),children:"Home"}),(0,s.jsx)(n(),{href:"/articles",className:"text-gray-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium",onClick:()=>t(!1),children:"Articles"}),(0,s.jsx)(n(),{href:"/about",className:"text-gray-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium",onClick:()=>t(!1),children:"About"})]})})]})})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,663],()=>r(1591));module.exports=s})();