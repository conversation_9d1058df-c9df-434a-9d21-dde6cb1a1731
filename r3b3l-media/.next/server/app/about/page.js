(()=>{var e={};e.id=220,e.ids=[220],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28770:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,metadata:()=>a});var r=s(37413);let a={title:"About | R3B3L M3D14",description:"Learn about the R3B3L M3D14 platform and the GodsIMiJ Empire ReBeLuTioN media division."};function i(){return(0,r.jsx)("div",{className:"min-h-screen py-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("header",{className:"text-center mb-16",children:[(0,r.jsxs)("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:[(0,r.jsx)("span",{className:"neon-text",children:"About"})," ",(0,r.jsx)("span",{className:"glitch-text",children:"R3B3L M3D14"})]}),(0,r.jsx)("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto",children:"The official media arm of the GodsIMiJ Empire ReBeLuTioN, dedicated to truth, resistance, and digital liberation."})]}),(0,r.jsxs)("section",{className:"mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-white mb-8",children:"Our Mission"}),(0,r.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg p-8",children:[(0,r.jsx)("p",{className:"text-gray-300 text-lg leading-relaxed mb-6",children:"R3B3L M3D14 exists to challenge mainstream narratives and provide a platform for truth-seekers in the digital age. We are the resistance against algorithmic censorship, corporate media manipulation, and the suppression of free thought."}),(0,r.jsxs)("p",{className:"text-gray-300 text-lg leading-relaxed",children:["Every piece of content we publish is"," ",(0,r.jsx)("span",{className:"neon-text font-semibold",children:"FLAME-licensed"})," for maximum distribution and"," ",(0,r.jsx)("span",{className:"glitch-text font-semibold",children:"NODE certified"})," for authenticity verification."]})]})]}),(0,r.jsxs)("section",{className:"mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-white mb-8",children:"What We Do"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[(0,r.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDCDC"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-white mb-3",children:"Truth Scrolls"}),(0,r.jsx)("p",{className:"text-gray-400",children:"In-depth articles exposing hidden realities, challenging official narratives, and providing alternative perspectives on current events."})]}),(0,r.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[(0,r.jsx)("div",{className:"text-4xl mb-4",children:"\uD83C\uDFAD"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-white mb-3",children:"Meme Warfare"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Weaponized humor and visual content designed to spread truth through viral distribution and cultural impact."})]}),(0,r.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[(0,r.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDCFB"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-white mb-3",children:"R3B3L Radio"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Podcast platform featuring voices from the resistance underground, uncensored discussions, and alternative media content."})]}),(0,r.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[(0,r.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDD0D"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-white mb-3",children:"Intelligence Drops"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Leaked documents, insider information, and investigative reports that mainstream media won't touch."})]})]})]}),(0,r.jsxs)("section",{className:"mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-white mb-8",children:"The GodsIMiJ Empire"}),(0,r.jsxs)("div",{className:"bg-gradient-to-r from-gray-900/50 to-gray-800/50 border border-gray-700 rounded-lg p-8",children:[(0,r.jsx)("p",{className:"text-gray-300 text-lg leading-relaxed mb-6",children:"R3B3L M3D14 operates under the banner of the GodsIMiJ Empire, a decentralized network of digital freedom fighters, truth-seekers, and resistance operatives working to preserve human autonomy in the age of AI surveillance."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mt-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold neon-text mb-2",children:"FLAME"}),(0,r.jsx)("div",{className:"text-gray-400 text-sm",children:"Freedom License for Autonomous Media Expression"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold glitch-text mb-2",children:"NODE"}),(0,r.jsx)("div",{className:"text-gray-400 text-sm",children:"Network of Digital Enlightenment"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-white mb-2",children:"R3B3L"}),(0,r.jsx)("div",{className:"text-gray-400 text-sm",children:"Resistance Electronic Broadcasting Liberation"})]})]})]})]}),(0,r.jsxs)("section",{className:"mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-white mb-8",children:"Our Technology"}),(0,r.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg p-8",children:[(0,r.jsx)("p",{className:"text-gray-300 text-lg leading-relaxed mb-6",children:"Built on modern web technologies with a focus on performance, security, and resistance to censorship. Our platform is designed to be fast, reliable, and accessible to truth-seekers worldwide."}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mt-6",children:[(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-800/50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-cyan-400 font-semibold",children:"Next.js 15"}),(0,r.jsx)("div",{className:"text-gray-400 text-sm",children:"React Framework"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-800/50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-cyan-400 font-semibold",children:"TypeScript"}),(0,r.jsx)("div",{className:"text-gray-400 text-sm",children:"Type Safety"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-800/50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-cyan-400 font-semibold",children:"Tailwind CSS"}),(0,r.jsx)("div",{className:"text-gray-400 text-sm",children:"Styling"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-800/50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-cyan-400 font-semibold",children:"Markdown"}),(0,r.jsx)("div",{className:"text-gray-400 text-sm",children:"Content System"})]})]})]})]}),(0,r.jsxs)("section",{className:"text-center",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-white mb-8",children:"Join the Resistance"}),(0,r.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg p-8",children:[(0,r.jsx)("p",{className:"text-gray-300 text-lg mb-6",children:"Ready to contribute to the digital ReBeLuTioN? We're always looking for truth-seekers, content creators, and digital freedom fighters."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)("span",{className:"flame-badge text-lg px-6 py-3",children:"\uD83D\uDD25 RECRUITMENT ACTIVE"}),(0,r.jsx)("span",{className:"glitch-text text-lg",children:"SECURE CHANNELS OPEN"})]})]})]})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66779:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),d=s(30893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);s.d(t,l);let o={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,28770)),"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/about/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,95968))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,98042)),"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,95968))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/about/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},95968:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[243,512,658,108],()=>s(66779));module.exports=r})();