(()=>{var e={};e.id=116,e.ids=[116],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,r,t)=>{"use strict";var s=t(65773);t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64248:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(60687),a=t(43210),n=t(82136),i=t(16189);function o(){let[e,r]=(0,a.useState)(""),[t,o]=(0,a.useState)(""),[d,l]=(0,a.useState)(""),[c,m]=(0,a.useState)(!1),u=(0,i.useRouter)(),p=async r=>{r.preventDefault(),m(!0),l("");try{let r=await (0,n.signIn)("credentials",{email:e,password:t,redirect:!1});if(r?.error)l("Invalid credentials");else{let e=await (0,n.getSession)();e?.user?.role==="admin"?u.push("/admin/dashboard"):l("Access denied")}}catch(e){l("Login failed")}finally{m(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-black flex items-center justify-center px-4",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("h1",{className:"text-4xl font-bold mb-2",children:[(0,s.jsx)("span",{className:"text-red-500",children:"R3B3L"})," ",(0,s.jsx)("span",{className:"text-cyan-400",children:"M3D14"})]}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"ADMIN ACCESS"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Ghost King Command Center"}),(0,s.jsx)("div",{className:"w-full h-px bg-gradient-to-r from-transparent via-red-500 to-transparent mt-4"})]}),(0,s.jsxs)("form",{onSubmit:p,className:"mt-8 space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300 mb-2",children:"Admin Email"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",required:!0,value:e,onChange:e=>r(e.target.value),className:"w-full px-4 py-3 bg-gray-900 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent",placeholder:"Enter admin email"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-300 mb-2",children:"Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",required:!0,value:t,onChange:e=>o(e.target.value),className:"w-full px-4 py-3 bg-gray-900 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent",placeholder:"Enter password"})]})]}),d&&(0,s.jsx)("div",{className:"bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded-lg",children:d}),(0,s.jsx)("button",{type:"submit",disabled:c,className:"w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-bold py-3 px-4 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:c?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"ACCESSING..."]}):"ACCESS COMMAND CENTER"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"\uD83D\uDD25 FLAME-Licensed Content Management System \uD83D\uDD25"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"For the Empire • For the ReBeLuTioN"})]})]})})}},72184:(e,r,t)=>{Promise.resolve().then(t.bind(t,76158))},76158:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/admin/login/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/admin/login/page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81912:(e,r,t)=>{Promise.resolve().then(t.bind(t,64248))},90979:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>l});var s=t(65239),a=t(48088),n=t(88170),i=t.n(n),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["admin",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,76158)),"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/admin/login/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,95968))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,98042)),"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,95968))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/admin/login/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/login/page",pathname:"/admin/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},95968:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,512,658,108],()=>t(90979));module.exports=s})();