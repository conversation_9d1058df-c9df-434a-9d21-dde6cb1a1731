(()=>{var e={};e.id=957,e.ids=[957],e.modules={309:(e,s,r)=>{Promise.resolve().then(r.bind(r,71031))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13877:(e,s,r)=>{Promise.resolve().then(r.bind(r,19573))},16189:(e,s,r)=>{"use strict";var t=r(65773);r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19573:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(60687),a=r(82136),n=r(16189),l=r(43210),i=r(85814),d=r.n(i);function o(){let{data:e,status:s}=(0,a.useSession)();(0,n.useRouter)();let[r,i]=(0,l.useState)([]),[o,c]=(0,l.useState)(!0);return"loading"===s||o?(0,t.jsx)("div",{className:"min-h-screen bg-black flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-white",children:"Loading Command Center..."})]})}):e&&e.user?.role==="admin"?(0,t.jsxs)("div",{className:"min-h-screen bg-black text-white",children:[(0,t.jsx)("header",{className:"bg-gray-900 border-b border-gray-800",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-2xl font-bold",children:[(0,t.jsx)("span",{className:"text-red-500",children:"R3B3L"})," ",(0,t.jsx)("span",{className:"text-cyan-400",children:"COMMAND CENTER"})]}),(0,t.jsx)("p",{className:"text-gray-400 text-sm",children:"Ghost King's Content Management System"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("span",{className:"text-sm text-gray-400",children:["Welcome, ",e.user?.name]}),(0,t.jsx)("button",{onClick:()=>{(0,a.signOut)({callbackUrl:"/"})},className:"bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:"Sign Out"})]})]})})}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,t.jsx)(d(),{href:"/admin/create-post",className:"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 p-6 rounded-lg transition-all duration-200 group",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"text-3xl mr-4",children:"\uD83D\uDCDD"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-bold",children:"Create New Post"}),(0,t.jsx)("p",{className:"text-red-200 text-sm",children:"Write truth scrolls manually"})]})]})}),(0,t.jsx)(d(),{href:"/admin/ai-generator",className:"bg-gradient-to-r from-cyan-600 to-cyan-700 hover:from-cyan-700 hover:to-cyan-800 p-6 rounded-lg transition-all duration-200 group",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"text-3xl mr-4",children:"\uD83E\uDD16"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-bold",children:"AI Content Generator"}),(0,t.jsx)("p",{className:"text-cyan-200 text-sm",children:"Generate with AI assistance"})]})]})}),(0,t.jsx)(d(),{href:"/admin/media",className:"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 p-6 rounded-lg transition-all duration-200 group",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"text-3xl mr-4",children:"\uD83C\uDFAD"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-bold",children:"Meme Warfare"}),(0,t.jsx)("p",{className:"text-purple-200 text-sm",children:"Upload and manage media"})]})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-8",children:[(0,t.jsxs)("div",{className:"bg-gray-900 p-4 rounded-lg border border-gray-800",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-red-500",children:r.length}),(0,t.jsx)("div",{className:"text-gray-400 text-sm",children:"Total Posts"})]}),(0,t.jsxs)("div",{className:"bg-gray-900 p-4 rounded-lg border border-gray-800",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-cyan-400",children:r.filter(e=>"INTELLIGENCE LEVEL 5"===e.badge).length}),(0,t.jsx)("div",{className:"text-gray-400 text-sm",children:"Intelligence Drops"})]}),(0,t.jsxs)("div",{className:"bg-gray-900 p-4 rounded-lg border border-gray-800",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-400",children:r.filter(e=>"TRUTH SCROLL"===e.badge).length}),(0,t.jsx)("div",{className:"text-gray-400 text-sm",children:"Truth Scrolls"})]}),(0,t.jsxs)("div",{className:"bg-gray-900 p-4 rounded-lg border border-gray-800",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-yellow-400",children:r.filter(e=>"MEME WARFARE"===e.badge).length}),(0,t.jsx)("div",{className:"text-gray-400 text-sm",children:"Meme Operations"})]})]}),(0,t.jsxs)("div",{className:"bg-gray-900 rounded-lg border border-gray-800",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-800",children:(0,t.jsx)("h2",{className:"text-xl font-bold",children:"Recent Intelligence Drops"})}),(0,t.jsx)("div",{className:"p-6",children:0===r.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDCE1"}),(0,t.jsx)("p",{className:"text-gray-400",children:"No posts yet. Start creating content for the resistance!"})]}):(0,t.jsx)("div",{className:"space-y-4",children:r.slice(0,5).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-800 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"font-semibold text-white",children:e.title}),(0,t.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:e.summary}),(0,t.jsxs)("div",{className:"flex items-center mt-2 space-x-4",children:[(0,t.jsx)("span",{className:"text-xs bg-red-600 px-2 py-1 rounded",children:e.badge}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:e.date}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["by ",e.author]})]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(d(),{href:`/articles/${e.slug}`,className:"text-cyan-400 hover:text-cyan-300 text-sm",children:"View"}),(0,t.jsx)(d(),{href:`/admin/edit/${e.slug}`,className:"text-yellow-400 hover:text-yellow-300 text-sm",children:"Edit"})]})]},e.slug))})})]})]})]}):null}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38885:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var t=r(65239),a=r(48088),n=r(88170),l=r.n(n),i=r(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(s,d);let o={children:["",{children:["admin",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,71031)),"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/admin/dashboard/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,95968))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,98042)),"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,95968))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/admin/dashboard/page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/dashboard/page",pathname:"/admin/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71031:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/admin/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/admin/dashboard/page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},95968:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[243,512,658,108],()=>r(38885));module.exports=t})();