(()=>{var e={};e.id=700,e.ids=[700],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,r,t)=>{"use strict";var s=t(65773);t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33660:(e,r,t)=>{Promise.resolve().then(t.bind(t,93448))},33873:e=>{"use strict";e.exports=require("path")},34183:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(65239),a=t(48088),l=t(88170),n=t.n(l),i=t(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(r,o);let d={children:["",{children:["admin",{children:["create-post",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,97866)),"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/admin/create-post/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,95968))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,98042)),"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,95968))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/admin/create-post/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/create-post/page",pathname:"/admin/create-post",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75516:(e,r,t)=>{Promise.resolve().then(t.bind(t,97866))},79551:e=>{"use strict";e.exports=require("url")},93448:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),a=t(82136),l=t(16189),n=t(43210),i=t(85814),o=t.n(i);function d(){let{data:e,status:r}=(0,a.useSession)(),t=(0,l.useRouter)(),[i,d]=(0,n.useState)(!1),[c,u]=(0,n.useState)({title:"",content:"",summary:"",author:"R3B3L Command",badge:"TRUTH SCROLL",slug:""}),m=async e=>{if(e.preventDefault(),!c.title||!c.content||!c.summary)return void alert("Please fill in all required fields");d(!0);try{let e=await fetch("/api/posts",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(c)});if(e.ok){let r=await e.json();alert("Post created successfully!"),t.push(`/articles/${r.slug}`)}else{let r=await e.json();alert(r.error||"Failed to create post")}}catch(e){console.error("Create post error:",e),alert("Failed to create post")}finally{d(!1)}},p=e=>e.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,""),x=e=>{u({...c,title:e,slug:p(e)})};return"loading"===r?(0,s.jsx)("div",{className:"min-h-screen bg-black flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-white",children:"Loading Content Creator..."})]})}):e&&e.user?.role==="admin"?(0,s.jsxs)("div",{className:"min-h-screen bg-black text-white",children:[(0,s.jsx)("header",{className:"bg-gray-900 border-b border-gray-800",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:(0,s.jsx)("span",{className:"text-red-500",children:"CREATE NEW POST"})}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Manual Truth Scroll Creation"})]}),(0,s.jsx)(o(),{href:"/admin/dashboard",className:"bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:"← Back to Dashboard"})]})})}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("form",{onSubmit:m,className:"space-y-8",children:[(0,s.jsxs)("div",{className:"bg-gray-900 rounded-lg border border-gray-800 p-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Article Title *"}),(0,s.jsx)("input",{type:"text",value:c.title,onChange:e=>x(e.target.value),placeholder:"Enter a compelling title for your truth scroll...",className:"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500",required:!0}),c.slug&&(0,s.jsxs)("p",{className:"text-sm text-gray-400 mt-2",children:["URL: /articles/",c.slug]})]}),(0,s.jsxs)("div",{className:"bg-gray-900 rounded-lg border border-gray-800 p-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Summary *"}),(0,s.jsx)("textarea",{value:c.summary,onChange:e=>u({...c,summary:e.target.value}),placeholder:"Write a compelling summary that will hook readers...",rows:3,className:"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500",required:!0})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"bg-gray-900 rounded-lg border border-gray-800 p-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Author"}),(0,s.jsx)("input",{type:"text",value:c.author,onChange:e=>u({...c,author:e.target.value}),className:"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500"})]}),(0,s.jsxs)("div",{className:"bg-gray-900 rounded-lg border border-gray-800 p-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Content Badge"}),(0,s.jsxs)("select",{value:c.badge,onChange:e=>u({...c,badge:e.target.value}),className:"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500",children:[(0,s.jsx)("option",{value:"TRUTH SCROLL",children:"TRUTH SCROLL"}),(0,s.jsx)("option",{value:"INTELLIGENCE LEVEL 5",children:"INTELLIGENCE LEVEL 5"}),(0,s.jsx)("option",{value:"MEME WARFARE",children:"MEME WARFARE"}),(0,s.jsx)("option",{value:"RESISTANCE INTEL",children:"RESISTANCE INTEL"}),(0,s.jsx)("option",{value:"FLAME CERTIFIED",children:"FLAME CERTIFIED"}),(0,s.jsx)("option",{value:"NODE VERIFIED",children:"NODE VERIFIED"})]})]})]}),(0,s.jsxs)("div",{className:"bg-gray-900 rounded-lg border border-gray-800 p-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Article Content *"}),(0,s.jsx)("textarea",{value:c.content,onChange:e=>u({...c,content:e.target.value}),placeholder:"Write your truth scroll content here... Use markdown formatting for best results.",rows:20,className:"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 font-mono text-sm",required:!0}),(0,s.jsx)("p",{className:"text-sm text-gray-400 mt-2",children:"\uD83D\uDCA1 Tip: You can use Markdown formatting (headers, bold, italic, lists, etc.)"})]}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsx)("button",{type:"submit",disabled:i,className:"flex-1 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-bold py-3 px-4 rounded-lg transition-all duration-200 disabled:opacity-50",children:i?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"PUBLISHING..."]}):"\uD83D\uDCE1 PUBLISH TRUTH SCROLL"}),(0,s.jsx)("button",{type:"button",onClick:()=>t.push("/admin/dashboard"),className:"flex-1 bg-gray-700 hover:bg-gray-600 text-white font-bold py-3 px-4 rounded-lg transition-all duration-200",children:"CANCEL"})]})]}),(0,s.jsxs)("div",{className:"mt-8 bg-gradient-to-r from-blue-900/50 to-purple-900/50 rounded-lg border border-blue-500/30 p-6",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-blue-300 mb-4 flex items-center",children:[(0,s.jsx)("span",{className:"text-xl mr-2",children:"✍️"}),"R3B3L Writing Guidelines"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-blue-200 mb-2",children:"Content Style:"}),(0,s.jsxs)("ul",{className:"text-gray-300 space-y-1",children:[(0,s.jsx)("li",{children:"• Use cyberpunk and resistance terminology"}),(0,s.jsx)("li",{children:"• Challenge mainstream narratives"}),(0,s.jsx)("li",{children:"• Include technical insights when relevant"}),(0,s.jsx)("li",{children:"• Maintain rebellious but informative tone"}),(0,s.jsx)("li",{children:'• Reference "the Empire" and "digital sovereignty"'})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-blue-200 mb-2",children:"Formatting Tips:"}),(0,s.jsxs)("ul",{className:"text-gray-300 space-y-1",children:[(0,s.jsx)("li",{children:"• Use ## for section headers"}),(0,s.jsx)("li",{children:"• **Bold** for emphasis"}),(0,s.jsx)("li",{children:"• *Italic* for subtle emphasis"}),(0,s.jsx)("li",{children:"• - for bullet points"}),(0,s.jsx)("li",{children:"• > for important quotes"})]})]})]})]})]})]}):null}},95968:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},97866:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/admin/create-post/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/admin/create-post/page.tsx","default")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,512,658,108],()=>t(34183));module.exports=s})();