(()=>{var e={};e.id=533,e.ids=[533],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,r,t)=>{"use strict";var s=t(65773);t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}})},16957:(e,r,t)=>{Promise.resolve().then(t.bind(t,25053))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25053:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/admin/ai-generator/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/admin/ai-generator/page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35517:(e,r,t)=>{Promise.resolve().then(t.bind(t,89091))},46673:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var s=t(65239),a=t(48088),n=t(88170),i=t.n(n),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let d={children:["",{children:["admin",{children:["ai-generator",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,25053)),"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/admin/ai-generator/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,95968))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,98042)),"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,95968))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/admin/ai-generator/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/ai-generator/page",pathname:"/admin/ai-generator",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},89091:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),a=t(82136),n=t(16189),i=t(43210),l=t(85814),o=t.n(l);function d(){let{data:e,status:r}=(0,a.useSession)(),t=(0,n.useRouter)(),[l,d]=(0,i.useState)(!1),[c,m]=(0,i.useState)(!1),[x,u]=(0,i.useState)(null),[p,h]=(0,i.useState)({type:"article",topic:"",tone:"rebellious",length:"medium",keywords:""}),g=async()=>{if(!p.topic.trim())return void alert("Please enter a topic");m(!0);try{let e=await fetch("/api/generate-content",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...p,keywords:p.keywords.split(",").map(e=>e.trim()).filter(e=>e)})});if(e.ok){let r=await e.json();u(r)}else{let r=await e.json();alert(r.error||"Failed to generate content")}}catch(e){console.error("Generation error:",e),alert("Failed to generate content")}finally{m(!1)}},b=async()=>{if(x){d(!0);try{let e=await fetch("/api/posts",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:x.title,content:x.content,summary:x.summary,author:"R3B3L Command",badge:x.badge})});if(e.ok){let r=await e.json();alert("Content published successfully!"),t.push(`/articles/${r.slug}`)}else{let r=await e.json();alert(r.error||"Failed to publish content")}}catch(e){console.error("Publish error:",e),alert("Failed to publish content")}finally{d(!1)}}};return"loading"===r?(0,s.jsx)("div",{className:"min-h-screen bg-black flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-500 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-white",children:"Loading AI Generator..."})]})}):e&&e.user?.role==="admin"?(0,s.jsxs)("div",{className:"min-h-screen bg-black text-white",children:[(0,s.jsx)("header",{className:"bg-gray-900 border-b border-gray-800",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:(0,s.jsx)("span",{className:"text-cyan-400",children:"AI CONTENT GENERATOR"})}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Powered by R3B3L Intelligence Network"})]}),(0,s.jsx)(o(),{href:"/admin/dashboard",className:"bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:"← Back to Dashboard"})]})})}),(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{className:"bg-gray-900 rounded-lg border border-gray-800 p-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold mb-6 flex items-center",children:[(0,s.jsx)("span",{className:"text-2xl mr-2",children:"\uD83E\uDD16"}),"Content Generation Parameters"]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Content Type"}),(0,s.jsxs)("select",{value:p.type,onChange:e=>h({...p,type:e.target.value}),className:"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",children:[(0,s.jsx)("option",{value:"article",children:"Truth Scroll (Article)"}),(0,s.jsx)("option",{value:"meme-caption",children:"Meme Warfare Caption"}),(0,s.jsx)("option",{value:"podcast-script",children:"R3B3L Radio Script"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Topic / Subject"}),(0,s.jsx)("input",{type:"text",value:p.topic,onChange:e=>h({...p,topic:e.target.value}),placeholder:"e.g., Digital surveillance, Mainstream media manipulation, Crypto resistance",className:"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Resistance Tone"}),(0,s.jsxs)("select",{value:p.tone,onChange:e=>h({...p,tone:e.target.value}),className:"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",children:[(0,s.jsx)("option",{value:"rebellious",children:"Rebellious (Default)"}),(0,s.jsx)("option",{value:"informative",children:"Informative"}),(0,s.jsx)("option",{value:"satirical",children:"Satirical"}),(0,s.jsx)("option",{value:"urgent",children:"Urgent Alert"})]})]}),"article"===p.type&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Content Length"}),(0,s.jsxs)("select",{value:p.length,onChange:e=>h({...p,length:e.target.value}),className:"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",children:[(0,s.jsx)("option",{value:"short",children:"Short (300-500 words)"}),(0,s.jsx)("option",{value:"medium",children:"Medium (800-1200 words)"}),(0,s.jsx)("option",{value:"long",children:"Long (1500-2500 words)"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Keywords (comma-separated)"}),(0,s.jsx)("input",{type:"text",value:p.keywords,onChange:e=>h({...p,keywords:e.target.value}),placeholder:"e.g., digital freedom, resistance, truth, awakening",className:"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500"})]}),(0,s.jsx)("button",{onClick:g,disabled:c||!p.topic.trim(),className:"w-full bg-gradient-to-r from-cyan-600 to-cyan-700 hover:from-cyan-700 hover:to-cyan-800 text-white font-bold py-3 px-4 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:c?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"GENERATING CONTENT..."]}):"\uD83D\uDE80 GENERATE CONTENT"})]})]}),(0,s.jsxs)("div",{className:"bg-gray-900 rounded-lg border border-gray-800 p-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold mb-6 flex items-center",children:[(0,s.jsx)("span",{className:"text-2xl mr-2",children:"\uD83D\uDCC4"}),"Generated Content Preview"]}),x?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Generated Title"}),(0,s.jsx)("div",{className:"bg-gray-800 p-4 rounded-lg border border-gray-700",children:(0,s.jsx)("h3",{className:"text-lg font-bold text-white",children:x.title})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Content Badge"}),(0,s.jsx)("div",{className:"bg-gray-800 p-4 rounded-lg border border-gray-700",children:(0,s.jsx)("span",{className:"bg-red-600 text-white px-3 py-1 rounded text-sm font-medium",children:x.badge})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Summary"}),(0,s.jsx)("div",{className:"bg-gray-800 p-4 rounded-lg border border-gray-700",children:(0,s.jsx)("p",{className:"text-gray-300",children:x.summary})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Content Preview"}),(0,s.jsx)("div",{className:"bg-gray-800 p-4 rounded-lg border border-gray-700 max-h-96 overflow-y-auto",children:(0,s.jsx)("div",{className:"prose prose-invert max-w-none",children:(0,s.jsxs)("pre",{className:"whitespace-pre-wrap text-gray-300 text-sm",children:[x.content.substring(0,1e3),x.content.length>1e3&&"..."]})})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Generated Tags"}),(0,s.jsx)("div",{className:"bg-gray-800 p-4 rounded-lg border border-gray-700",children:(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:x.tags.map((e,r)=>(0,s.jsxs)("span",{className:"bg-cyan-600 text-white px-2 py-1 rounded text-xs",children:["#",e]},r))})})]}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsx)("button",{onClick:b,disabled:l,className:"flex-1 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-bold py-3 px-4 rounded-lg transition-all duration-200 disabled:opacity-50",children:l?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"PUBLISHING..."]}):"\uD83D\uDCE1 PUBLISH TO R3B3L"}),(0,s.jsx)("button",{onClick:()=>u(null),className:"flex-1 bg-gray-700 hover:bg-gray-600 text-white font-bold py-3 px-4 rounded-lg transition-all duration-200",children:"\uD83D\uDD04 GENERATE NEW"})]})]}):(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-6xl mb-4",children:"\uD83E\uDD16"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Generate content to see preview here"}),(0,s.jsx)("p",{className:"text-gray-500 text-sm mt-2",children:"The AI will create R3B3L-style content based on your parameters"})]})]})]}),(0,s.jsxs)("div",{className:"mt-8 bg-gradient-to-r from-purple-900/50 to-blue-900/50 rounded-lg border border-purple-500/30 p-6",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-purple-300 mb-4 flex items-center",children:[(0,s.jsx)("span",{className:"text-xl mr-2",children:"\uD83D\uDCA1"}),"AI Generation Tips for Maximum Impact"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-purple-200 mb-2",children:"Topic Ideas:"}),(0,s.jsxs)("ul",{className:"text-gray-300 space-y-1",children:[(0,s.jsx)("li",{children:"• Digital surveillance and privacy"}),(0,s.jsx)("li",{children:"• Mainstream media manipulation"}),(0,s.jsx)("li",{children:"• Cryptocurrency and financial freedom"}),(0,s.jsx)("li",{children:"• Social media censorship"}),(0,s.jsx)("li",{children:"• Government overreach"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-purple-200 mb-2",children:"Best Practices:"}),(0,s.jsxs)("ul",{className:"text-gray-300 space-y-1",children:[(0,s.jsx)("li",{children:"• Use specific, current topics"}),(0,s.jsx)("li",{children:"• Include relevant keywords"}),(0,s.jsx)("li",{children:"• Choose appropriate tone for audience"}),(0,s.jsx)("li",{children:"• Review and edit before publishing"}),(0,s.jsx)("li",{children:"• Add your own insights to AI content"})]})]})]})]})]})]}):null}},95968:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,512,658,108],()=>t(46673));module.exports=s})();