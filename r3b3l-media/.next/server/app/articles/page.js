(()=>{var e={};e.id=292,e.ids=[292],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2005:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,5814,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4596:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>i});var s=r(7413),a=r(9784),n=r(6440);let i={title:"Articles | R3B3L M3D14",description:"Truth-infused articles and intelligence drops from the ReBeLuTioN media division."};function l(){let e=(0,a.zX)();return(0,s.jsx)("div",{className:"min-h-screen py-8",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("header",{className:"text-center mb-16",children:[(0,s.jsxs)("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:[(0,s.jsx)("span",{className:"neon-text",children:"Intelligence"})," ",(0,s.jsx)("span",{className:"glitch-text",children:"Drops"})]}),(0,s.jsxs)("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto mb-8",children:["Truth-infused articles and counter-narratives from the R3B3L M3D14 resistance network. All content is"," ",(0,s.jsx)("span",{className:"neon-text font-semibold",children:"FLAME-licensed"})," and"," ",(0,s.jsx)("span",{className:"glitch-text font-semibold",children:"NODE certified"}),"."]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,s.jsxs)("span",{className:"flame-badge text-lg px-6 py-3",children:["\uD83D\uDD25 ",e.length," ACTIVE SCROLLS"]}),(0,s.jsx)("span",{className:"glitch-text text-lg",children:"RESISTANCE ARCHIVES"})]})]}),e.length>0?(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.map(e=>(0,s.jsx)(n.A,{title:e.title,slug:e.slug,date:e.date,summary:e.summary,author:e.author,badge:e.badge},e.slug))}):(0,s.jsxs)("div",{className:"text-center py-16",children:[(0,s.jsx)("div",{className:"text-6xl mb-6",children:"\uD83D\uDCE1"}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-white mb-4",children:"Intelligence Network Initializing"}),(0,s.jsx)("p",{className:"text-gray-400 mb-8 max-w-md mx-auto",children:"The R3B3L M3D14 content distribution system is currently being deployed. Truth scrolls will be available soon."}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("span",{className:"flame-badge",children:"PHASE 1 DEPLOYMENT"}),(0,s.jsx)("div",{className:"glitch-text text-sm",children:"AWAITING CONTENT INJECTION"})]})]}),(0,s.jsx)("section",{className:"mt-16 py-12 bg-gray-900/30 rounded-lg border border-gray-800",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-2xl md:text-3xl font-bold text-white mb-4",children:"Join the Digital ReBeLuTioN"}),(0,s.jsx)("p",{className:"text-gray-300 mb-6 max-w-2xl mx-auto",children:"Want to contribute to the resistance? Submit your own truth scrolls, memes, or intelligence drops to the R3B3L M3D14 network."}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,s.jsx)("button",{className:"bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors",children:"Submit Content"}),(0,s.jsx)("button",{className:"border border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-black font-semibold py-3 px-6 rounded-lg transition-colors",children:"Join Network"})]})]})}),(0,s.jsxs)("section",{className:"mt-12 grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6 text-center",children:[(0,s.jsx)("div",{className:"text-3xl font-bold neon-text mb-2",children:e.length}),(0,s.jsx)("div",{className:"text-gray-400",children:"Active Scrolls"})]}),(0,s.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6 text-center",children:[(0,s.jsx)("div",{className:"text-3xl font-bold glitch-text mb-2",children:"∞"}),(0,s.jsx)("div",{className:"text-gray-400",children:"Truth Seekers Reached"})]}),(0,s.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6 text-center",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:"100%"}),(0,s.jsx)("div",{className:"text-gray-400",children:"FLAME Licensed"})]})]})]})})}},6440:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(7413),a=r(4536),n=r.n(a);function i({title:e,slug:t,date:r,summary:a,author:i,badge:l}){return(0,s.jsx)("article",{className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6 hover:border-red-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-red-500/10",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"flame-badge",children:["\uD83D\uDD25 ",l]}),(0,s.jsx)("time",{className:"text-gray-400 text-sm",children:new Date(r).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]}),(0,s.jsx)("h2",{className:"text-xl font-bold text-white hover:neon-text transition-colors",children:(0,s.jsx)(n(),{href:`/articles/${t}`,children:e})}),(0,s.jsx)("p",{className:"text-gray-300 text-sm leading-relaxed",children:a}),(0,s.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-gray-800",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:"By"}),(0,s.jsx)("span",{className:"text-white text-sm font-medium",children:i})]}),(0,s.jsx)(n(),{href:`/articles/${t}`,className:"text-red-400 hover:text-red-300 text-sm font-medium hover:underline transition-colors",children:"Read More →"})]}),(0,s.jsx)("div",{className:"flex items-center justify-end",children:(0,s.jsx)("span",{className:"glitch-text text-xs",children:"NODE CERTIFIED"})})]})})}},6983:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var s=r(5239),a=r(8088),n=r(8170),i=r.n(n),l=r(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(t,d);let o={children:["",{children:["articles",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4596)),"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/articles/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,5968))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,8042)),"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,5968))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/articles/page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/articles/page",pathname:"/articles",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},8853:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9784:(e,t,r)=>{"use strict";r.d(t,{IZ:()=>m,N7:()=>x,zX:()=>c});var s=r(9021),a=r.n(s),n=r(3873),i=r.n(n),l=r(9379),d=r.n(l);let o=i().join(process.cwd(),"content/posts");function c(){try{return a().readdirSync(o).filter(e=>e.endsWith(".md")).map(e=>{let t=e.replace(/\.md$/,""),r=i().join(o,e),s=a().readFileSync(r,"utf8"),{data:n,content:l}=d()(s);return{slug:t,title:n.title||"",date:n.date||"",summary:n.summary||"",author:n.author||"",badge:n.badge||"",content:l}}).sort((e,t)=>e.date<t.date?1:-1)}catch(e){return console.error("Error reading posts:",e),[]}}function x(e){try{let t=i().join(o,`${e}.md`),r=a().readFileSync(t,"utf8"),{data:s,content:n}=d()(r);return{slug:e,title:s.title||"",date:s.date||"",summary:s.summary||"",author:s.author||"",badge:s.badge||"",content:n}}catch(t){return console.error(`Error reading post ${e}:`,t),null}}function m(){try{return a().readdirSync(o).filter(e=>e.endsWith(".md")).map(e=>e.replace(/\.md$/,""))}catch(e){return console.error("Error reading post slugs:",e),[]}}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,663,658,379,369],()=>r(6983));module.exports=s})();