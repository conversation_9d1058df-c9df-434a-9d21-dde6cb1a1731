(()=>{var e={};e.id=904,e.ids=[904],e.modules={163:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return r}});let r=n(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23747:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>d,tree:()=>u});var r=n(65239),l=n(48088),i=n(88170),o=n.n(i),a=n(30893),s={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>a[e]);n.d(t,s);let u={children:["",{children:["articles",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,68703)),"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/articles/[slug]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,95968))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,98042)),"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,95968))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/articles/[slug]/page.tsx"],p={require:n,loadChunk:()=>Promise.resolve()},d=new r.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/articles/[slug]/page",pathname:"/articles/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},26556:e=>{"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,l=Object.getOwnPropertyDescriptor,i=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},o=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,l=t.call(e,"constructor"),i=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!l&&!i)return!1;for(r in e);return void 0===r||t.call(e,r)},a=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},s=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;else if(l)return l(e,n).value}return e[n]};e.exports=function e(){var t,n,r,l,u,c,p=arguments[0],d=1,f=arguments.length,h=!1;for("boolean"==typeof p&&(h=p,p=arguments[1]||{},d=2),(null==p||"object"!=typeof p&&"function"!=typeof p)&&(p={});d<f;++d)if(t=arguments[d],null!=t)for(n in t)r=s(p,n),p!==(l=s(t,n))&&(h&&l&&(o(l)||(u=i(l)))?(u?(u=!1,c=r&&i(r)?r:[]):c=r&&o(r)?r:{},a(p,{name:n,newValue:e(h,c,l)})):void 0!==l&&a(p,{name:n,newValue:l}));return p}},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32558:function(e,t,n){"use strict";var r=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(94934)),l=n(53162);function i(e,t){var n={};return e&&"string"==typeof e&&(0,r.default)(e,function(e,r){e&&r&&(n[(0,l.camelCase)(e,t)]=r)}),n}i.default=i,e.exports=i},33873:e=>{"use strict";e.exports=require("path")},48976:(e,t,n)=>{"use strict";function r(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return r}}),n(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},49784:(e,t,n)=>{"use strict";n.d(t,{IZ:()=>d,N7:()=>p,zX:()=>c});var r=n(29021),l=n.n(r),i=n(33873),o=n.n(i),a=n(99379),s=n.n(a);let u=o().join(process.cwd(),"content/posts");function c(){try{return l().readdirSync(u).filter(e=>e.endsWith(".md")).map(e=>{let t=e.replace(/\.md$/,""),n=o().join(u,e),r=l().readFileSync(n,"utf8"),{data:i,content:a}=s()(r);return{slug:t,title:i.title||"",date:i.date||"",summary:i.summary||"",author:i.author||"",badge:i.badge||"",content:a}}).sort((e,t)=>e.date<t.date?1:-1)}catch(e){return console.error("Error reading posts:",e),[]}}function p(e){try{let t=o().join(u,`${e}.md`),n=l().readFileSync(t,"utf8"),{data:r,content:i}=s()(n);return{slug:e,title:r.title||"",date:r.date||"",summary:r.summary||"",author:r.author||"",badge:r.badge||"",content:i}}catch(t){return console.error(`Error reading post ${e}:`,t),null}}function d(){try{return l().readdirSync(u).filter(e=>e.endsWith(".md")).map(e=>e.replace(/\.md$/,""))}catch(e){return console.error("Error reading post slugs:",e),[]}}},53162:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var n=/^--[a-zA-Z0-9_-]+$/,r=/-([a-z])/g,l=/^[^-]+$/,i=/^-(webkit|moz|ms|o|khtml)-/,o=/^-(ms)-/,a=function(e,t){return t.toUpperCase()},s=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){var u;return(void 0===t&&(t={}),!(u=e)||l.test(u)||n.test(u))?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(o,s):e.replace(i,s)).replace(r,a))}},62765:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return l}});let r=""+n(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function l(){let e=Object.defineProperty(Error(r),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=r,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68703:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>nj,generateMetadata:()=>n_,generateStaticParams:()=>nO});var r={};n.r(r),n.d(r,{boolean:()=>x,booleanish:()=>v,commaOrSpaceSeparated:()=>T,commaSeparated:()=>E,number:()=>w,overloadedBoolean:()=>k,spaceSeparated:()=>S});var l={};n.r(l),n.d(l,{attentionMarkers:()=>tP,contentInitial:()=>tE,disable:()=>tA,document:()=>tS,flow:()=>tC,flowInitial:()=>tT,insideSpan:()=>tL,string:()=>tD,text:()=>tq});var i=n(37413),o=n(97576),a=n(49784);let s=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,u=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,c={};function p(e,t){return((t||c).jsx?u:s).test(e)}let d=/[ \t\n\f\r]/g;function f(e){return""===e.replace(d,"")}class h{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function m(e,t){let n={},r={};for(let t of e)Object.assign(n,t.property),Object.assign(r,t.normal);return new h(n,r,t)}function g(e){return e.toLowerCase()}h.prototype.normal={},h.prototype.property={},h.prototype.space=void 0;class y{constructor(e,t){this.attribute=t,this.property=e}}y.prototype.attribute="",y.prototype.booleanish=!1,y.prototype.boolean=!1,y.prototype.commaOrSpaceSeparated=!1,y.prototype.commaSeparated=!1,y.prototype.defined=!1,y.prototype.mustUseProperty=!1,y.prototype.number=!1,y.prototype.overloadedBoolean=!1,y.prototype.property="",y.prototype.spaceSeparated=!1,y.prototype.space=void 0;let b=0,x=C(),v=C(),k=C(),w=C(),S=C(),E=C(),T=C();function C(){return 2**++b}let D=Object.keys(r);class q extends y{constructor(e,t,n,l){let i=-1;if(super(e,t),function(e,t,n){n&&(e[t]=n)}(this,"space",l),"number"==typeof n)for(;++i<D.length;){let e=D[i];!function(e,t,n){n&&(e[t]=n)}(this,D[i],(n&r[e])===r[e])}}}function L(e){let t={},n={};for(let[r,l]of Object.entries(e.properties)){let i=new q(r,e.transform(e.attributes||{},r),l,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(i.mustUseProperty=!0),t[r]=i,n[g(r)]=r,n[g(i.attribute)]=r}return new h(t,n,e.space)}q.prototype.defined=!0;let P=L({properties:{ariaActiveDescendant:null,ariaAtomic:v,ariaAutoComplete:null,ariaBusy:v,ariaChecked:v,ariaColCount:w,ariaColIndex:w,ariaColSpan:w,ariaControls:S,ariaCurrent:null,ariaDescribedBy:S,ariaDetails:null,ariaDisabled:v,ariaDropEffect:S,ariaErrorMessage:null,ariaExpanded:v,ariaFlowTo:S,ariaGrabbed:v,ariaHasPopup:null,ariaHidden:v,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:S,ariaLevel:w,ariaLive:null,ariaModal:v,ariaMultiLine:v,ariaMultiSelectable:v,ariaOrientation:null,ariaOwns:S,ariaPlaceholder:null,ariaPosInSet:w,ariaPressed:v,ariaReadOnly:v,ariaRelevant:null,ariaRequired:v,ariaRoleDescription:S,ariaRowCount:w,ariaRowIndex:w,ariaRowSpan:w,ariaSelected:v,ariaSetSize:w,ariaSort:null,ariaValueMax:w,ariaValueMin:w,ariaValueNow:w,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function A(e,t){return t in e?e[t]:t}function N(e,t){return A(e,t.toLowerCase())}let I=L({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:E,acceptCharset:S,accessKey:S,action:null,allow:null,allowFullScreen:x,allowPaymentRequest:x,allowUserMedia:x,alt:null,as:null,async:x,autoCapitalize:null,autoComplete:S,autoFocus:x,autoPlay:x,blocking:S,capture:null,charSet:null,checked:x,cite:null,className:S,cols:w,colSpan:null,content:null,contentEditable:v,controls:x,controlsList:S,coords:w|E,crossOrigin:null,data:null,dateTime:null,decoding:null,default:x,defer:x,dir:null,dirName:null,disabled:x,download:k,draggable:v,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:x,formTarget:null,headers:S,height:w,hidden:k,high:w,href:null,hrefLang:null,htmlFor:S,httpEquiv:S,id:null,imageSizes:null,imageSrcSet:null,inert:x,inputMode:null,integrity:null,is:null,isMap:x,itemId:null,itemProp:S,itemRef:S,itemScope:x,itemType:S,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:x,low:w,manifest:null,max:null,maxLength:w,media:null,method:null,min:null,minLength:w,multiple:x,muted:x,name:null,nonce:null,noModule:x,noValidate:x,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:x,optimum:w,pattern:null,ping:S,placeholder:null,playsInline:x,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:x,referrerPolicy:null,rel:S,required:x,reversed:x,rows:w,rowSpan:w,sandbox:S,scope:null,scoped:x,seamless:x,selected:x,shadowRootClonable:x,shadowRootDelegatesFocus:x,shadowRootMode:null,shape:null,size:w,sizes:null,slot:null,span:w,spellCheck:v,src:null,srcDoc:null,srcLang:null,srcSet:null,start:w,step:null,style:null,tabIndex:w,target:null,title:null,translate:null,type:null,typeMustMatch:x,useMap:null,value:v,width:w,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:S,axis:null,background:null,bgColor:null,border:w,borderColor:null,bottomMargin:w,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:x,declare:x,event:null,face:null,frame:null,frameBorder:null,hSpace:w,leftMargin:w,link:null,longDesc:null,lowSrc:null,marginHeight:w,marginWidth:w,noResize:x,noHref:x,noShade:x,noWrap:x,object:null,profile:null,prompt:null,rev:null,rightMargin:w,rules:null,scheme:null,scrolling:v,standby:null,summary:null,text:null,topMargin:w,valueType:null,version:null,vAlign:null,vLink:null,vSpace:w,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:x,disableRemotePlayback:x,prefix:null,property:null,results:w,security:null,unselectable:null},space:"html",transform:N}),R=L({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:T,accentHeight:w,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:w,amplitude:w,arabicForm:null,ascent:w,attributeName:null,attributeType:null,azimuth:w,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:w,by:null,calcMode:null,capHeight:w,className:S,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:w,diffuseConstant:w,direction:null,display:null,dur:null,divisor:w,dominantBaseline:null,download:x,dx:null,dy:null,edgeMode:null,editable:null,elevation:w,enableBackground:null,end:null,event:null,exponent:w,externalResourcesRequired:null,fill:null,fillOpacity:w,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:E,g2:E,glyphName:E,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:w,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:w,horizOriginX:w,horizOriginY:w,id:null,ideographic:w,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:w,k:w,k1:w,k2:w,k3:w,k4:w,kernelMatrix:T,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:w,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:w,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:w,overlineThickness:w,paintOrder:null,panose1:null,path:null,pathLength:w,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:S,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:w,pointsAtY:w,pointsAtZ:w,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:T,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:T,rev:T,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:T,requiredFeatures:T,requiredFonts:T,requiredFormats:T,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:w,specularExponent:w,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:w,strikethroughThickness:w,string:null,stroke:null,strokeDashArray:T,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:w,strokeOpacity:w,strokeWidth:null,style:null,surfaceScale:w,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:T,tabIndex:w,tableValues:null,target:null,targetX:w,targetY:w,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:T,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:w,underlineThickness:w,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:w,values:null,vAlphabetic:w,vMathematical:w,vectorEffect:null,vHanging:w,vIdeographic:w,version:null,vertAdvY:w,vertOriginX:w,vertOriginY:w,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:w,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:A}),O=L({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),_=L({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:N}),j=L({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),M=m([P,I,O,_,j],"html"),z=m([P,R,O,_,j],"svg"),F=/[A-Z]/g,B=/-[a-z]/g,U=/^data[-\w.:]+$/i;function V(e){return"-"+e.toLowerCase()}function H(e){return e.charAt(1).toUpperCase()}let G={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var Y=n(32558);let K=X("end"),W=X("start");function X(e){return function(t){let n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function Q(e){return e&&"object"==typeof e?"position"in e||"type"in e?Z(e.position):"start"in e||"end"in e?Z(e):"line"in e||"column"in e?J(e):"":""}function J(e){return $(e&&e.line)+":"+$(e&&e.column)}function Z(e){return J(e&&e.start)+"-"+J(e&&e.end)}function $(e){return e&&"number"==typeof e?e:1}class ee extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",l={},i=!1;if(t&&(l="line"in t&&"column"in t||"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!l.cause&&e&&(i=!0,r=e.message,l.cause=e),!l.ruleId&&!l.source&&"string"==typeof n){let e=n.indexOf(":");-1===e?l.ruleId=n:(l.source=n.slice(0,e),l.ruleId=n.slice(e+1))}if(!l.place&&l.ancestors&&l.ancestors){let e=l.ancestors[l.ancestors.length-1];e&&(l.place=e.position)}let o=l.place&&"start"in l.place?l.place.start:l.place;this.ancestors=l.ancestors||void 0,this.cause=l.cause||void 0,this.column=o?o.column:void 0,this.fatal=void 0,this.file,this.message=r,this.line=o?o.line:void 0,this.name=Q(l.place)||"1:1",this.place=l.place||void 0,this.reason=this.message,this.ruleId=l.ruleId||void 0,this.source=l.source||void 0,this.stack=i&&l.cause&&"string"==typeof l.cause.stack?l.cause.stack:"",this.actual,this.expected,this.note,this.url}}ee.prototype.file="",ee.prototype.name="",ee.prototype.reason="",ee.prototype.message="",ee.prototype.stack="",ee.prototype.column=void 0,ee.prototype.line=void 0,ee.prototype.ancestors=void 0,ee.prototype.cause=void 0,ee.prototype.fatal=void 0,ee.prototype.place=void 0,ee.prototype.ruleId=void 0,ee.prototype.source=void 0;let et={}.hasOwnProperty,en=new Map,er=/[A-Z]/g,el=new Set(["table","tbody","thead","tfoot","tr"]),ei=new Set(["td","th"]),eo="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function ea(e,t,n){var r;return"element"===t.type?function(e,t,n){let r=e.schema,l=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(e.schema=z),e.ancestors.push(t);let i=ep(e,t.tagName,!1),o=function(e,t){let n,r,l={};for(r in t.properties)if("children"!==r&&et.call(t.properties,r)){let i=function(e,t,n){let r=function(e,t){let n=g(t),r=t,l=y;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&U.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(B,H);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!B.test(e)){let n=e.replace(F,V);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}l=q}return new l(r,t)}(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?function(e,t){let n={};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}(n):n.join(" ").trim()),"style"===r.property){let t="object"==typeof n?n:function(e,t){try{return Y(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};let t=new ee("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:n,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw t.file=e.filePath||void 0,t.url=eo+"#cannot-parse-style-attribute",t}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){let t,n={};for(t in e)et.call(e,t)&&(n[function(e){let t=e.replace(er,ef);return"ms-"===t.slice(0,3)&&(t="-"+t),t}(t)]=e[t]);return n}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?G[r.property]||r.property:r.attribute,n]}}(e,r,t.properties[r]);if(i){let[r,o]=i;e.tableCellAlignToStyle&&"align"===r&&"string"==typeof o&&ei.has(t.tagName)?n=o:l[r]=o}}return n&&((l.style||(l.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=n),l}(e,t),a=ec(e,t);return el.has(t.tagName)&&(a=a.filter(function(e){return"string"!=typeof e||!("object"==typeof e?"text"===e.type&&f(e.value):f(e))})),es(e,o,i,t),eu(o,a),e.ancestors.pop(),e.schema=r,e.create(t,i,o,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){let n=t.data.estree.body[0];return n.type,e.evaluater.evaluateExpression(n.expression)}ed(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){let r=e.schema,l=r;"svg"===t.name&&"html"===r.space&&(e.schema=z),e.ancestors.push(t);let i=null===t.name?e.Fragment:ep(e,t.name,!0),o=function(e,t){let n={};for(let r of t.attributes)if("mdxJsxExpressionAttribute"===r.type)if(r.data&&r.data.estree&&e.evaluater){let t=r.data.estree.body[0];t.type;let l=t.expression;l.type;let i=l.properties[0];i.type,Object.assign(n,e.evaluater.evaluateExpression(i.argument))}else ed(e,t.position);else{let l,i=r.name;if(r.value&&"object"==typeof r.value)if(r.value.data&&r.value.data.estree&&e.evaluater){let t=r.value.data.estree.body[0];t.type,l=e.evaluater.evaluateExpression(t.expression)}else ed(e,t.position);else l=null===r.value||r.value;n[i]=l}return n}(e,t),a=ec(e,t);return es(e,o,i,t),eu(o,a),e.ancestors.pop(),e.schema=r,e.create(t,i,o,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);ed(e,t.position)}(e,t):"root"===t.type?function(e,t,n){let r={};return eu(r,ec(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?(r=0,t.value):void 0}function es(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function eu(e,t){if(t.length>0){let n=t.length>1?t:t[0];n&&(e.children=n)}}function ec(e,t){let n=[],r=-1,l=e.passKeys?new Map:en;for(;++r<t.children.length;){let i,o=t.children[r];if(e.passKeys){let e="element"===o.type?o.tagName:"mdxJsxFlowElement"===o.type||"mdxJsxTextElement"===o.type?o.name:void 0;if(e){let t=l.get(e)||0;i=e+"-"+t,l.set(e,t+1)}}let a=ea(e,o,i);void 0!==a&&n.push(a)}return n}function ep(e,t,n){let r;if(n)if(t.includes(".")){let e,n=t.split("."),l=-1;for(;++l<n.length;){let t=p(n[l])?{type:"Identifier",name:n[l]}:{type:"Literal",value:n[l]};e=e?{type:"MemberExpression",object:e,property:t,computed:!!(l&&"Literal"===t.type),optional:!1}:t}r=e}else r=p(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};else r={type:"Literal",value:t};if("Literal"===r.type){let t=r.value;return et.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);ed(e)}function ed(e,t){let n=new ee("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=eo+"#cannot-handle-mdx-estrees-without-createevaluater",n}function ef(e){return"-"+e.toLowerCase()}let eh={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};n(61120);let em={};function eg(e,t,n){var r;if((r=e)&&"object"==typeof r){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return ey(e.children,t,n)}return Array.isArray(e)?ey(e,t,n):""}function ey(e,t,n){let r=[],l=-1;for(;++l<e.length;)r[l]=eg(e[l],t,n);return r.join("")}function eb(e,t,n,r){let l,i=e.length,o=0;if(t=t<0?-t>i?0:i+t:t>i?i:t,n=n>0?n:0,r.length<1e4)(l=Array.from(r)).unshift(t,n),e.splice(...l);else for(n&&e.splice(t,n);o<r.length;)(l=r.slice(o,o+1e4)).unshift(t,0),e.splice(...l),o+=1e4,t+=1e4}function ex(e,t){return e.length>0?(eb(e,e.length,0,t),e):t}class ev{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){let n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-(t||0),Number.POSITIVE_INFINITY);return n&&ek(this.left,n),r.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),ek(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),ek(this.right,e.reverse())}setCursor(e){if(e!==this.left.length&&(!(e>this.left.length)||0!==this.right.length)&&(!(e<0)||0!==this.left.length))if(e<this.left.length){let t=this.left.splice(e,Number.POSITIVE_INFINITY);ek(this.right,t.reverse())}else{let t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);ek(this.left,t.reverse())}}}function ek(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function ew(e){let t,n,r,l,i,o,a,s={},u=-1,c=new ev(e);for(;++u<c.length;){for(;u in s;)u=s[u];if(t=c.get(u),u&&"chunkFlow"===t[1].type&&"listItemPrefix"===c.get(u-1)[1].type&&((r=0)<(o=t[1]._tokenizer.events).length&&"lineEndingBlank"===o[r][1].type&&(r+=2),r<o.length&&"content"===o[r][1].type))for(;++r<o.length&&"content"!==o[r][1].type;)"chunkText"===o[r][1].type&&(o[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===t[0])t[1].contentType&&(Object.assign(s,function(e,t){let n,r,l=e.get(t)[1],i=e.get(t)[2],o=t-1,a=[],s=l._tokenizer;!s&&(s=i.parser[l.contentType](l.start),l._contentTypeTextTrailing&&(s._contentTypeTextTrailing=!0));let u=s.events,c=[],p={},d=-1,f=l,h=0,m=0,g=[0];for(;f;){for(;e.get(++o)[1]!==f;);a.push(o),!f._tokenizer&&(n=i.sliceStream(f),f.next||n.push(null),r&&s.defineSkip(f.start),f._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=!0),s.write(n),f._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=void 0)),r=f,f=f.next}for(f=l;++d<u.length;)"exit"===u[d][0]&&"enter"===u[d-1][0]&&u[d][1].type===u[d-1][1].type&&u[d][1].start.line!==u[d][1].end.line&&(m=d+1,g.push(m),f._tokenizer=void 0,f.previous=void 0,f=f.next);for(s.events=[],f?(f._tokenizer=void 0,f.previous=void 0):g.pop(),d=g.length;d--;){let t=u.slice(g[d],g[d+1]),n=a.pop();c.push([n,n+t.length-1]),e.splice(n,2,t)}for(c.reverse(),d=-1;++d<c.length;)p[h+c[d][0]]=h+c[d][1],h+=c[d][1]-c[d][0]-1;return p}(c,u)),u=s[u],a=!0);else if(t[1]._container){for(r=u,n=void 0;r--;)if("lineEnding"===(l=c.get(r))[1].type||"lineEndingBlank"===l[1].type)"enter"===l[0]&&(n&&(c.get(n)[1].type="lineEndingBlank"),l[1].type="lineEnding",n=r);else if("linePrefix"===l[1].type||"listItemIndent"===l[1].type);else break;n&&(t[1].end={...c.get(n)[1].start},(i=c.slice(n,u)).unshift(t),c.splice(n,u-n+1,i))}}return eb(e,0,Number.POSITIVE_INFINITY,c.slice(0)),!a}let eS={}.hasOwnProperty,eE=e_(/[A-Za-z]/),eT=e_(/[\dA-Za-z]/),eC=e_(/[#-'*+\--9=?A-Z^-~]/);function eD(e){return null!==e&&(e<32||127===e)}let eq=e_(/\d/),eL=e_(/[\dA-Fa-f]/),eP=e_(/[!-/:-@[-`{-~]/);function eA(e){return null!==e&&e<-2}function eN(e){return null!==e&&(e<0||32===e)}function eI(e){return -2===e||-1===e||32===e}let eR=e_(/\p{P}|\p{S}/u),eO=e_(/\s/);function e_(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function ej(e,t,n,r){let l=r?r-1:Number.POSITIVE_INFINITY,i=0;return function(r){return eI(r)?(e.enter(n),function r(o){return eI(o)&&i++<l?(e.consume(o),r):(e.exit(n),t(o))}(r)):t(r)}}let eM={tokenize:function(e){let t,n=e.attempt(this.parser.constructs.contentInitial,function(t){return null===t?void e.consume(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),ej(e,n,"linePrefix"))},function(n){return e.enter("paragraph"),function n(r){let l=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=l),t=l,function t(r){if(null===r){e.exit("chunkText"),e.exit("paragraph"),e.consume(r);return}return eA(r)?(e.consume(r),e.exit("chunkText"),n):(e.consume(r),t)}(r)}(n)});return n}},ez={tokenize:function(e){let t,n,r,l=this,i=[],o=0;return a;function a(t){if(o<i.length){let n=i[o];return l.containerState=n[1],e.attempt(n[0].continuation,s,u)(t)}return u(t)}function s(e){if(o++,l.containerState._closeFlow){let n;l.containerState._closeFlow=void 0,t&&y();let r=l.events.length,i=r;for(;i--;)if("exit"===l.events[i][0]&&"chunkFlow"===l.events[i][1].type){n=l.events[i][1].end;break}g(o);let a=r;for(;a<l.events.length;)l.events[a][1].end={...n},a++;return eb(l.events,i+1,0,l.events.slice(r)),l.events.length=a,u(e)}return a(e)}function u(n){if(o===i.length){if(!t)return d(n);if(t.currentConstruct&&t.currentConstruct.concrete)return h(n);l.interrupt=!!(t.currentConstruct&&!t._gfmTableDynamicInterruptHack)}return l.containerState={},e.check(eF,c,p)(n)}function c(e){return t&&y(),g(o),d(e)}function p(e){return l.parser.lazy[l.now().line]=o!==i.length,r=l.now().offset,h(e)}function d(t){return l.containerState={},e.attempt(eF,f,h)(t)}function f(e){return o++,i.push([l.currentConstruct,l.containerState]),d(e)}function h(r){if(null===r){t&&y(),g(0),e.consume(r);return}return t=t||l.parser.flow(l.now()),e.enter("chunkFlow",{_tokenizer:t,contentType:"flow",previous:n}),function t(n){if(null===n){m(e.exit("chunkFlow"),!0),g(0),e.consume(n);return}return eA(n)?(e.consume(n),m(e.exit("chunkFlow")),o=0,l.interrupt=void 0,a):(e.consume(n),t)}(r)}function m(e,i){let a=l.sliceStream(e);if(i&&a.push(null),e.previous=n,n&&(n.next=e),n=e,t.defineSkip(e.start),t.write(a),l.parser.lazy[e.start.line]){let e,n,i=t.events.length;for(;i--;)if(t.events[i][1].start.offset<r&&(!t.events[i][1].end||t.events[i][1].end.offset>r))return;let a=l.events.length,s=a;for(;s--;)if("exit"===l.events[s][0]&&"chunkFlow"===l.events[s][1].type){if(e){n=l.events[s][1].end;break}e=!0}for(g(o),i=a;i<l.events.length;)l.events[i][1].end={...n},i++;eb(l.events,s+1,0,l.events.slice(a)),l.events.length=i}}function g(t){let n=i.length;for(;n-- >t;){let t=i[n];l.containerState=t[1],t[0].exit.call(l,e)}i.length=t}function y(){t.write([null]),n=void 0,t=void 0,l.containerState._closeFlow=void 0}}},eF={tokenize:function(e,t,n){return ej(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}},eB={partial:!0,tokenize:function(e,t,n){return function(t){return eI(t)?ej(e,r,"linePrefix")(t):r(t)};function r(e){return null===e||eA(e)?t(e):n(e)}}},eU={resolve:function(e){return ew(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?l(t):eA(t)?e.check(eV,i,l)(t):(e.consume(t),r)}function l(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function i(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},eV={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),ej(e,l,"linePrefix")};function l(l){if(null===l||eA(l))return n(l);let i=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(l):e.interrupt(r.parser.constructs.flow,n,t)(l)}}},eH={tokenize:function(e){let t=this,n=e.attempt(eB,function(r){return null===r?void e.consume(r):(e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n)},e.attempt(this.parser.constructs.flowInitial,r,ej(e,e.attempt(this.parser.constructs.flow,r,e.attempt(eU,r)),"linePrefix")));return n;function r(r){return null===r?void e.consume(r):(e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n)}}},eG={resolveAll:eX()},eY=eW("string"),eK=eW("text");function eW(e){return{resolveAll:eX("text"===e?eQ:void 0),tokenize:function(t){let n=this,r=this.parser.constructs[e],l=t.attempt(r,i,o);return i;function i(e){return s(e)?l(e):o(e)}function o(e){return null===e?void t.consume(e):(t.enter("data"),t.consume(e),a)}function a(e){return s(e)?(t.exit("data"),l(e)):(t.consume(e),a)}function s(e){if(null===e)return!0;let t=r[e],l=-1;if(t)for(;++l<t.length;){let e=t[l];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function eX(e){return function(t,n){let r,l=-1;for(;++l<=t.length;)void 0===r?t[l]&&"data"===t[l][1].type&&(r=l,l++):t[l]&&"data"===t[l][1].type||(l!==r+2&&(t[r][1].end=t[l-1][1].end,t.splice(r+2,l-r-2),l=r+2),r=void 0);return e?e(t,n):t}}function eQ(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){let r,l=e[n-1][1],i=t.sliceStream(l),o=i.length,a=-1,s=0;for(;o--;){let e=i[o];if("string"==typeof e){for(a=e.length;32===e.charCodeAt(a-1);)s++,a--;if(a)break;a=-1}else if(-2===e)r=!0,s++;else if(-1===e);else{o++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(s=0),s){let i={type:n===e.length||r||s<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:o?a:l.start._bufferIndex+a,_index:l.start._index+o,line:l.end.line,column:l.end.column-s,offset:l.end.offset-s},end:{...l.end}};l.end={...i.start},l.start.offset===l.end.offset?Object.assign(l,i):(e.splice(n,0,["enter",i,t],["exit",i,t]),n+=2)}n++}return e}let eJ={name:"thematicBreak",tokenize:function(e,t,n){let r,l=0;return function(i){var o;return e.enter("thematicBreak"),r=o=i,function i(o){return o===r?(e.enter("thematicBreakSequence"),function t(n){return n===r?(e.consume(n),l++,t):(e.exit("thematicBreakSequence"),eI(n)?ej(e,i,"whitespace")(n):i(n))}(o)):l>=3&&(null===o||eA(o))?(e.exit("thematicBreak"),t(o)):n(o)}(o)}}},eZ={continuation:{tokenize:function(e,t,n){let r=this;return r.containerState._closeFlow=void 0,e.check(eB,function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,ej(e,t,"listItemIndent",r.containerState.size+1)(n)},function(n){return r.containerState.furtherBlankLines||!eI(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,l(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(e0,t,l)(n))});function l(l){return r.containerState._closeFlow=!0,r.interrupt=void 0,ej(e,e.attempt(eZ,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(l)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){let r=this,l=r.events[r.events.length-1],i=l&&"linePrefix"===l[1].type?l[2].sliceSerialize(l[1],!0).length:0,o=0;return function(t){let l=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===l?!r.containerState.marker||t===r.containerState.marker:eq(t)){if(r.containerState.type||(r.containerState.type=l,e.enter(l,{_container:!0})),"listUnordered"===l)return e.enter("listItemPrefix"),42===t||45===t?e.check(eJ,n,a)(t):a(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),function t(l){return eq(l)&&++o<10?(e.consume(l),t):(!r.interrupt||o<2)&&(r.containerState.marker?l===r.containerState.marker:41===l||46===l)?(e.exit("listItemValue"),a(l)):n(l)}(t)}return n(t)};function a(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(eB,r.interrupt?n:s,e.attempt(e$,c,u))}function s(e){return r.containerState.initialBlankLine=!0,i++,c(e)}function u(t){return eI(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),c):n(t)}function c(n){return r.containerState.size=i+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},e$={partial:!0,tokenize:function(e,t,n){let r=this;return ej(e,function(e){let l=r.events[r.events.length-1];return!eI(e)&&l&&"listItemPrefixWhitespace"===l[1].type?t(e):n(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},e0={partial:!0,tokenize:function(e,t,n){let r=this;return ej(e,function(e){let l=r.events[r.events.length-1];return l&&"listItemIndent"===l[1].type&&l[2].sliceSerialize(l[1],!0).length===r.containerState.size?t(e):n(e)},"listItemIndent",r.containerState.size+1)}},e1={continuation:{tokenize:function(e,t,n){let r=this;return function(t){return eI(t)?ej(e,l,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):l(t)};function l(r){return e.attempt(e1,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){let r=this;return function(t){if(62===t){let n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),l}return n(t)};function l(n){return eI(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};function e2(e,t,n,r,l,i,o,a,s){let u=s||Number.POSITIVE_INFINITY,c=0;return function(t){return 60===t?(e.enter(r),e.enter(l),e.enter(i),e.consume(t),e.exit(i),p):null===t||32===t||41===t||eD(t)?n(t):(e.enter(r),e.enter(o),e.enter(a),e.enter("chunkString",{contentType:"string"}),h(t))};function p(n){return 62===n?(e.enter(i),e.consume(n),e.exit(i),e.exit(l),e.exit(r),t):(e.enter(a),e.enter("chunkString",{contentType:"string"}),d(n))}function d(t){return 62===t?(e.exit("chunkString"),e.exit(a),p(t)):null===t||60===t||eA(t)?n(t):(e.consume(t),92===t?f:d)}function f(t){return 60===t||62===t||92===t?(e.consume(t),d):d(t)}function h(l){return!c&&(null===l||41===l||eN(l))?(e.exit("chunkString"),e.exit(a),e.exit(o),e.exit(r),t(l)):c<u&&40===l?(e.consume(l),c++,h):41===l?(e.consume(l),c--,h):null===l||32===l||40===l||eD(l)?n(l):(e.consume(l),92===l?m:h)}function m(t){return 40===t||41===t||92===t?(e.consume(t),h):h(t)}}function e4(e,t,n,r,l,i){let o,a=this,s=0;return function(t){return e.enter(r),e.enter(l),e.consume(t),e.exit(l),e.enter(i),u};function u(p){return s>999||null===p||91===p||93===p&&!o||94===p&&!s&&"_hiddenFootnoteSupport"in a.parser.constructs?n(p):93===p?(e.exit(i),e.enter(l),e.consume(p),e.exit(l),e.exit(r),t):eA(p)?(e.enter("lineEnding"),e.consume(p),e.exit("lineEnding"),u):(e.enter("chunkString",{contentType:"string"}),c(p))}function c(t){return null===t||91===t||93===t||eA(t)||s++>999?(e.exit("chunkString"),u(t)):(e.consume(t),o||(o=!eI(t)),92===t?p:c)}function p(t){return 91===t||92===t||93===t?(e.consume(t),s++,c):c(t)}}function e3(e,t,n,r,l,i){let o;return function(t){return 34===t||39===t||40===t?(e.enter(r),e.enter(l),e.consume(t),e.exit(l),o=40===t?41:t,a):n(t)};function a(n){return n===o?(e.enter(l),e.consume(n),e.exit(l),e.exit(r),t):(e.enter(i),s(n))}function s(t){return t===o?(e.exit(i),a(o)):null===t?n(t):eA(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),ej(e,s,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),u(t))}function u(t){return t===o||null===t||eA(t)?(e.exit("chunkString"),s(t)):(e.consume(t),92===t?c:u)}function c(t){return t===o||92===t?(e.consume(t),u):u(t)}}function e6(e,t){let n;return function r(l){return eA(l)?(e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),n=!0,r):eI(l)?ej(e,r,n?"linePrefix":"lineSuffix")(l):t(l)}}function e9(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}let e5={partial:!0,tokenize:function(e,t,n){return function(t){return eN(t)?e6(e,r)(t):n(t)};function r(t){return e3(e,l,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function l(t){return eI(t)?ej(e,i,"whitespace")(t):i(t)}function i(e){return null===e||eA(e)?t(e):n(e)}}},e8={name:"codeIndented",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("codeIndented"),ej(e,l,"linePrefix",5)(t)};function l(t){let l=r.events[r.events.length-1];return l&&"linePrefix"===l[1].type&&l[2].sliceSerialize(l[1],!0).length>=4?function t(n){return null===n?i(n):eA(n)?e.attempt(e7,t,i)(n):(e.enter("codeFlowValue"),function n(r){return null===r||eA(r)?(e.exit("codeFlowValue"),t(r)):(e.consume(r),n)}(n))}(t):n(t)}function i(n){return e.exit("codeIndented"),t(n)}}},e7={partial:!0,tokenize:function(e,t,n){let r=this;return l;function l(t){return r.parser.lazy[r.now().line]?n(t):eA(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),l):ej(e,i,"linePrefix",5)(t)}function i(e){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(e):eA(e)?l(e):n(e)}}},te={name:"setextUnderline",resolveTo:function(e,t){let n,r,l,i=e.length;for(;i--;)if("enter"===e[i][0]){if("content"===e[i][1].type){n=i;break}"paragraph"===e[i][1].type&&(r=i)}else"content"===e[i][1].type&&e.splice(i,1),l||"definition"!==e[i][1].type||(l=i);let o={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};return e[r][1].type="setextHeadingText",l?(e.splice(r,0,["enter",o,t]),e.splice(l+1,0,["exit",e[n][1],t]),e[n][1].end={...e[l][1].end}):e[n][1]=o,e.push(["exit",o,t]),e},tokenize:function(e,t,n){let r,l=this;return function(t){var o;let a,s=l.events.length;for(;s--;)if("lineEnding"!==l.events[s][1].type&&"linePrefix"!==l.events[s][1].type&&"content"!==l.events[s][1].type){a="paragraph"===l.events[s][1].type;break}return!l.parser.lazy[l.now().line]&&(l.interrupt||a)?(e.enter("setextHeadingLine"),r=t,o=t,e.enter("setextHeadingLineSequence"),function t(n){return n===r?(e.consume(n),t):(e.exit("setextHeadingLineSequence"),eI(n)?ej(e,i,"lineSuffix")(n):i(n))}(o)):n(t)};function i(r){return null===r||eA(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}},tt=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],tn=["pre","script","style","textarea"],tr={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(eB,t,n)}}},tl={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return eA(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),l):n(t)};function l(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},ti={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return null===t?n(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),l)};function l(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},to={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){let r,l=this,i={partial:!0,tokenize:function(e,t,n){let i=0;return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o};function o(t){return e.enter("codeFencedFence"),eI(t)?ej(e,s,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):s(t)}function s(t){return t===r?(e.enter("codeFencedFenceSequence"),function t(l){return l===r?(i++,e.consume(l),t):i>=a?(e.exit("codeFencedFenceSequence"),eI(l)?ej(e,u,"whitespace")(l):u(l)):n(l)}(t)):n(t)}function u(r){return null===r||eA(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}},o=0,a=0;return function(t){var i=t;let u=l.events[l.events.length-1];return o=u&&"linePrefix"===u[1].type?u[2].sliceSerialize(u[1],!0).length:0,r=i,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),function t(l){return l===r?(a++,e.consume(l),t):a<3?n(l):(e.exit("codeFencedFenceSequence"),eI(l)?ej(e,s,"whitespace")(l):s(l))}(i)};function s(i){return null===i||eA(i)?(e.exit("codeFencedFence"),l.interrupt?t(i):e.check(ti,c,h)(i)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),function t(l){return null===l||eA(l)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),s(l)):eI(l)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),ej(e,u,"whitespace")(l)):96===l&&l===r?n(l):(e.consume(l),t)}(i))}function u(t){return null===t||eA(t)?s(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),function t(l){return null===l||eA(l)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),s(l)):96===l&&l===r?n(l):(e.consume(l),t)}(t))}function c(t){return e.attempt(i,h,p)(t)}function p(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),d}function d(t){return o>0&&eI(t)?ej(e,f,"linePrefix",o+1)(t):f(t)}function f(t){return null===t||eA(t)?e.check(ti,c,h)(t):(e.enter("codeFlowValue"),function t(n){return null===n||eA(n)?(e.exit("codeFlowValue"),f(n)):(e.consume(n),t)}(t))}function h(n){return e.exit("codeFenced"),t(n)}}},ta={AElig:"\xc6",AMP:"&",Aacute:"\xc1",Abreve:"Ă",Acirc:"\xc2",Acy:"А",Afr:"\uD835\uDD04",Agrave:"\xc0",Alpha:"Α",Amacr:"Ā",And:"⩓",Aogon:"Ą",Aopf:"\uD835\uDD38",ApplyFunction:"⁡",Aring:"\xc5",Ascr:"\uD835\uDC9C",Assign:"≔",Atilde:"\xc3",Auml:"\xc4",Backslash:"∖",Barv:"⫧",Barwed:"⌆",Bcy:"Б",Because:"∵",Bernoullis:"ℬ",Beta:"Β",Bfr:"\uD835\uDD05",Bopf:"\uD835\uDD39",Breve:"˘",Bscr:"ℬ",Bumpeq:"≎",CHcy:"Ч",COPY:"\xa9",Cacute:"Ć",Cap:"⋒",CapitalDifferentialD:"ⅅ",Cayleys:"ℭ",Ccaron:"Č",Ccedil:"\xc7",Ccirc:"Ĉ",Cconint:"∰",Cdot:"Ċ",Cedilla:"\xb8",CenterDot:"\xb7",Cfr:"ℭ",Chi:"Χ",CircleDot:"⊙",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",Colon:"∷",Colone:"⩴",Congruent:"≡",Conint:"∯",ContourIntegral:"∮",Copf:"ℂ",Coproduct:"∐",CounterClockwiseContourIntegral:"∳",Cross:"⨯",Cscr:"\uD835\uDC9E",Cup:"⋓",CupCap:"≍",DD:"ⅅ",DDotrahd:"⤑",DJcy:"Ђ",DScy:"Ѕ",DZcy:"Џ",Dagger:"‡",Darr:"↡",Dashv:"⫤",Dcaron:"Ď",Dcy:"Д",Del:"∇",Delta:"Δ",Dfr:"\uD835\uDD07",DiacriticalAcute:"\xb4",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",Diamond:"⋄",DifferentialD:"ⅆ",Dopf:"\uD835\uDD3B",Dot:"\xa8",DotDot:"⃜",DotEqual:"≐",DoubleContourIntegral:"∯",DoubleDot:"\xa8",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",Downarrow:"⇓",Dscr:"\uD835\uDC9F",Dstrok:"Đ",ENG:"Ŋ",ETH:"\xd0",Eacute:"\xc9",Ecaron:"Ě",Ecirc:"\xca",Ecy:"Э",Edot:"Ė",Efr:"\uD835\uDD08",Egrave:"\xc8",Element:"∈",Emacr:"Ē",EmptySmallSquare:"◻",EmptyVerySmallSquare:"▫",Eogon:"Ę",Eopf:"\uD835\uDD3C",Epsilon:"Ε",Equal:"⩵",EqualTilde:"≂",Equilibrium:"⇌",Escr:"ℰ",Esim:"⩳",Eta:"Η",Euml:"\xcb",Exists:"∃",ExponentialE:"ⅇ",Fcy:"Ф",Ffr:"\uD835\uDD09",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",Fopf:"\uD835\uDD3D",ForAll:"∀",Fouriertrf:"ℱ",Fscr:"ℱ",GJcy:"Ѓ",GT:">",Gamma:"Γ",Gammad:"Ϝ",Gbreve:"Ğ",Gcedil:"Ģ",Gcirc:"Ĝ",Gcy:"Г",Gdot:"Ġ",Gfr:"\uD835\uDD0A",Gg:"⋙",Gopf:"\uD835\uDD3E",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"\uD835\uDCA2",Gt:"≫",HARDcy:"Ъ",Hacek:"ˇ",Hat:"^",Hcirc:"Ĥ",Hfr:"ℌ",HilbertSpace:"ℋ",Hopf:"ℍ",HorizontalLine:"─",Hscr:"ℋ",Hstrok:"Ħ",HumpDownHump:"≎",HumpEqual:"≏",IEcy:"Е",IJlig:"Ĳ",IOcy:"Ё",Iacute:"\xcd",Icirc:"\xce",Icy:"И",Idot:"İ",Ifr:"ℑ",Igrave:"\xcc",Im:"ℑ",Imacr:"Ī",ImaginaryI:"ⅈ",Implies:"⇒",Int:"∬",Integral:"∫",Intersection:"⋂",InvisibleComma:"⁣",InvisibleTimes:"⁢",Iogon:"Į",Iopf:"\uD835\uDD40",Iota:"Ι",Iscr:"ℐ",Itilde:"Ĩ",Iukcy:"І",Iuml:"\xcf",Jcirc:"Ĵ",Jcy:"Й",Jfr:"\uD835\uDD0D",Jopf:"\uD835\uDD41",Jscr:"\uD835\uDCA5",Jsercy:"Ј",Jukcy:"Є",KHcy:"Х",KJcy:"Ќ",Kappa:"Κ",Kcedil:"Ķ",Kcy:"К",Kfr:"\uD835\uDD0E",Kopf:"\uD835\uDD42",Kscr:"\uD835\uDCA6",LJcy:"Љ",LT:"<",Lacute:"Ĺ",Lambda:"Λ",Lang:"⟪",Laplacetrf:"ℒ",Larr:"↞",Lcaron:"Ľ",Lcedil:"Ļ",Lcy:"Л",LeftAngleBracket:"⟨",LeftArrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",LeftRightArrow:"↔",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",Leftarrow:"⇐",Leftrightarrow:"⇔",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",LessLess:"⪡",LessSlantEqual:"⩽",LessTilde:"≲",Lfr:"\uD835\uDD0F",Ll:"⋘",Lleftarrow:"⇚",Lmidot:"Ŀ",LongLeftArrow:"⟵",LongLeftRightArrow:"⟷",LongRightArrow:"⟶",Longleftarrow:"⟸",Longleftrightarrow:"⟺",Longrightarrow:"⟹",Lopf:"\uD835\uDD43",LowerLeftArrow:"↙",LowerRightArrow:"↘",Lscr:"ℒ",Lsh:"↰",Lstrok:"Ł",Lt:"≪",Map:"⤅",Mcy:"М",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"\uD835\uDD10",MinusPlus:"∓",Mopf:"\uD835\uDD44",Mscr:"ℳ",Mu:"Μ",NJcy:"Њ",Nacute:"Ń",Ncaron:"Ň",Ncedil:"Ņ",Ncy:"Н",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:"\n",Nfr:"\uD835\uDD11",NoBreak:"⁠",NonBreakingSpace:"\xa0",Nopf:"ℕ",Not:"⫬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",Nscr:"\uD835\uDCA9",Ntilde:"\xd1",Nu:"Ν",OElig:"Œ",Oacute:"\xd3",Ocirc:"\xd4",Ocy:"О",Odblac:"Ő",Ofr:"\uD835\uDD12",Ograve:"\xd2",Omacr:"Ō",Omega:"Ω",Omicron:"Ο",Oopf:"\uD835\uDD46",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",Or:"⩔",Oscr:"\uD835\uDCAA",Oslash:"\xd8",Otilde:"\xd5",Otimes:"⨷",Ouml:"\xd6",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",PartialD:"∂",Pcy:"П",Pfr:"\uD835\uDD13",Phi:"Φ",Pi:"Π",PlusMinus:"\xb1",Poincareplane:"ℌ",Popf:"ℙ",Pr:"⪻",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",Prime:"″",Product:"∏",Proportion:"∷",Proportional:"∝",Pscr:"\uD835\uDCAB",Psi:"Ψ",QUOT:'"',Qfr:"\uD835\uDD14",Qopf:"ℚ",Qscr:"\uD835\uDCAC",RBarr:"⤐",REG:"\xae",Racute:"Ŕ",Rang:"⟫",Rarr:"↠",Rarrtl:"⤖",Rcaron:"Ř",Rcedil:"Ŗ",Rcy:"Р",Re:"ℜ",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",Rfr:"ℜ",Rho:"Ρ",RightAngleBracket:"⟩",RightArrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",Rightarrow:"⇒",Ropf:"ℝ",RoundImplies:"⥰",Rrightarrow:"⇛",Rscr:"ℛ",Rsh:"↱",RuleDelayed:"⧴",SHCHcy:"Щ",SHcy:"Ш",SOFTcy:"Ь",Sacute:"Ś",Sc:"⪼",Scaron:"Š",Scedil:"Ş",Scirc:"Ŝ",Scy:"С",Sfr:"\uD835\uDD16",ShortDownArrow:"↓",ShortLeftArrow:"←",ShortRightArrow:"→",ShortUpArrow:"↑",Sigma:"Σ",SmallCircle:"∘",Sopf:"\uD835\uDD4A",Sqrt:"√",Square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",Sscr:"\uD835\uDCAE",Star:"⋆",Sub:"⋐",Subset:"⋐",SubsetEqual:"⊆",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",SuchThat:"∋",Sum:"∑",Sup:"⋑",Superset:"⊃",SupersetEqual:"⊇",Supset:"⋑",THORN:"\xde",TRADE:"™",TSHcy:"Ћ",TScy:"Ц",Tab:"	",Tau:"Τ",Tcaron:"Ť",Tcedil:"Ţ",Tcy:"Т",Tfr:"\uD835\uDD17",Therefore:"∴",Theta:"Θ",ThickSpace:"  ",ThinSpace:" ",Tilde:"∼",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",Topf:"\uD835\uDD4B",TripleDot:"⃛",Tscr:"\uD835\uDCAF",Tstrok:"Ŧ",Uacute:"\xda",Uarr:"↟",Uarrocir:"⥉",Ubrcy:"Ў",Ubreve:"Ŭ",Ucirc:"\xdb",Ucy:"У",Udblac:"Ű",Ufr:"\uD835\uDD18",Ugrave:"\xd9",Umacr:"Ū",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",Uopf:"\uD835\uDD4C",UpArrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",UpDownArrow:"↕",UpEquilibrium:"⥮",UpTee:"⊥",UpTeeArrow:"↥",Uparrow:"⇑",Updownarrow:"⇕",UpperLeftArrow:"↖",UpperRightArrow:"↗",Upsi:"ϒ",Upsilon:"Υ",Uring:"Ů",Uscr:"\uD835\uDCB0",Utilde:"Ũ",Uuml:"\xdc",VDash:"⊫",Vbar:"⫫",Vcy:"В",Vdash:"⊩",Vdashl:"⫦",Vee:"⋁",Verbar:"‖",Vert:"‖",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"\uD835\uDD19",Vopf:"\uD835\uDD4D",Vscr:"\uD835\uDCB1",Vvdash:"⊪",Wcirc:"Ŵ",Wedge:"⋀",Wfr:"\uD835\uDD1A",Wopf:"\uD835\uDD4E",Wscr:"\uD835\uDCB2",Xfr:"\uD835\uDD1B",Xi:"Ξ",Xopf:"\uD835\uDD4F",Xscr:"\uD835\uDCB3",YAcy:"Я",YIcy:"Ї",YUcy:"Ю",Yacute:"\xdd",Ycirc:"Ŷ",Ycy:"Ы",Yfr:"\uD835\uDD1C",Yopf:"\uD835\uDD50",Yscr:"\uD835\uDCB4",Yuml:"Ÿ",ZHcy:"Ж",Zacute:"Ź",Zcaron:"Ž",Zcy:"З",Zdot:"Ż",ZeroWidthSpace:"​",Zeta:"Ζ",Zfr:"ℨ",Zopf:"ℤ",Zscr:"\uD835\uDCB5",aacute:"\xe1",abreve:"ă",ac:"∾",acE:"∾̳",acd:"∿",acirc:"\xe2",acute:"\xb4",acy:"а",aelig:"\xe6",af:"⁡",afr:"\uD835\uDD1E",agrave:"\xe0",alefsym:"ℵ",aleph:"ℵ",alpha:"α",amacr:"ā",amalg:"⨿",amp:"&",and:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"\xc5",angzarr:"⍼",aogon:"ą",aopf:"\uD835\uDD52",ap:"≈",apE:"⩰",apacir:"⩯",ape:"≊",apid:"≋",apos:"'",approx:"≈",approxeq:"≊",aring:"\xe5",ascr:"\uD835\uDCB6",ast:"*",asymp:"≈",asympeq:"≍",atilde:"\xe3",auml:"\xe4",awconint:"∳",awint:"⨑",bNot:"⫭",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",barvee:"⊽",barwed:"⌅",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",bcy:"б",bdquo:"„",becaus:"∵",because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",beta:"β",beth:"ℶ",between:"≬",bfr:"\uD835\uDD1F",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bnot:"⌐",bopf:"\uD835\uDD53",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxDL:"╗",boxDR:"╔",boxDl:"╖",boxDr:"╓",boxH:"═",boxHD:"╦",boxHU:"╩",boxHd:"╤",boxHu:"╧",boxUL:"╝",boxUR:"╚",boxUl:"╜",boxUr:"╙",boxV:"║",boxVH:"╬",boxVL:"╣",boxVR:"╠",boxVh:"╫",boxVl:"╢",boxVr:"╟",boxbox:"⧉",boxdL:"╕",boxdR:"╒",boxdl:"┐",boxdr:"┌",boxh:"─",boxhD:"╥",boxhU:"╨",boxhd:"┬",boxhu:"┴",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxuL:"╛",boxuR:"╘",boxul:"┘",boxur:"└",boxv:"│",boxvH:"╪",boxvL:"╡",boxvR:"╞",boxvh:"┼",boxvl:"┤",boxvr:"├",bprime:"‵",breve:"˘",brvbar:"\xa6",bscr:"\uD835\uDCB7",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",bumpeq:"≏",cacute:"ć",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",caps:"∩︀",caret:"⁁",caron:"ˇ",ccaps:"⩍",ccaron:"č",ccedil:"\xe7",ccirc:"ĉ",ccups:"⩌",ccupssm:"⩐",cdot:"ċ",cedil:"\xb8",cemptyv:"⦲",cent:"\xa2",centerdot:"\xb7",cfr:"\uD835\uDD20",chcy:"ч",check:"✓",checkmark:"✓",chi:"χ",cir:"○",cirE:"⧃",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledR:"\xae",circledS:"Ⓢ",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",clubs:"♣",clubsuit:"♣",colon:":",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",conint:"∮",copf:"\uD835\uDD54",coprod:"∐",copy:"\xa9",copysr:"℗",crarr:"↵",cross:"✗",cscr:"\uD835\uDCB8",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",cup:"∪",cupbrcap:"⩈",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"\xa4",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",dArr:"⇓",dHar:"⥥",dagger:"†",daleth:"ℸ",darr:"↓",dash:"‐",dashv:"⊣",dbkarow:"⤏",dblac:"˝",dcaron:"ď",dcy:"д",dd:"ⅆ",ddagger:"‡",ddarr:"⇊",ddotseq:"⩷",deg:"\xb0",delta:"δ",demptyv:"⦱",dfisht:"⥿",dfr:"\uD835\uDD21",dharl:"⇃",dharr:"⇂",diam:"⋄",diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"\xa8",digamma:"ϝ",disin:"⋲",div:"\xf7",divide:"\xf7",divideontimes:"⋇",divonx:"⋇",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",dopf:"\uD835\uDD55",dot:"˙",doteq:"≐",doteqdot:"≑",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",downarrow:"↓",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",dscr:"\uD835\uDCB9",dscy:"ѕ",dsol:"⧶",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",dzcy:"џ",dzigrarr:"⟿",eDDot:"⩷",eDot:"≑",eacute:"\xe9",easter:"⩮",ecaron:"ě",ecir:"≖",ecirc:"\xea",ecolon:"≕",ecy:"э",edot:"ė",ee:"ⅇ",efDot:"≒",efr:"\uD835\uDD22",eg:"⪚",egrave:"\xe8",egs:"⪖",egsdot:"⪘",el:"⪙",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",emacr:"ē",empty:"∅",emptyset:"∅",emptyv:"∅",emsp13:" ",emsp14:" ",emsp:" ",eng:"ŋ",ensp:" ",eogon:"ę",eopf:"\uD835\uDD56",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",equals:"=",equest:"≟",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erDot:"≓",erarr:"⥱",escr:"ℯ",esdot:"≐",esim:"≂",eta:"η",eth:"\xf0",euml:"\xeb",euro:"€",excl:"!",exist:"∃",expectation:"ℰ",exponentiale:"ⅇ",fallingdotseq:"≒",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",ffr:"\uD835\uDD23",filig:"ﬁ",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",fopf:"\uD835\uDD57",forall:"∀",fork:"⋔",forkv:"⫙",fpartint:"⨍",frac12:"\xbd",frac13:"⅓",frac14:"\xbc",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"\xbe",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",fscr:"\uD835\uDCBB",gE:"≧",gEl:"⪌",gacute:"ǵ",gamma:"γ",gammad:"ϝ",gap:"⪆",gbreve:"ğ",gcirc:"ĝ",gcy:"г",gdot:"ġ",ge:"≥",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",gfr:"\uD835\uDD24",gg:"≫",ggg:"⋙",gimel:"ℷ",gjcy:"ѓ",gl:"≷",glE:"⪒",gla:"⪥",glj:"⪤",gnE:"≩",gnap:"⪊",gnapprox:"⪊",gne:"⪈",gneq:"⪈",gneqq:"≩",gnsim:"⋧",gopf:"\uD835\uDD58",grave:"`",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",gt:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",hArr:"⇔",hairsp:" ",half:"\xbd",hamilt:"ℋ",hardcy:"ъ",harr:"↔",harrcir:"⥈",harrw:"↭",hbar:"ℏ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",hfr:"\uD835\uDD25",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",hopf:"\uD835\uDD59",horbar:"―",hscr:"\uD835\uDCBD",hslash:"ℏ",hstrok:"ħ",hybull:"⁃",hyphen:"‐",iacute:"\xed",ic:"⁣",icirc:"\xee",icy:"и",iecy:"е",iexcl:"\xa1",iff:"⇔",ifr:"\uD835\uDD26",igrave:"\xec",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",ijlig:"ĳ",imacr:"ī",image:"ℑ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",in:"∈",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",int:"∫",intcal:"⊺",integers:"ℤ",intercal:"⊺",intlarhk:"⨗",intprod:"⨼",iocy:"ё",iogon:"į",iopf:"\uD835\uDD5A",iota:"ι",iprod:"⨼",iquest:"\xbf",iscr:"\uD835\uDCBE",isin:"∈",isinE:"⋹",isindot:"⋵",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",itilde:"ĩ",iukcy:"і",iuml:"\xef",jcirc:"ĵ",jcy:"й",jfr:"\uD835\uDD27",jmath:"ȷ",jopf:"\uD835\uDD5B",jscr:"\uD835\uDCBF",jsercy:"ј",jukcy:"є",kappa:"κ",kappav:"ϰ",kcedil:"ķ",kcy:"к",kfr:"\uD835\uDD28",kgreen:"ĸ",khcy:"х",kjcy:"ќ",kopf:"\uD835\uDD5C",kscr:"\uD835\uDCC0",lAarr:"⇚",lArr:"⇐",lAtail:"⤛",lBarr:"⤎",lE:"≦",lEg:"⪋",lHar:"⥢",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",lambda:"λ",lang:"⟨",langd:"⦑",langle:"⟨",lap:"⪅",laquo:"\xab",larr:"←",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",latail:"⤙",late:"⪭",lates:"⪭︀",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",lcaron:"ľ",lcedil:"ļ",lceil:"⌈",lcub:"{",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",le:"≤",leftarrow:"←",leftarrowtail:"↢",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",leftrightarrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",leftthreetimes:"⋋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",lessgtr:"≶",lesssim:"≲",lfisht:"⥼",lfloor:"⌊",lfr:"\uD835\uDD29",lg:"≶",lgE:"⪑",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",ljcy:"љ",ll:"≪",llarr:"⇇",llcorner:"⌞",llhard:"⥫",lltri:"◺",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnE:"≨",lnap:"⪉",lnapprox:"⪉",lne:"⪇",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",longleftarrow:"⟵",longleftrightarrow:"⟷",longmapsto:"⟼",longrightarrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",lopf:"\uD835\uDD5D",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",lscr:"\uD835\uDCC1",lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",lstrok:"ł",lt:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltrPar:"⦖",ltri:"◃",ltrie:"⊴",ltrif:"◂",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",mDDot:"∺",macr:"\xaf",male:"♂",malt:"✠",maltese:"✠",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",mcy:"м",mdash:"—",measuredangle:"∡",mfr:"\uD835\uDD2A",mho:"℧",micro:"\xb5",mid:"∣",midast:"*",midcir:"⫰",middot:"\xb7",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",mopf:"\uD835\uDD5E",mp:"∓",mscr:"\uD835\uDCC2",mstpos:"∾",mu:"μ",multimap:"⊸",mumap:"⊸",nGg:"⋙̸",nGt:"≫⃒",nGtv:"≫̸",nLeftarrow:"⇍",nLeftrightarrow:"⇎",nLl:"⋘̸",nLt:"≪⃒",nLtv:"≪̸",nRightarrow:"⇏",nVDash:"⊯",nVdash:"⊮",nabla:"∇",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbsp:"\xa0",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",ncaron:"ň",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",ncy:"н",ndash:"–",ne:"≠",neArr:"⇗",nearhk:"⤤",nearr:"↗",nearrow:"↗",nedot:"≐̸",nequiv:"≢",nesear:"⤨",nesim:"≂̸",nexist:"∄",nexists:"∄",nfr:"\uD835\uDD2B",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",ngsim:"≵",ngt:"≯",ngtr:"≯",nhArr:"⇎",nharr:"↮",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",njcy:"њ",nlArr:"⇍",nlE:"≦̸",nlarr:"↚",nldr:"‥",nle:"≰",nleftarrow:"↚",nleftrightarrow:"↮",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nlsim:"≴",nlt:"≮",nltri:"⋪",nltrie:"⋬",nmid:"∤",nopf:"\uD835\uDD5F",not:"\xac",notin:"∉",notinE:"⋹̸",notindot:"⋵̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrArr:"⇏",nrarr:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nrightarrow:"↛",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",nscr:"\uD835\uDCC3",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",ntilde:"\xf1",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",nu:"ν",num:"#",numero:"№",numsp:" ",nvDash:"⊭",nvHarr:"⤄",nvap:"≍⃒",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwArr:"⇖",nwarhk:"⤣",nwarr:"↖",nwarrow:"↖",nwnear:"⤧",oS:"Ⓢ",oacute:"\xf3",oast:"⊛",ocir:"⊚",ocirc:"\xf4",ocy:"о",odash:"⊝",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",oelig:"œ",ofcir:"⦿",ofr:"\uD835\uDD2C",ogon:"˛",ograve:"\xf2",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",omacr:"ō",omega:"ω",omicron:"ο",omid:"⦶",ominus:"⊖",oopf:"\uD835\uDD60",opar:"⦷",operp:"⦹",oplus:"⊕",or:"∨",orarr:"↻",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"\xaa",ordm:"\xba",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oscr:"ℴ",oslash:"\xf8",osol:"⊘",otilde:"\xf5",otimes:"⊗",otimesas:"⨶",ouml:"\xf6",ovbar:"⌽",par:"∥",para:"\xb6",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",pfr:"\uD835\uDD2D",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",plusmn:"\xb1",plussim:"⨦",plustwo:"⨧",pm:"\xb1",pointint:"⨕",popf:"\uD835\uDD61",pound:"\xa3",pr:"≺",prE:"⪳",prap:"⪷",prcue:"≼",pre:"⪯",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",prime:"′",primes:"ℙ",prnE:"⪵",prnap:"⪹",prnsim:"⋨",prod:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",propto:"∝",prsim:"≾",prurel:"⊰",pscr:"\uD835\uDCC5",psi:"ψ",puncsp:" ",qfr:"\uD835\uDD2E",qint:"⨌",qopf:"\uD835\uDD62",qprime:"⁗",qscr:"\uD835\uDCC6",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",quot:'"',rAarr:"⇛",rArr:"⇒",rAtail:"⤜",rBarr:"⤏",rHar:"⥤",race:"∽̱",racute:"ŕ",radic:"√",raemptyv:"⦳",rang:"⟩",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"\xbb",rarr:"→",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",rarrtl:"↣",rarrw:"↝",ratail:"⤚",ratio:"∶",rationals:"ℚ",rbarr:"⤍",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",rcaron:"ř",rcedil:"ŗ",rceil:"⌉",rcub:"}",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",reg:"\xae",rfisht:"⥽",rfloor:"⌋",rfr:"\uD835\uDD2F",rhard:"⇁",rharu:"⇀",rharul:"⥬",rho:"ρ",rhov:"ϱ",rightarrow:"→",rightarrowtail:"↣",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",rightthreetimes:"⋌",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",ropf:"\uD835\uDD63",roplus:"⨮",rotimes:"⨵",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",rsaquo:"›",rscr:"\uD835\uDCC7",rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",ruluhar:"⥨",rx:"℞",sacute:"ś",sbquo:"‚",sc:"≻",scE:"⪴",scap:"⪸",scaron:"š",sccue:"≽",sce:"⪰",scedil:"ş",scirc:"ŝ",scnE:"⪶",scnap:"⪺",scnsim:"⋩",scpolint:"⨓",scsim:"≿",scy:"с",sdot:"⋅",sdotb:"⊡",sdote:"⩦",seArr:"⇘",searhk:"⤥",searr:"↘",searrow:"↘",sect:"\xa7",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",sfr:"\uD835\uDD30",sfrown:"⌢",sharp:"♯",shchcy:"щ",shcy:"ш",shortmid:"∣",shortparallel:"∥",shy:"\xad",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",sopf:"\uD835\uDD64",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",square:"□",squarf:"▪",squf:"▪",srarr:"→",sscr:"\uD835\uDCC8",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"\xaf",sub:"⊂",subE:"⫅",subdot:"⪽",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",subset:"⊂",subseteq:"⊆",subseteqq:"⫅",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",sum:"∑",sung:"♪",sup1:"\xb9",sup2:"\xb2",sup3:"\xb3",sup:"⊃",supE:"⫆",supdot:"⪾",supdsub:"⫘",supe:"⊇",supedot:"⫄",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",supset:"⊃",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swArr:"⇙",swarhk:"⤦",swarr:"↙",swarrow:"↙",swnwar:"⤪",szlig:"\xdf",target:"⌖",tau:"τ",tbrk:"⎴",tcaron:"ť",tcedil:"ţ",tcy:"т",tdot:"⃛",telrec:"⌕",tfr:"\uD835\uDD31",there4:"∴",therefore:"∴",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",thinsp:" ",thkap:"≈",thksim:"∼",thorn:"\xfe",tilde:"˜",times:"\xd7",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",topf:"\uD835\uDD65",topfork:"⫚",tosa:"⤩",tprime:"‴",trade:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",tscr:"\uD835\uDCC9",tscy:"ц",tshcy:"ћ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",uArr:"⇑",uHar:"⥣",uacute:"\xfa",uarr:"↑",ubrcy:"ў",ubreve:"ŭ",ucirc:"\xfb",ucy:"у",udarr:"⇅",udblac:"ű",udhar:"⥮",ufisht:"⥾",ufr:"\uD835\uDD32",ugrave:"\xf9",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",umacr:"ū",uml:"\xa8",uogon:"ų",uopf:"\uD835\uDD66",uparrow:"↑",updownarrow:"↕",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",upsi:"υ",upsih:"ϒ",upsilon:"υ",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",uring:"ů",urtri:"◹",uscr:"\uD835\uDCCA",utdot:"⋰",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",uuml:"\xfc",uwangle:"⦧",vArr:"⇕",vBar:"⫨",vBarv:"⫩",vDash:"⊨",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",varr:"↕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",vcy:"в",vdash:"⊢",vee:"∨",veebar:"⊻",veeeq:"≚",vellip:"⋮",verbar:"|",vert:"|",vfr:"\uD835\uDD33",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",vopf:"\uD835\uDD67",vprop:"∝",vrtri:"⊳",vscr:"\uD835\uDCCB",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",vzigzag:"⦚",wcirc:"ŵ",wedbar:"⩟",wedge:"∧",wedgeq:"≙",weierp:"℘",wfr:"\uD835\uDD34",wopf:"\uD835\uDD68",wp:"℘",wr:"≀",wreath:"≀",wscr:"\uD835\uDCCC",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",xfr:"\uD835\uDD35",xhArr:"⟺",xharr:"⟷",xi:"ξ",xlArr:"⟸",xlarr:"⟵",xmap:"⟼",xnis:"⋻",xodot:"⨀",xopf:"\uD835\uDD69",xoplus:"⨁",xotime:"⨂",xrArr:"⟹",xrarr:"⟶",xscr:"\uD835\uDCCD",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",yacute:"\xfd",yacy:"я",ycirc:"ŷ",ycy:"ы",yen:"\xa5",yfr:"\uD835\uDD36",yicy:"ї",yopf:"\uD835\uDD6A",yscr:"\uD835\uDCCE",yucy:"ю",yuml:"\xff",zacute:"ź",zcaron:"ž",zcy:"з",zdot:"ż",zeetrf:"ℨ",zeta:"ζ",zfr:"\uD835\uDD37",zhcy:"ж",zigrarr:"⇝",zopf:"\uD835\uDD6B",zscr:"\uD835\uDCCF",zwj:"‍",zwnj:"‌"},ts={}.hasOwnProperty;function tu(e){return!!ts.call(ta,e)&&ta[e]}let tc={name:"characterReference",tokenize:function(e,t,n){let r,l,i=this,o=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),a};function a(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),s):(e.enter("characterReferenceValue"),r=31,l=eT,u(t))}function s(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,l=eL,u):(e.enter("characterReferenceValue"),r=7,l=eq,u(t))}function u(a){if(59===a&&o){let r=e.exit("characterReferenceValue");return l!==eT||tu(i.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(a),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(a)}return l(a)&&o++<r?(e.consume(a),u):n(a)}}},tp={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return eP(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}},td={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),ej(e,t,"linePrefix")}}};function tf(e,t,n){let r=[],l=-1;for(;++l<e.length;){let i=e[l].resolveAll;i&&!r.includes(i)&&(t=i(t,n),r.push(i))}return t}let th={name:"labelEnd",resolveAll:function(e){let t=-1,n=[];for(;++t<e.length;){let r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){let e="labelImage"===r.type?4:2;r.type="data",t+=e}}return e.length!==n.length&&eb(e,0,e.length,n),e},resolveTo:function(e,t){let n,r,l,i,o=e.length,a=0;for(;o--;)if(n=e[o][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[o][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(l){if("enter"===e[o][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=o,"labelLink"!==n.type)){a=2;break}}else"labelEnd"===n.type&&(l=o);let s={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},u={type:"label",start:{...e[r][1].start},end:{...e[l][1].end}},c={type:"labelText",start:{...e[r+a+2][1].end},end:{...e[l-2][1].start}};return i=ex(i=[["enter",s,t],["enter",u,t]],e.slice(r+1,r+a+3)),i=ex(i,[["enter",c,t]]),i=ex(i,tf(t.parser.constructs.insideSpan.null,e.slice(r+a+4,l-3),t)),i=ex(i,[["exit",c,t],e[l-2],e[l-1],["exit",u,t]]),i=ex(i,e.slice(l+1)),i=ex(i,[["exit",s,t]]),eb(e,r,e.length,i),e},tokenize:function(e,t,n){let r,l,i=this,o=i.events.length;for(;o--;)if(("labelImage"===i.events[o][1].type||"labelLink"===i.events[o][1].type)&&!i.events[o][1]._balanced){r=i.events[o][1];break}return function(t){return r?r._inactive?c(t):(l=i.parser.defined.includes(e9(i.sliceSerialize({start:r.end,end:i.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),a):n(t)};function a(t){return 40===t?e.attempt(tm,u,l?u:c)(t):91===t?e.attempt(tg,u,l?s:c)(t):l?u(t):c(t)}function s(t){return e.attempt(ty,u,c)(t)}function u(e){return t(e)}function c(e){return r._balanced=!0,n(e)}}},tm={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return eN(t)?e6(e,l)(t):l(t)}function l(t){return 41===t?u(t):e2(e,i,o,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function i(t){return eN(t)?e6(e,a)(t):u(t)}function o(e){return n(e)}function a(t){return 34===t||39===t||40===t?e3(e,s,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):u(t)}function s(t){return eN(t)?e6(e,u)(t):u(t)}function u(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},tg={tokenize:function(e,t,n){let r=this;return function(t){return e4.call(r,e,l,i,"reference","referenceMarker","referenceString")(t)};function l(e){return r.parser.defined.includes(e9(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function i(e){return n(e)}}},ty={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}},tb={name:"labelStartImage",resolveAll:th.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),l};function l(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),i):n(t)}function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};function tx(e){return null===e||eN(e)||eO(e)?1:eR(e)?2:void 0}let tv={name:"attention",resolveAll:function(e,t){let n,r,l,i,o,a,s,u,c=-1;for(;++c<e.length;)if("enter"===e[c][0]&&"attentionSequence"===e[c][1].type&&e[c][1]._close){for(n=c;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[c][1]).charCodeAt(0)){if((e[n][1]._close||e[c][1]._open)&&(e[c][1].end.offset-e[c][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[c][1].end.offset-e[c][1].start.offset)%3))continue;a=e[n][1].end.offset-e[n][1].start.offset>1&&e[c][1].end.offset-e[c][1].start.offset>1?2:1;let p={...e[n][1].end},d={...e[c][1].start};tk(p,-a),tk(d,a),i={type:a>1?"strongSequence":"emphasisSequence",start:p,end:{...e[n][1].end}},o={type:a>1?"strongSequence":"emphasisSequence",start:{...e[c][1].start},end:d},l={type:a>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[c][1].start}},r={type:a>1?"strong":"emphasis",start:{...i.start},end:{...o.end}},e[n][1].end={...i.start},e[c][1].start={...o.end},s=[],e[n][1].end.offset-e[n][1].start.offset&&(s=ex(s,[["enter",e[n][1],t],["exit",e[n][1],t]])),s=ex(s,[["enter",r,t],["enter",i,t],["exit",i,t],["enter",l,t]]),s=ex(s,tf(t.parser.constructs.insideSpan.null,e.slice(n+1,c),t)),s=ex(s,[["exit",l,t],["enter",o,t],["exit",o,t],["exit",r,t]]),e[c][1].end.offset-e[c][1].start.offset?(u=2,s=ex(s,[["enter",e[c][1],t],["exit",e[c][1],t]])):u=0,eb(e,n-1,c-n+3,s),c=n+s.length-u-2;break}}for(c=-1;++c<e.length;)"attentionSequence"===e[c][1].type&&(e[c][1].type="data");return e},tokenize:function(e,t){let n,r=this.parser.constructs.attentionMarkers.null,l=this.previous,i=tx(l);return function(o){return n=o,e.enter("attentionSequence"),function o(a){if(a===n)return e.consume(a),o;let s=e.exit("attentionSequence"),u=tx(a),c=!u||2===u&&i||r.includes(a),p=!i||2===i&&u||r.includes(l);return s._open=!!(42===n?c:c&&(i||!p)),s._close=!!(42===n?p:p&&(u||!c)),t(a)}(o)}}};function tk(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}let tw={name:"labelStartLink",resolveAll:th.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),l};function l(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}},tS={42:eZ,43:eZ,45:eZ,48:eZ,49:eZ,50:eZ,51:eZ,52:eZ,53:eZ,54:eZ,55:eZ,56:eZ,57:eZ,62:e1},tE={91:{name:"definition",tokenize:function(e,t,n){let r,l=this;return function(t){var r;return e.enter("definition"),r=t,e4.call(l,e,i,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(r)};function i(t){return(r=e9(l.sliceSerialize(l.events[l.events.length-1][1]).slice(1,-1)),58===t)?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),o):n(t)}function o(t){return eN(t)?e6(e,a)(t):a(t)}function a(t){return e2(e,s,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function s(t){return e.attempt(e5,u,u)(t)}function u(t){return eI(t)?ej(e,c,"whitespace")(t):c(t)}function c(i){return null===i||eA(i)?(e.exit("definition"),l.parser.defined.push(r),t(i)):n(i)}}}},tT={[-2]:e8,[-1]:e8,32:e8},tC={35:{name:"headingAtx",resolve:function(e,t){let n,r,l=e.length-2,i=3;return"whitespace"===e[3][1].type&&(i+=2),l-2>i&&"whitespace"===e[l][1].type&&(l-=2),"atxHeadingSequence"===e[l][1].type&&(i===l-1||l-4>i&&"whitespace"===e[l-2][1].type)&&(l-=i+1===l?2:4),l>i&&(n={type:"atxHeadingText",start:e[i][1].start,end:e[l][1].end},r={type:"chunkText",start:e[i][1].start,end:e[l][1].end,contentType:"text"},eb(e,i,l-i+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e},tokenize:function(e,t,n){let r=0;return function(l){var i;return e.enter("atxHeading"),i=l,e.enter("atxHeadingSequence"),function l(i){return 35===i&&r++<6?(e.consume(i),l):null===i||eN(i)?(e.exit("atxHeadingSequence"),function n(r){return 35===r?(e.enter("atxHeadingSequence"),function t(r){return 35===r?(e.consume(r),t):(e.exit("atxHeadingSequence"),n(r))}(r)):null===r||eA(r)?(e.exit("atxHeading"),t(r)):eI(r)?ej(e,n,"whitespace")(r):(e.enter("atxHeadingText"),function t(r){return null===r||35===r||eN(r)?(e.exit("atxHeadingText"),n(r)):(e.consume(r),t)}(r))}(i)):n(i)}(i)}}},42:eJ,45:[te,eJ],60:{concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e},tokenize:function(e,t,n){let r,l,i,o,a,s=this;return function(t){var n;return n=t,e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(n),u};function u(o){return 33===o?(e.consume(o),c):47===o?(e.consume(o),l=!0,f):63===o?(e.consume(o),r=3,s.interrupt?t:N):eE(o)?(e.consume(o),i=String.fromCharCode(o),h):n(o)}function c(l){return 45===l?(e.consume(l),r=2,p):91===l?(e.consume(l),r=5,o=0,d):eE(l)?(e.consume(l),r=4,s.interrupt?t:N):n(l)}function p(r){return 45===r?(e.consume(r),s.interrupt?t:N):n(r)}function d(r){let l="CDATA[";return r===l.charCodeAt(o++)?(e.consume(r),o===l.length)?s.interrupt?t:E:d:n(r)}function f(t){return eE(t)?(e.consume(t),i=String.fromCharCode(t),h):n(t)}function h(o){if(null===o||47===o||62===o||eN(o)){let a=47===o,u=i.toLowerCase();return!a&&!l&&tn.includes(u)?(r=1,s.interrupt?t(o):E(o)):tt.includes(i.toLowerCase())?(r=6,a)?(e.consume(o),m):s.interrupt?t(o):E(o):(r=7,s.interrupt&&!s.parser.lazy[s.now().line]?n(o):l?function t(n){return eI(n)?(e.consume(n),t):w(n)}(o):g(o))}return 45===o||eT(o)?(e.consume(o),i+=String.fromCharCode(o),h):n(o)}function m(r){return 62===r?(e.consume(r),s.interrupt?t:E):n(r)}function g(t){return 47===t?(e.consume(t),w):58===t||95===t||eE(t)?(e.consume(t),y):eI(t)?(e.consume(t),g):w(t)}function y(t){return 45===t||46===t||58===t||95===t||eT(t)?(e.consume(t),y):b(t)}function b(t){return 61===t?(e.consume(t),x):eI(t)?(e.consume(t),b):g(t)}function x(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),a=t,v):eI(t)?(e.consume(t),x):function t(n){return null===n||34===n||39===n||47===n||60===n||61===n||62===n||96===n||eN(n)?b(n):(e.consume(n),t)}(t)}function v(t){return t===a?(e.consume(t),a=null,k):null===t||eA(t)?n(t):(e.consume(t),v)}function k(e){return 47===e||62===e||eI(e)?g(e):n(e)}function w(t){return 62===t?(e.consume(t),S):n(t)}function S(t){return null===t||eA(t)?E(t):eI(t)?(e.consume(t),S):n(t)}function E(t){return 45===t&&2===r?(e.consume(t),q):60===t&&1===r?(e.consume(t),L):62===t&&4===r?(e.consume(t),I):63===t&&3===r?(e.consume(t),N):93===t&&5===r?(e.consume(t),A):eA(t)&&(6===r||7===r)?(e.exit("htmlFlowData"),e.check(tr,R,T)(t)):null===t||eA(t)?(e.exit("htmlFlowData"),T(t)):(e.consume(t),E)}function T(t){return e.check(tl,C,R)(t)}function C(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),D}function D(t){return null===t||eA(t)?T(t):(e.enter("htmlFlowData"),E(t))}function q(t){return 45===t?(e.consume(t),N):E(t)}function L(t){return 47===t?(e.consume(t),i="",P):E(t)}function P(t){if(62===t){let n=i.toLowerCase();return tn.includes(n)?(e.consume(t),I):E(t)}return eE(t)&&i.length<8?(e.consume(t),i+=String.fromCharCode(t),P):E(t)}function A(t){return 93===t?(e.consume(t),N):E(t)}function N(t){return 62===t?(e.consume(t),I):45===t&&2===r?(e.consume(t),N):E(t)}function I(t){return null===t||eA(t)?(e.exit("htmlFlowData"),R(t)):(e.consume(t),I)}function R(n){return e.exit("htmlFlow"),t(n)}}},61:te,95:eJ,96:to,126:to},tD={38:tc,92:tp},tq={[-5]:td,[-4]:td,[-3]:td,33:tb,38:tc,42:tv,60:[{name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),l};function l(t){return eE(t)?(e.consume(t),i):64===t?n(t):a(t)}function i(t){return 43===t||45===t||46===t||eT(t)?(r=1,function t(n){return 58===n?(e.consume(n),r=0,o):(43===n||45===n||46===n||eT(n))&&r++<32?(e.consume(n),t):(r=0,a(n))}(t)):a(t)}function o(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||eD(r)?n(r):(e.consume(r),o)}function a(t){return 64===t?(e.consume(t),s):eC(t)?(e.consume(t),a):n(t)}function s(l){return eT(l)?function l(i){return 46===i?(e.consume(i),r=0,s):62===i?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(i),e.exit("autolinkMarker"),e.exit("autolink"),t):function t(i){if((45===i||eT(i))&&r++<63){let n=45===i?t:l;return e.consume(i),n}return n(i)}(i)}(l):n(l)}}},{name:"htmlText",tokenize:function(e,t,n){let r,l,i,o=this;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),a};function a(t){return 33===t?(e.consume(t),s):47===t?(e.consume(t),v):63===t?(e.consume(t),b):eE(t)?(e.consume(t),w):n(t)}function s(t){return 45===t?(e.consume(t),u):91===t?(e.consume(t),l=0,f):eE(t)?(e.consume(t),y):n(t)}function u(t){return 45===t?(e.consume(t),d):n(t)}function c(t){return null===t?n(t):45===t?(e.consume(t),p):eA(t)?(i=c,P(t)):(e.consume(t),c)}function p(t){return 45===t?(e.consume(t),d):c(t)}function d(e){return 62===e?L(e):45===e?p(e):c(e)}function f(t){let r="CDATA[";return t===r.charCodeAt(l++)?(e.consume(t),l===r.length?h:f):n(t)}function h(t){return null===t?n(t):93===t?(e.consume(t),m):eA(t)?(i=h,P(t)):(e.consume(t),h)}function m(t){return 93===t?(e.consume(t),g):h(t)}function g(t){return 62===t?L(t):93===t?(e.consume(t),g):h(t)}function y(t){return null===t||62===t?L(t):eA(t)?(i=y,P(t)):(e.consume(t),y)}function b(t){return null===t?n(t):63===t?(e.consume(t),x):eA(t)?(i=b,P(t)):(e.consume(t),b)}function x(e){return 62===e?L(e):b(e)}function v(t){return eE(t)?(e.consume(t),k):n(t)}function k(t){return 45===t||eT(t)?(e.consume(t),k):function t(n){return eA(n)?(i=t,P(n)):eI(n)?(e.consume(n),t):L(n)}(t)}function w(t){return 45===t||eT(t)?(e.consume(t),w):47===t||62===t||eN(t)?S(t):n(t)}function S(t){return 47===t?(e.consume(t),L):58===t||95===t||eE(t)?(e.consume(t),E):eA(t)?(i=S,P(t)):eI(t)?(e.consume(t),S):L(t)}function E(t){return 45===t||46===t||58===t||95===t||eT(t)?(e.consume(t),E):function t(n){return 61===n?(e.consume(n),T):eA(n)?(i=t,P(n)):eI(n)?(e.consume(n),t):S(n)}(t)}function T(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),r=t,C):eA(t)?(i=T,P(t)):eI(t)?(e.consume(t),T):(e.consume(t),D)}function C(t){return t===r?(e.consume(t),r=void 0,q):null===t?n(t):eA(t)?(i=C,P(t)):(e.consume(t),C)}function D(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||eN(t)?S(t):(e.consume(t),D)}function q(e){return 47===e||62===e||eN(e)?S(e):n(e)}function L(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function P(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),A}function A(t){return eI(t)?ej(e,N,"linePrefix",o.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):N(t)}function N(t){return e.enter("htmlTextData"),i(t)}}}],91:tw,92:[{name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return eA(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}},tp],93:th,95:tv,96:{name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,l=3;if(("lineEnding"===e[3][1].type||"space"===e[l][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(t=l;++t<r;)if("codeTextData"===e[t][1].type){e[l][1].type="codeTextPadding",e[r][1].type="codeTextPadding",l+=2,r-=2;break}}for(t=l-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):(t===r||"lineEnding"===e[t][1].type)&&(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,l,i=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),function t(n){return 96===n?(e.consume(n),i++,t):(e.exit("codeTextSequence"),o(n))}(t)};function o(s){return null===s?n(s):32===s?(e.enter("space"),e.consume(s),e.exit("space"),o):96===s?(l=e.enter("codeTextSequence"),r=0,function n(o){return 96===o?(e.consume(o),r++,n):r===i?(e.exit("codeTextSequence"),e.exit("codeText"),t(o)):(l.type="codeTextData",a(o))}(s)):eA(s)?(e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),o):(e.enter("codeTextData"),a(s))}function a(t){return null===t||32===t||96===t||eA(t)?(e.exit("codeTextData"),o(t)):(e.consume(t),a)}}}},tL={null:[tv,eG]},tP={null:[42,95]},tA={null:[]},tN=/[\0\t\n\r]/g;function tI(e,t){let n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(65535&n)==65535||(65535&n)==65534||n>1114111?"�":String.fromCodePoint(n)}let tR=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function tO(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){let e=n.charCodeAt(1),t=120===e||88===e;return tI(n.slice(t?2:1),t?16:10)}return tu(n)||e}let t_={}.hasOwnProperty;function tj(e){return{line:e.line,column:e.column,offset:e.offset}}function tM(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+Q({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+Q({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+Q({start:t.start,end:t.end})+") is still open")}function tz(e){let t=this;t.parser=function(n){var r,i;let o,a,s,u;return"string"!=typeof(r={...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})&&(i=r,r=void 0),(function(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(y),autolinkProtocol:u,autolinkEmail:u,atxHeading:r(h),blockQuote:r(function(){return{type:"blockquote",children:[]}}),characterEscape:u,characterReference:u,codeFenced:r(f),codeFencedFenceInfo:l,codeFencedFenceMeta:l,codeIndented:r(f,l),codeText:r(function(){return{type:"inlineCode",value:""}},l),codeTextData:u,data:u,codeFlowValue:u,definition:r(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:l,definitionLabelString:l,definitionTitleString:l,emphasis:r(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:r(m),hardBreakTrailing:r(m),htmlFlow:r(g,l),htmlFlowData:u,htmlText:r(g,l),htmlTextData:u,image:r(function(){return{type:"image",title:null,url:"",alt:null}}),label:l,link:r(y),listItem:r(function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}),listItemValue:function(e){this.data.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0)},listOrdered:r(b,function(){this.data.expectingFirstListItemValue=!0}),listUnordered:r(b),paragraph:r(function(){return{type:"paragraph",children:[]}}),reference:function(){this.data.referenceType="collapsed"},referenceString:l,resourceDestinationString:l,resourceTitleString:l,setextHeading:r(h),strong:r(function(){return{type:"strong",children:[]}}),thematicBreak:r(function(){return{type:"thematicBreak"}})},exit:{atxHeading:o(),atxHeadingSequence:function(e){let t=this.stack[this.stack.length-1];t.depth||(t.depth=this.sliceSerialize(e).length)},autolink:o(),autolinkEmail:function(e){c.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){c.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:o(),characterEscapeValue:c,characterReferenceMarkerHexadecimal:d,characterReferenceMarkerNumeric:d,characterReferenceValue:function(e){let t,n=this.sliceSerialize(e),r=this.data.characterReferenceType;r?(t=tI(n,"characterReferenceMarkerNumeric"===r?10:16),this.data.characterReferenceType=void 0):t=tu(n);let l=this.stack[this.stack.length-1];l.value+=t},characterReference:function(e){this.stack.pop().position.end=tj(e.end)},codeFenced:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){let e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){let e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:c,codeIndented:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}),codeText:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),codeTextData:c,data:c,definition:o(),definitionDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=e9(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:o(),hardBreakEscape:o(p),hardBreakTrailing:o(p),htmlFlow:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlFlowData:c,htmlText:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlTextData:c,image:o(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),label:function(){let e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];this.data.inReference=!0,"link"===n.type?n.children=e.children:n.alt=t},labelText:function(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=t.replace(tR,tO),n.identifier=e9(t).toLowerCase()},lineEnding:function(e){let n=this.stack[this.stack.length-1];if(this.data.atHardBreak){n.children[n.children.length-1].position.end=tj(e.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(u.call(this,e),c.call(this,e))},link:o(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),listItem:o(),listOrdered:o(),listUnordered:o(),paragraph:o(),referenceString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=e9(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){this.data.inReference=void 0},setextHeading:o(function(){this.data.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:o(),thematicBreak:o()}};!function e(t,n){let r=-1;for(;++r<n.length;){let l=n[r];Array.isArray(l)?e(t,l):function(e,t){let n;for(n in t)if(t_.call(t,n))switch(n){case"canContainEols":{let r=t[n];r&&e[n].push(...r);break}case"transforms":{let r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{let r=t[n];r&&Object.assign(e[n],r)}}}(t,l)}}(t,(e||{}).mdastExtensions||[]);let n={};return function(e){let r={type:"root",children:[]},o={stack:[r],tokenStack:[],config:t,enter:i,exit:a,buffer:l,resume:s,data:n},u=[],c=-1;for(;++c<e.length;)("listOrdered"===e[c][1].type||"listUnordered"===e[c][1].type)&&("enter"===e[c][0]?u.push(c):c=function(e,t,n){let r,l,i,o,a=t-1,s=-1,u=!1;for(;++a<=n;){let t=e[a];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?s++:s--,o=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||o||s||i||(i=a),o=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:o=void 0}if(!s&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===s&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let o=a;for(l=void 0;o--;){let t=e[o];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;l&&(e[l][1].type="lineEndingBlank",u=!0),t[1].type="lineEnding",l=o}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}i&&(!l||i<l)&&(r._spread=!0),r.end=Object.assign({},l?e[l][1].start:t[1].end),e.splice(l||a,0,["exit",r,t[2]]),a++,n++}if("listItemPrefix"===t[1].type){let l={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=l,e.splice(a,0,["enter",l,t[2]]),a++,n++,i=void 0,o=!0}}}return e[t][1]._spread=u,n}(e,u.pop(),c));for(c=-1;++c<e.length;){let n=t[e[c][0]];t_.call(n,e[c][1].type)&&n[e[c][1].type].call(Object.assign({sliceSerialize:e[c][2].sliceSerialize},o),e[c][1])}if(o.tokenStack.length>0){let e=o.tokenStack[o.tokenStack.length-1];(e[1]||tM).call(o,void 0,e[0])}for(r.position={start:tj(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:tj(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},c=-1;++c<t.transforms.length;)r=t.transforms[c](r)||r;return r};function r(e,t){return function(n){i.call(this,e(n),n),t&&t.call(this,n)}}function l(){this.stack.push({type:"fragment",children:[]})}function i(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:tj(t.start),end:void 0}}function o(e){return function(t){e&&e.call(this,t),a.call(this,t)}}function a(e,t){let n=this.stack.pop(),r=this.tokenStack.pop();if(r)r[0].type!==e.type&&(t?t.call(this,e,r[0]):(r[1]||tM).call(this,e,r[0]));else throw Error("Cannot close `"+e.type+"` ("+Q({start:e.start,end:e.end})+"): it’s not open");n.position.end=tj(e.end)}function s(){var e;return eg(this.stack.pop(),"boolean"!=typeof em.includeImageAlt||em.includeImageAlt,"boolean"!=typeof em.includeHtml||em.includeHtml)}function u(e){let t=this.stack[this.stack.length-1].children,n=t[t.length-1];n&&"text"===n.type||((n={type:"text",value:""}).position={start:tj(e.start),end:void 0},t.push(n)),this.stack.push(n)}function c(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=tj(e.end)}function p(){this.data.atHardBreak=!0}function d(e){this.data.characterReferenceType=e.type}function f(){return{type:"code",lang:null,meta:null,value:""}}function h(){return{type:"heading",depth:0,children:[]}}function m(){return{type:"break"}}function g(){return{type:"html",value:""}}function y(){return{type:"link",title:null,url:"",children:[]}}function b(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}})(i)(function(e){for(;!ew(e););return e}((function(e){let t={constructs:function(e){let t={},n=-1;for(;++n<e.length;)!function(e,t){let n;for(n in t){let r,l=(eS.call(e,n)?e[n]:void 0)||(e[n]={}),i=t[n];if(i)for(r in i){eS.call(l,r)||(l[r]=[]);let e=i[r];!function(e,t){let n=-1,r=[];for(;++n<t.length;)("after"===t[n].add?e:r).push(t[n]);eb(e,0,0,r)}(l[r],Array.isArray(e)?e:e?[e]:[])}}}(t,e[n]);return t}([l,...(e||{}).extensions||[]]),content:n(eM),defined:[],document:n(ez),flow:n(eH),lazy:{},string:n(eY),text:n(eK)};return t;function n(e){return function(n){return function(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},l={},i=[],o=[],a=[],s={attempt:h(function(e,t){m(e,t.from)}),check:h(f),consume:function(e){eA(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,g()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===o[r._index].length&&(r._bufferIndex=-1,r._index++)),u.previous=e},enter:function(e,t){let n=t||{};return n.type=e,n.start=d(),u.events.push(["enter",n,u]),a.push(n),n},exit:function(e){let t=a.pop();return t.end=d(),u.events.push(["exit",t,u]),t},interrupt:h(f,{interrupt:!0})},u={code:null,containerState:{},defineSkip:function(e){l[e.line]=e.column,g()},events:[],now:d,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n,r=-1,l=[];for(;++r<e.length;){let i,o=e[r];if("string"==typeof o)i=o;else switch(o){case -5:i="\r";break;case -4:i="\n";break;case -3:i="\r\n";break;case -2:i=t?" ":"	";break;case -1:if(!t&&n)continue;i=" ";break;default:i=String.fromCharCode(o)}n=-2===o,l.push(i)}return l.join("")}(p(e),t)},sliceStream:p,write:function(e){return(o=ex(o,e),function(){let e;for(;r._index<o.length;){let n=o[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;){var t;t=n.charCodeAt(r._bufferIndex),c=c(t)}else c=c(n)}}(),null!==o[o.length-1])?[]:(m(t,0),u.events=tf(i,u.events,u),u.events)}},c=t.tokenize.call(u,s);return t.resolveAll&&i.push(t),u;function p(e){return function(e,t){let n,r=t.start._index,l=t.start._bufferIndex,i=t.end._index,o=t.end._bufferIndex;if(r===i)n=[e[r].slice(l,o)];else{if(n=e.slice(r,i),l>-1){let e=n[0];"string"==typeof e?n[0]=e.slice(l):n.shift()}o>0&&n.push(e[i].slice(0,o))}return n}(o,e)}function d(){let{_bufferIndex:e,_index:t,line:n,column:l,offset:i}=r;return{_bufferIndex:e,_index:t,line:n,column:l,offset:i}}function f(e,t){t.restore()}function h(e,t){return function(n,l,i){var o;let c,p,f,h;return Array.isArray(n)?m(n):"tokenize"in n?m([n]):(o=n,function(e){let t=null!==e&&o[e],n=null!==e&&o.null;return m([...Array.isArray(t)?t:t?[t]:[],...Array.isArray(n)?n:n?[n]:[]])(e)});function m(e){return(c=e,p=0,0===e.length)?i:y(e[p])}function y(e){return function(n){return(h=function(){let e=d(),t=u.previous,n=u.currentConstruct,l=u.events.length,i=Array.from(a);return{from:l,restore:function(){r=e,u.previous=t,u.currentConstruct=n,u.events.length=l,a=i,g()}}}(),f=e,e.partial||(u.currentConstruct=e),e.name&&u.parser.constructs.disable.null.includes(e.name))?x(n):e.tokenize.call(t?Object.assign(Object.create(u),t):u,s,b,x)(n)}}function b(t){return e(f,h),l}function x(e){return(h.restore(),++p<c.length)?y(c[p]):i}}}function m(e,t){e.resolveAll&&!i.includes(e)&&i.push(e),e.resolve&&eb(u.events,t,u.events.length-t,e.resolve(u.events.slice(t),u)),e.resolveTo&&(u.events=e.resolveTo(u.events,u))}function g(){r.line in l&&r.column<2&&(r.column=l[r.line],r.offset+=l[r.line]-1)}}(t,e,n)}}})(i).document().write((a=1,s="",u=!0,function(e,t,n){let r,l,i,c,p,d=[];for(e=s+("string"==typeof e?e.toString():new TextDecoder(t||void 0).decode(e)),i=0,s="",u&&(65279===e.charCodeAt(0)&&i++,u=void 0);i<e.length;){if(tN.lastIndex=i,c=(r=tN.exec(e))&&void 0!==r.index?r.index:e.length,p=e.charCodeAt(c),!r){s=e.slice(i);break}if(10===p&&i===c&&o)d.push(-3),o=void 0;else switch(o&&(d.push(-5),o=void 0),i<c&&(d.push(e.slice(i,c)),a+=c-i),p){case 0:d.push(65533),a++;break;case 9:for(l=4*Math.ceil(a/4),d.push(-2);a++<l;)d.push(-1);break;case 10:d.push(-4),a=1;break;default:o=!0,a=1}i=c+1}return n&&(o&&d.push(-5),s&&d.push(s),d.push(null)),d})(n,r,!0))))}}let tF="object"==typeof self?self:globalThis,tB=(e,t)=>{let n=(t,n)=>(e.set(n,t),t),r=l=>{if(e.has(l))return e.get(l);let[i,o]=t[l];switch(i){case 0:case -1:return n(o,l);case 1:{let e=n([],l);for(let t of o)e.push(r(t));return e}case 2:{let e=n({},l);for(let[t,n]of o)e[r(t)]=r(n);return e}case 3:return n(new Date(o),l);case 4:{let{source:e,flags:t}=o;return n(new RegExp(e,t),l)}case 5:{let e=n(new Map,l);for(let[t,n]of o)e.set(r(t),r(n));return e}case 6:{let e=n(new Set,l);for(let t of o)e.add(r(t));return e}case 7:{let{name:e,message:t}=o;return n(new tF[e](t),l)}case 8:return n(BigInt(o),l);case"BigInt":return n(Object(BigInt(o)),l);case"ArrayBuffer":return n(new Uint8Array(o).buffer,o);case"DataView":{let{buffer:e}=new Uint8Array(o);return n(new DataView(e),o)}}return n(new tF[i](o),l)};return r},tU=e=>tB(new Map,e)(0),{toString:tV}={},{keys:tH}=Object,tG=e=>{let t=typeof e;if("object"!==t||!e)return[0,t];let n=tV.call(e).slice(8,-1);switch(n){case"Array":return[1,""];case"Object":return[2,""];case"Date":return[3,""];case"RegExp":return[4,""];case"Map":return[5,""];case"Set":return[6,""];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},tY=([e,t])=>0===e&&("function"===t||"symbol"===t),tK=(e,t,n,r)=>{let l=(e,t)=>{let l=r.push(e)-1;return n.set(t,l),l},i=r=>{if(n.has(r))return n.get(r);let[o,a]=tG(r);switch(o){case 0:{let t=r;switch(a){case"bigint":o=8,t=r.toString();break;case"function":case"symbol":if(e)throw TypeError("unable to serialize "+a);t=null;break;case"undefined":return l([-1],r)}return l([o,t],r)}case 1:{if(a){let e=r;return"DataView"===a?e=new Uint8Array(r.buffer):"ArrayBuffer"===a&&(e=new Uint8Array(r)),l([a,[...e]],r)}let e=[],t=l([o,e],r);for(let t of r)e.push(i(t));return t}case 2:{if(a)switch(a){case"BigInt":return l([a,r.toString()],r);case"Boolean":case"Number":case"String":return l([a,r.valueOf()],r)}if(t&&"toJSON"in r)return i(r.toJSON());let n=[],s=l([o,n],r);for(let t of tH(r))(e||!tY(tG(r[t])))&&n.push([i(t),i(r[t])]);return s}case 3:return l([o,r.toISOString()],r);case 4:{let{source:e,flags:t}=r;return l([o,{source:e,flags:t}],r)}case 5:{let t=[],n=l([o,t],r);for(let[n,l]of r)(e||!(tY(tG(n))||tY(tG(l))))&&t.push([i(n),i(l)]);return n}case 6:{let t=[],n=l([o,t],r);for(let n of r)(e||!tY(tG(n)))&&t.push(i(n));return n}}let{message:s}=r;return l([o,{name:a,message:s}],r)};return i},tW=(e,{json:t,lossy:n}={})=>{let r=[];return tK(!(t||n),!!t,new Map,r)(e),r},tX="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?tU(tW(e,t)):structuredClone(e):(e,t)=>tU(tW(e,t));function tQ(e){let t=[],n=-1,r=0,l=0;for(;++n<e.length;){let i=e.charCodeAt(n),o="";if(37===i&&eT(e.charCodeAt(n+1))&&eT(e.charCodeAt(n+2)))l=2;else if(i<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(i))||(o=String.fromCharCode(i));else if(i>55295&&i<57344){let t=e.charCodeAt(n+1);i<56320&&t>56319&&t<57344?(o=String.fromCharCode(i,t),l=1):o="�"}else o=String.fromCharCode(i);o&&(t.push(e.slice(r,n),encodeURIComponent(o)),r=n+l+1,o=""),l&&(n+=l,l=0)}return t.join("")+e.slice(r)}function tJ(e,t){let n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function tZ(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}let t$=function(e){var t,n;if(null==e)return t1;if("function"==typeof e)return t0(e);if("object"==typeof e){return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=t$(e[n]);return t0(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):(t=e,t0(function(e){let n;for(n in t)if(e[n]!==t[n])return!1;return!0}))}if("string"==typeof e){return n=e,t0(function(e){return e&&e.type===n})}throw Error("Expected function, string, or object as test")};function t0(e){return function(t,n,r){return!!(function(e){return null!==e&&"object"==typeof e&&"type"in e}(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function t1(){return!0}let t2=[];function t4(e,t,n,r){let l,i,o,a;"function"==typeof t&&"function"!=typeof n?(i=void 0,o=t,l=n):(i=t,o=n,l=r);var s=i,u=function(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return o(e,r,n)},c=l;"function"==typeof s&&"function"!=typeof u?(c=u,u=s):a=s;let p=t$(a),d=c?-1:1;(function e(t,n,r){let l=t&&"object"==typeof t?t:{};if("string"==typeof l.type){let e="string"==typeof l.tagName?l.tagName:"string"==typeof l.name?l.name:void 0;Object.defineProperty(i,"name",{value:"node (\x1b[33m"+t.type+(e?"<"+e+">":"")+"\x1b[39m)"})}return i;function i(){var l;let i,o,a,f=t2;if((!s||p(t,n,r[r.length-1]||void 0))&&!1===(f=Array.isArray(l=u(t,r))?l:"number"==typeof l?[!0,l]:null==l?t2:[l])[0])return f;if("children"in t&&t.children&&t.children&&"skip"!==f[0])for(o=(c?t.children.length:-1)+d,a=r.concat(t);o>-1&&o<t.children.length;){if(!1===(i=e(t.children[o],o,a)())[0])return i;o="number"==typeof i[1]?i[1]:o+d}return f}})(e,void 0,[])()}function t3(e,t){let n=t.referenceType,r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];let l=e.all(t),i=l[0];i&&"text"===i.type?i.value="["+i.value:l.unshift({type:"text",value:"["});let o=l[l.length-1];return o&&"text"===o.type?o.value+=r:l.push({type:"text",value:r}),l}function t6(e){let t=e.spread;return null==t?e.children.length>1:t}function t9(e,t,n){let r=0,l=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(l-1);for(;9===t||32===t;)l--,t=e.codePointAt(l-1)}return l>r?e.slice(r,l):""}let t5={blockquote:function(e,t){let n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){let n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){let n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let l={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(l.data={meta:t.meta}),e.patch(t,l),l={type:"element",tagName:"pre",properties:{},children:[l=e.applyData(t,l)]},e.patch(t,l),l},delete:function(e,t){let n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){let n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){let n,r="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",l=String(t.identifier).toUpperCase(),i=tQ(l.toLowerCase()),o=e.footnoteOrder.indexOf(l),a=e.footnoteCounts.get(l);void 0===a?(a=0,e.footnoteOrder.push(l),n=e.footnoteOrder.length):n=o+1,a+=1,e.footnoteCounts.set(l,a);let s={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+i,id:r+"fnref-"+i+(a>1?"-"+a:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(n)}]};e.patch(t,s);let u={type:"element",tagName:"sup",properties:{},children:[s]};return e.patch(t,u),e.applyData(t,u)},heading:function(e,t){let n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){let n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return t3(e,t);let l={src:tQ(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(l.title=r.title);let i={type:"element",tagName:"img",properties:l,children:[]};return e.patch(t,i),e.applyData(t,i)},image:function(e,t){let n={src:tQ(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){let n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);let r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return t3(e,t);let l={href:tQ(r.url||"")};null!==r.title&&void 0!==r.title&&(l.title=r.title);let i={type:"element",tagName:"a",properties:l,children:e.all(t)};return e.patch(t,i),e.applyData(t,i)},link:function(e,t){let n={href:tQ(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){let r=e.all(t),l=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;let n=e.children,r=-1;for(;!t&&++r<n.length;)t=t6(n[r])}return t}(n):t6(t),i={},o=[];if("boolean"==typeof t.checked){let e,n=r[0];n&&"element"===n.type&&"p"===n.tagName?e=n:(e={type:"element",tagName:"p",properties:{},children:[]},r.unshift(e)),e.children.length>0&&e.children.unshift({type:"text",value:" "}),e.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),i.className=["task-list-item"]}let a=-1;for(;++a<r.length;){let e=r[a];(l||0!==a||"element"!==e.type||"p"!==e.tagName)&&o.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||l?o.push(e):o.push(...e.children)}let s=r[r.length-1];s&&(l||"element"!==s.type||"p"!==s.tagName)&&o.push({type:"text",value:"\n"});let u={type:"element",tagName:"li",properties:i,children:o};return e.patch(t,u),e.applyData(t,u)},list:function(e,t){let n={},r=e.all(t),l=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++l<r.length;){let e=r[l];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let i={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,i),e.applyData(t,i)},paragraph:function(e,t){let n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){let n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){let n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){let n=e.all(t),r=n.shift(),l=[];if(r){let n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),l.push(n)}if(n.length>0){let r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},i=W(t.children[1]),o=K(t.children[t.children.length-1]);i&&o&&(r.position={start:i,end:o}),l.push(r)}let i={type:"element",tagName:"table",properties:{},children:e.wrap(l,!0)};return e.patch(t,i),e.applyData(t,i)},tableCell:function(e,t){let n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){let r=n?n.children:void 0,l=0===(r?r.indexOf(t):1)?"th":"td",i=n&&"table"===n.type?n.align:void 0,o=i?i.length:t.children.length,a=-1,s=[];for(;++a<o;){let n=t.children[a],r={},o=i?i[a]:void 0;o&&(r.align=o);let u={type:"element",tagName:l,properties:r,children:[]};n&&(u.children=e.all(n),e.patch(n,u),u=e.applyData(n,u)),s.push(u)}let u={type:"element",tagName:"tr",properties:{},children:e.wrap(s,!0)};return e.patch(t,u),e.applyData(t,u)},text:function(e,t){let n={type:"text",value:function(e){let t=String(e),n=/\r?\n|\r/g,r=n.exec(t),l=0,i=[];for(;r;)i.push(t9(t.slice(l,r.index),l>0,!0),r[0]),l=r.index+r[0].length,r=n.exec(t);return i.push(t9(t.slice(l),l>0,!1)),i.join("")}(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){let n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:t8,yaml:t8,definition:t8,footnoteDefinition:t8};function t8(){}let t7={}.hasOwnProperty,ne={};function nt(e,t){e.position&&(t.position=function(e){let t=W(e),n=K(e);if(t&&n)return{start:t,end:n}}(e))}function nn(e,t){let n=t;if(e&&e.data){let t=e.data.hName,r=e.data.hChildren,l=e.data.hProperties;"string"==typeof t&&("element"===n.type?n.tagName=t:n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}),"element"===n.type&&l&&Object.assign(n.properties,tX(l)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function nr(e,t){let n=[],r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function nl(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function ni(e,t){let n=function(e,t){let n=t||ne,r=new Map,l=new Map,i={all:function(e){let t=[];if("children"in e){let n=e.children,r=-1;for(;++r<n.length;){let l=i.one(n[r],e);if(l){if(r&&"break"===n[r-1].type&&(Array.isArray(l)||"text"!==l.type||(l.value=nl(l.value)),!Array.isArray(l)&&"element"===l.type)){let e=l.children[0];e&&"text"===e.type&&(e.value=nl(e.value))}Array.isArray(l)?t.push(...l):t.push(l)}}}return t},applyData:nn,definitionById:r,footnoteById:l,footnoteCounts:new Map,footnoteOrder:[],handlers:{...t5,...n.handlers},one:function(e,t){let n=e.type,r=i.handlers[n];if(t7.call(i.handlers,n)&&r)return r(i,e,t);if(i.options.passThrough&&i.options.passThrough.includes(n)){if("children"in e){let{children:t,...n}=e,r=tX(n);return r.children=i.all(e),r}return tX(e)}return(i.options.unknownHandler||function(e,t){let n=t.data||{},r="value"in t&&!(t7.call(n,"hProperties")||t7.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)})(i,e,t)},options:n,patch:nt,wrap:nr};return t4(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){let t="definition"===e.type?r:l,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),i}(e,t),r=n.one(e,void 0),l=function(e){let t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||tJ,r=e.options.footnoteBackLabel||tZ,l=e.options.footnoteLabel||"Footnotes",i=e.options.footnoteLabelTagName||"h2",o=e.options.footnoteLabelProperties||{className:["sr-only"]},a=[],s=-1;for(;++s<e.footnoteOrder.length;){let l=e.footnoteById.get(e.footnoteOrder[s]);if(!l)continue;let i=e.all(l),o=String(l.identifier).toUpperCase(),u=tQ(o.toLowerCase()),c=0,p=[],d=e.footnoteCounts.get(o);for(;void 0!==d&&++c<=d;){p.length>0&&p.push({type:"text",value:" "});let e="string"==typeof n?n:n(s,c);"string"==typeof e&&(e={type:"text",value:e}),p.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+u+(c>1?"-"+c:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(s,c),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}let f=i[i.length-1];if(f&&"element"===f.type&&"p"===f.tagName){let e=f.children[f.children.length-1];e&&"text"===e.type?e.value+=" ":f.children.push({type:"text",value:" "}),f.children.push(...p)}else i.push(...p);let h={type:"element",tagName:"li",properties:{id:t+"fn-"+u},children:e.wrap(i,!0)};e.patch(l,h),a.push(h)}if(0!==a.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:i,properties:{...tX(o),id:"footnote-label"},children:[{type:"text",value:l}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(a,!0)},{type:"text",value:"\n"}]}}(n),i=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return l&&i.children.push({type:"text",value:"\n"},l),i}function no(e,t){return e&&"run"in e?async function(n,r){let l=ni(n,{file:r,...t});await e.run(l,r)}:function(n,r){return ni(n,{file:r,...e||t})}}function na(e){if(e)throw e}var ns=n(26556);function nu(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}let nc=require("node:path"),np=require("node:process");function nd(e){return!!(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}let nf=require("node:url"),nh=["history","path","basename","stem","extname","dirname"];class nm{constructor(e){let t,n;t=e?nd(e)?{path:e}:"string"==typeof e||function(e){return!!(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":np.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<nh.length;){let e=nh[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)nh.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?nc.basename(this.path):void 0}set basename(e){ny(e,"basename"),ng(e,"basename"),this.path=nc.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?nc.dirname(this.path):void 0}set dirname(e){nb(this.basename,"dirname"),this.path=nc.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?nc.extname(this.path):void 0}set extname(e){if(ng(e,"extname"),nb(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=nc.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){nd(e)&&(e=(0,nf.fileURLToPath)(e)),ny(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?nc.basename(this.path,this.extname):void 0}set stem(e){ny(e,"stem"),ng(e,"stem"),this.path=nc.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){let r=new ee(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){return void 0===this.value?"":"string"==typeof this.value?this.value:new TextDecoder(e||void 0).decode(this.value)}}function ng(e,t){if(e&&e.includes(nc.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+nc.sep+"`")}function ny(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function nb(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}let nx=function(e){let t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},nv={}.hasOwnProperty;class nk extends nx{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=function(){let e=[],t={run:function(...t){let n=-1,r=t.pop();if("function"!=typeof r)throw TypeError("Expected function as last argument, not "+r);!function l(i,...o){let a=e[++n],s=-1;if(i)return void r(i);for(;++s<t.length;)(null===o[s]||void 0===o[s])&&(o[s]=t[s]);t=o,a?(function(e,t){let n;return function(...t){let i,o=e.length>t.length;o&&t.push(r);try{i=e.apply(this,t)}catch(e){if(o&&n)throw e;return r(e)}o||(i&&i.then&&"function"==typeof i.then?i.then(l,r):i instanceof Error?r(i):l(i))};function r(e,...l){n||(n=!0,t(e,...l))}function l(e){r(null,e)}})(a,l)(...o):r(null,...o)}(null,...t)},use:function(n){if("function"!=typeof n)throw TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}()}copy(){let e=new nk,t=-1;for(;++t<this.attachers.length;){let n=this.attachers[t];e.use(...n)}return e.data(ns(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2==arguments.length?(nT("data",this.frozen),this.namespace[e]=t,this):nv.call(this.namespace,e)&&this.namespace[e]||void 0:e?(nT("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;for(;++this.freezeIndex<this.attachers.length;){let[e,...t]=this.attachers[this.freezeIndex];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);let n=e.call(this,...t);"function"==typeof n&&this.transformers.use(n)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let t=nq(e),n=this.parser||this.Parser;return nS("parse",n),n(String(t),t)}process(e,t){let n=this;return this.freeze(),nS("process",this.parser||this.Parser),nE("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,l){let i=nq(e),o=n.parse(i);function a(e,n){e||!n?l(e):r?r(n):t(void 0,n)}n.run(o,i,function(e,t,r){var l,i;if(e||!t||!r)return a(e);let o=n.stringify(t,r);"string"==typeof(l=o)||(i=l)&&"object"==typeof i&&"byteLength"in i&&"byteOffset"in i?r.value=o:r.result=o,a(e,r)})}}processSync(e){let t,n=!1;return this.freeze(),nS("processSync",this.parser||this.Parser),nE("processSync",this.compiler||this.Compiler),this.process(e,function(e,r){n=!0,na(e),t=r}),nD("processSync","process",n),t}run(e,t,n){nC(e),this.freeze();let r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?l(void 0,n):new Promise(l);function l(l,i){let o=nq(t);r.run(e,o,function(t,r,o){let a=r||e;t?i(t):l?l(a):n(void 0,a,o)})}}runSync(e,t){let n,r=!1;return this.run(e,t,function(e,t){na(e),n=t,r=!0}),nD("runSync","run",r),n}stringify(e,t){this.freeze();let n=nq(t),r=this.compiler||this.Compiler;return nE("stringify",r),nC(e),r(e,n)}use(e,...t){let n=this.attachers,r=this.namespace;if(nT("use",this.frozen),null==e);else if("function"==typeof e)o(e,t);else if("object"==typeof e)Array.isArray(e)?i(e):l(e);else throw TypeError("Expected usable value, not `"+e+"`");return this;function l(e){if(!("plugins"in e)&&!("settings"in e))throw Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");i(e.plugins),e.settings&&(r.settings=ns(!0,r.settings,e.settings))}function i(e){let t=-1;if(null==e);else if(Array.isArray(e))for(;++t<e.length;){var n=e[t];if("function"==typeof n)o(n,[]);else if("object"==typeof n)if(Array.isArray(n)){let[e,...t]=n;o(e,t)}else l(n);else throw TypeError("Expected usable value, not `"+n+"`")}else throw TypeError("Expected a list of plugins, not `"+e+"`")}function o(e,t){let r=-1,l=-1;for(;++r<n.length;)if(n[r][0]===e){l=r;break}if(-1===l)n.push([e,...t]);else if(t.length>0){let[r,...i]=t,o=n[l][1];nu(o)&&nu(r)&&(r=ns(!0,o,r)),n[l]=[e,r,...i]}}}}let nw=new nk().freeze();function nS(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `parser`")}function nE(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `compiler`")}function nT(e,t){if(t)throw Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function nC(e){if(!nu(e)||"string"!=typeof e.type)throw TypeError("Expected node, got `"+e+"`")}function nD(e,t,n){if(!n)throw Error("`"+e+"` finished async. Use `"+t+"` instead")}function nq(e){var t;return(t=e)&&"object"==typeof t&&"message"in t&&"messages"in t?e:new nm(e)}let nL=[],nP={allowDangerousHtml:!0},nA=/^(https?|ircs?|mailto|xmpp)$/i,nN=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function nI(e){let t=function(e){let t=e.rehypePlugins||nL,n=e.remarkPlugins||nL,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...nP}:nP;return nw().use(tz).use(n).use(no,r).use(t)}(e),n=function(e){let t=e.children||"",n=new nm;return"string"==typeof t&&(n.value=t),n}(e);return function(e,t){let n=t.allowedElements,r=t.allowElement,l=t.components,o=t.disallowedElements,a=t.skipHtml,s=t.unwrapDisallowed,u=t.urlTransform||nR;for(let e of nN)Object.hasOwn(t,e.from)&&(e.from,e.to&&e.to,e.id);return t4(e,function(e,t,l){if("raw"===e.type&&l&&"number"==typeof t)return a?l.children.splice(t,1):l.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in eh)if(Object.hasOwn(eh,t)&&Object.hasOwn(e.properties,t)){let n=e.properties[t],r=eh[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=u(String(n||""),t,e))}}if("element"===e.type){let i=n?!n.includes(e.tagName):!!o&&o.includes(e.tagName);if(!i&&r&&"number"==typeof t&&(i=!r(e,t,l)),i&&l&&"number"==typeof t)return s&&e.children?l.children.splice(t,1,...e.children):l.children.splice(t,1),t}}),function(e,t){var n,r,l,i,o;let a;if(!t||void 0===t.Fragment)throw TypeError("Expected `Fragment` in options");let s=t.filePath||void 0;if(t.development){if("function"!=typeof t.jsxDEV)throw TypeError("Expected `jsxDEV` in options when `development: true`");n=s,r=t.jsxDEV,a=function(e,t,l,i){let o=Array.isArray(l.children),a=W(e);return r(t,l,i,o,{columnNumber:a?a.column-1:void 0,fileName:n,lineNumber:a?a.line:void 0},void 0)}}else{if("function"!=typeof t.jsx)throw TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw TypeError("Expected `jsxs` in production options");l=0,i=t.jsx,o=t.jsxs,a=function(e,t,n,r){let l=Array.isArray(n.children)?o:i;return r?l(t,n,r):l(t,n)}}let u={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:a,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:s,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?z:M,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},c=ea(u,e,void 0);return c&&"string"!=typeof c?c:u.create(e,u.Fragment,{children:c||void 0},void 0)}(e,{Fragment:i.Fragment,components:l,ignoreInvalidStyle:!0,jsx:i.jsx,jsxs:i.jsxs,passKeys:!0,passNode:!0})}(t.runSync(t.parse(n),n),e)}function nR(e){let t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),l=e.indexOf("/");return -1===t||-1!==l&&t>l||-1!==n&&t>n||-1!==r&&t>r||nA.test(e.slice(0,t))?e:""}async function nO(){return(0,a.IZ)().map(e=>({slug:e}))}async function n_({params:e}){let{slug:t}=await e,n=(0,a.N7)(t);return n?{title:`${n.title} | R3B3L M3D14`,description:n.summary,openGraph:{title:n.title,description:n.summary,type:"article",publishedTime:n.date,authors:[n.author]},twitter:{card:"summary_large_image",title:n.title,description:n.summary}}:{title:"Article Not Found | R3B3L M3D14"}}async function nj({params:e}){let{slug:t}=await e,n=(0,a.N7)(t);return n||(0,o.notFound)(),(0,i.jsx)("div",{className:"min-h-screen py-8",children:(0,i.jsxs)("article",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,i.jsxs)("header",{className:"mb-12",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsxs)("span",{className:"flame-badge",children:["\uD83D\uDD25 ",n.badge]}),(0,i.jsx)("time",{className:"text-gray-400 text-sm",children:new Date(n.date).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]}),(0,i.jsx)("h1",{className:"text-4xl md:text-6xl font-bold text-white mb-6 leading-tight",children:n.title}),(0,i.jsx)("p",{className:"text-xl text-gray-300 mb-6 leading-relaxed",children:n.summary}),(0,i.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-gray-800",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{className:"text-gray-400",children:"By"}),(0,i.jsx)("span",{className:"text-white font-medium",children:n.author})]}),(0,i.jsx)("div",{className:"glitch-text text-sm",children:"NODE CERTIFIED CONTENT"})]})]}),(0,i.jsx)("div",{className:"prose prose-invert prose-lg max-w-none",children:(0,i.jsx)(nI,{components:{h1:({children:e})=>(0,i.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-white mb-6 mt-8",children:e}),h2:({children:e})=>(0,i.jsx)("h2",{className:"text-2xl md:text-3xl font-bold text-white mb-4 mt-8",children:e}),h3:({children:e})=>(0,i.jsx)("h3",{className:"text-xl md:text-2xl font-bold text-white mb-4 mt-6",children:e}),p:({children:e})=>(0,i.jsx)("p",{className:"text-gray-300 mb-4 leading-relaxed",children:e}),strong:({children:e})=>(0,i.jsx)("strong",{className:"text-white font-bold",children:e}),em:({children:e})=>(0,i.jsx)("em",{className:"text-gray-200 italic",children:e}),ul:({children:e})=>(0,i.jsx)("ul",{className:"list-disc list-inside text-gray-300 mb-4 space-y-2",children:e}),ol:({children:e})=>(0,i.jsx)("ol",{className:"list-decimal list-inside text-gray-300 mb-4 space-y-2",children:e}),li:({children:e})=>(0,i.jsx)("li",{className:"text-gray-300",children:e}),blockquote:({children:e})=>(0,i.jsx)("blockquote",{className:"border-l-4 border-red-500 pl-6 py-2 my-6 bg-gray-900/50 rounded-r-lg",children:(0,i.jsx)("div",{className:"text-gray-200 italic",children:e})}),code:({children:e})=>(0,i.jsx)("code",{className:"bg-gray-800 text-cyan-400 px-2 py-1 rounded text-sm",children:e}),pre:({children:e})=>(0,i.jsx)("pre",{className:"bg-gray-900 border border-gray-700 rounded-lg p-4 overflow-x-auto mb-4",children:e})},children:n.content})}),(0,i.jsx)("footer",{className:"mt-12 pt-8 border-t border-gray-800",children:(0,i.jsx)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:(0,i.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Share this R3B3L Intelligence Drop"}),(0,i.jsx)("p",{className:"text-gray-400 text-sm",children:"Help spread truth across the digital resistance network"})]}),(0,i.jsxs)("div",{className:"flex space-x-4",children:[(0,i.jsx)("span",{className:"flame-badge",children:"FLAME LICENSED"}),(0,i.jsx)("span",{className:"glitch-text text-sm",children:"NODE SEALED"})]})]})})})]})})}},70899:(e,t,n)=>{"use strict";function r(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return r}}),n(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,o.isNextRouterError)(t)||(0,i.isBailoutToCSRError)(t)||(0,s.isDynamicServerError)(t)||(0,a.isDynamicPostpone)(t)||(0,l.isPostpone)(t)||(0,r.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let r=n(68388),l=n(52637),i=n(51846),o=n(31162),a=n(84971),s=n(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},86897:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getRedirectError:function(){return o},getRedirectStatusCodeFromError:function(){return p},getRedirectTypeFromError:function(){return c},getURLFromRedirectError:function(){return u},permanentRedirect:function(){return s},redirect:function(){return a}});let r=n(52836),l=n(49026),i=n(19121).actionAsyncStorage;function o(e,t,n){void 0===n&&(n=r.RedirectStatusCode.TemporaryRedirect);let i=Object.defineProperty(Error(l.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return i.digest=l.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+n+";",i}function a(e,t){var n;throw null!=t||(t=(null==i||null==(n=i.getStore())?void 0:n.isAction)?l.RedirectType.push:l.RedirectType.replace),o(e,t,r.RedirectStatusCode.TemporaryRedirect)}function s(e,t){throw void 0===t&&(t=l.RedirectType.replace),o(e,t,r.RedirectStatusCode.PermanentRedirect)}function u(e){return(0,l.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function c(e){if(!(0,l.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function p(e){if(!(0,l.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},94934:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,l.default)(e),i="function"==typeof t;return r.forEach(function(e){if("declaration"===e.type){var r=e.property,l=e.value;i?t(r,l,e):l&&((n=n||{})[r]=l)}}),n};var l=r(n(99603))},95968:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>l});var r=n(31658);let l=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},96487:()=>{},97576:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{ReadonlyURLSearchParams:function(){return c},RedirectType:function(){return l.RedirectType},forbidden:function(){return o.forbidden},notFound:function(){return i.notFound},permanentRedirect:function(){return r.permanentRedirect},redirect:function(){return r.redirect},unauthorized:function(){return a.unauthorized},unstable_rethrow:function(){return s.unstable_rethrow}});let r=n(86897),l=n(49026),i=n(62765),o=n(48976),a=n(70899),s=n(163);class u extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class c extends URLSearchParams{append(){throw new u}delete(){throw new u}set(){throw new u}sort(){throw new u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},99603:e=>{var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,l=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,i=/^:\s*/,o=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,a=/^[;\s]*/,s=/^\s+|\s+$/g;function u(e){return e?e.replace(s,""):""}e.exports=function(e,s){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];s=s||{};var c=1,p=1;function d(e){var t=e.match(n);t&&(c+=t.length);var r=e.lastIndexOf("\n");p=~r?e.length-r:p+e.length}function f(){var e={line:c,column:p};return function(t){return t.position=new h(e),y(r),t}}function h(e){this.start=e,this.end={line:c,column:p},this.source=s.source}h.prototype.content=e;var m=[];function g(t){var n=Error(s.source+":"+c+":"+p+": "+t);if(n.reason=t,n.filename=s.source,n.line=c,n.column=p,n.source=e,s.silent)m.push(n);else throw n}function y(t){var n=t.exec(e);if(n){var r=n[0];return d(r),e=e.slice(r.length),n}}function b(e){var t;for(e=e||[];t=x();)!1!==t&&e.push(t);return e}function x(){var t=f();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return g("End of comment missing");var r=e.slice(2,n-2);return p+=2,d(r),e=e.slice(n),p+=2,t({type:"comment",comment:r})}}y(r);var v,k=[];for(b(k);v=function(){var e=f(),n=y(l);if(n){if(x(),!y(i))return g("property missing ':'");var r=y(o),s=e({type:"declaration",property:u(n[0].replace(t,"")),value:r?u(r[0].replace(t,"")):""});return y(a),s}}();)!1!==v&&(k.push(v),b(k));return k}}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[243,512,658,379,108],()=>n(23747));module.exports=r})();