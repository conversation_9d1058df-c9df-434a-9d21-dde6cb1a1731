(()=>{var e={};e.id=607,e.ids=[607],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5831:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>v,routeModule:()=>f,serverHooks:()=>q,workAsyncStorage:()=>j,workUnitAsyncStorage:()=>h});var t={};s.r(t),s.d(t,{GET:()=>w,POST:()=>y});var o=s(96559),i=s(48088),n=s(37719),a=s(32190),u=s(19854),p=s(12909),c=s(29021),l=s.n(c),d=s(33873),x=s.n(d),m=s(99379),g=s.n(m);async function y(e){try{let r=await (0,u.getServerSession)(p.N);if(!r||"admin"!==r.user.role)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{title:s,content:t,summary:o,author:i,badge:n,slug:c}=await e.json();if(!s||!t||!o||!i||!n)return a.NextResponse.json({error:"Missing required fields"},{status:400});let d=c||s.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,""),m={title:s,date:new Date().toISOString().split("T")[0],summary:o,author:i,badge:n},y=g().stringify(t,m),w=x().join(process.cwd(),"content/posts");l().existsSync(w)||l().mkdirSync(w,{recursive:!0});let f=x().join(w,`${d}.md`);return l().writeFileSync(f,y),a.NextResponse.json({success:!0,slug:d,message:"Post created successfully"})}catch(e){return console.error("Post creation error:",e),a.NextResponse.json({error:"Failed to create post"},{status:500})}}async function w(e){try{let e=await (0,u.getServerSession)(p.N);if(!e||"admin"!==e.user.role)return a.NextResponse.json({error:"Unauthorized"},{status:401});let r=x().join(process.cwd(),"content/posts");if(!l().existsSync(r))return a.NextResponse.json([]);let s=l().readdirSync(r).filter(e=>e.endsWith(".md")).map(e=>{let s=x().join(r,e),t=l().readFileSync(s,"utf8"),{data:o}=g()(t);return{slug:e.replace(".md",""),...o,filename:e}}).sort((e,r)=>new Date(r.date).getTime()-new Date(e.date).getTime());return a.NextResponse.json(s)}catch(e){return console.error("Posts fetch error:",e),a.NextResponse.json({error:"Failed to fetch posts"},{status:500})}}let f=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/posts/route",pathname:"/api/posts",filename:"route",bundlePath:"app/api/posts/route"},resolvedPagePath:"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/api/posts/route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:j,workUnitAsyncStorage:h,serverHooks:q}=f;function v(){return(0,n.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:h})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,s)=>{"use strict";s.d(r,{N:()=>t});let t={providers:[(0,s(13581).A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=process.env.ADMIN_EMAIL,s=process.env.ADMIN_PASSWORD;return e.email===r&&e.password===s?{id:"1",email:r,name:"R3B3L Admin",role:"admin"}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role),e)},pages:{signIn:"/admin/login"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[243,286,379,190],()=>s(5831));module.exports=t})();