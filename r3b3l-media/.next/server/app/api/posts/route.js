(()=>{var e={};e.id=607,e.ids=[607],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5831:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>f,serverHooks:()=>q,workAsyncStorage:()=>j,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>y,POST:()=>w});var o=t(96559),i=t(48088),n=t(37719),a=t(32190),u=t(19854),p=t(12909),c=t(29021),l=t.n(c),d=t(33873),x=t.n(d),m=t(99379),g=t.n(m);async function w(e){try{let r=await (0,u.getServerSession)(p.N);if(!r||"admin"!==r.user.role)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{title:t,content:s,summary:o,author:i,badge:n,slug:c}=await e.json();if(!t||!s||!o||!i||!n)return a.NextResponse.json({error:"Missing required fields"},{status:400});let d=c||t.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,""),m={title:t,date:new Date().toISOString().split("T")[0],summary:o,author:i,badge:n},w=g().stringify(s,m),y=x().join(process.cwd(),"content/posts");l().existsSync(y)||l().mkdirSync(y,{recursive:!0});let f=x().join(y,`${d}.md`);return l().writeFileSync(f,w),a.NextResponse.json({success:!0,slug:d,message:"Post created successfully"})}catch(e){return console.error("Post creation error:",e),a.NextResponse.json({error:"Failed to create post"},{status:500})}}async function y(){try{let e=await (0,u.getServerSession)(p.N);if(!e||"admin"!==e.user.role)return a.NextResponse.json({error:"Unauthorized"},{status:401});let r=x().join(process.cwd(),"content/posts");if(!l().existsSync(r))return a.NextResponse.json([]);let t=l().readdirSync(r).filter(e=>e.endsWith(".md")).map(e=>{let t=x().join(r,e),s=l().readFileSync(t,"utf8"),{data:o}=g()(s);return{slug:e.replace(".md",""),...o,filename:e,date:o.date||new Date().toISOString().split("T")[0]}}).sort((e,r)=>new Date(r.date).getTime()-new Date(e.date).getTime());return a.NextResponse.json(t)}catch(e){return console.error("Posts fetch error:",e),a.NextResponse.json({error:"Failed to fetch posts"},{status:500})}}let f=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/posts/route",pathname:"/api/posts",filename:"route",bundlePath:"app/api/posts/route"},resolvedPagePath:"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/api/posts/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:j,workUnitAsyncStorage:h,serverHooks:q}=f;function v(){return(0,n.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:h})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>s});let s={providers:[(0,t(13581).A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=process.env.ADMIN_EMAIL,t=process.env.ADMIN_PASSWORD;return e.email===r&&e.password===t?{id:"1",email:r,name:"R3B3L Admin",role:"admin"}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role),e)},pages:{signIn:"/admin/login"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,286,379,190],()=>t(5831));module.exports=s})();