(()=>{var e={};e.id=923,e.ids=[923],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12405:(e,t,s)=>{"use strict";let n,r,i;s.r(t),s.d(t,{patchFetch:()=>nu,routeModule:()=>na,serverHooks:()=>nc,workAsyncStorage:()=>no,workUnitAsyncStorage:()=>nl});var a,o,l,c,u,h,d,f,p,m,g,y,w,_,b,v,x,S,A,$,I,R,O,k,E,T,P,N,C,<PERSON>,j,<PERSON>,D,B,W,q,U,F,X,J,H,K,V,z,G,Y,Q,Z,ee,et,es,en,er,ei,ea,eo,el,ec,eu,eh,ed,ef,ep,em,eg,ey,ew,e_,eb,ev,ex={};s.r(ex),s.d(ex,{POST:()=>ni});var eS=s(96559),eA=s(48088),e$=s(37719),eI=s(32190),eR=s(19854),eO=s(12909);function ek(e,t,s,n,r){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(e,s):r?r.value=s:t.set(e,s),s}function eE(e,t,s,n){if("a"===s&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?n:"a"===s?n.call(e):n?n.value:t.get(e)}let eT=function(){let{crypto:e}=globalThis;if(e?.randomUUID)return eT=e.randomUUID.bind(e),e.randomUUID();let t=new Uint8Array(1),s=e?()=>e.getRandomValues(t)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^s()&15>>e/4).toString(16))};function eP(e){return"object"==typeof e&&null!==e&&("name"in e&&"AbortError"===e.name||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}let eN=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e){try{if("[object Error]"===Object.prototype.toString.call(e)){let t=Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return Error(JSON.stringify(e))}catch{}}return Error(e)};class eC extends Error{}class eM extends eC{constructor(e,t,s,n){super(`${eM.makeMessage(e,t,s)}`),this.status=e,this.headers=n,this.requestID=n?.get("x-request-id"),this.error=t,this.code=t?.code,this.param=t?.param,this.type=t?.type}static makeMessage(e,t,s){let n=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):s;return e&&n?`${e} ${n}`:e?`${e} status code (no body)`:n||"(no status code or body)"}static generate(e,t,s,n){if(!e||!n)return new eL({message:s,cause:eN(t)});let r=t?.error;return 400===e?new eB(e,r,s,n):401===e?new eW(e,r,s,n):403===e?new eq(e,r,s,n):404===e?new eU(e,r,s,n):409===e?new eF(e,r,s,n):422===e?new eX(e,r,s,n):429===e?new eJ(e,r,s,n):e>=500?new eH(e,r,s,n):new eM(e,r,s,n)}}class ej extends eM{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class eL extends eM{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class eD extends eL{constructor({message:e}={}){super({message:e??"Request timed out."})}}class eB extends eM{}class eW extends eM{}class eq extends eM{}class eU extends eM{}class eF extends eM{}class eX extends eM{}class eJ extends eM{}class eH extends eM{}class eK extends eC{constructor(){super("Could not parse response content as the length limit was reached")}}class eV extends eC{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}let ez=/^[a-z][a-z0-9+.-]*:/i,eG=e=>ez.test(e);function eY(e){return null!=e&&"object"==typeof e&&!Array.isArray(e)}let eQ=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new eC(`${e} must be an integer`);if(t<0)throw new eC(`${e} must be a positive integer`);return t},eZ=e=>{try{return JSON.parse(e)}catch(e){return}},e0=e=>new Promise(t=>setTimeout(t,e)),e1={off:0,error:200,warn:300,info:400,debug:500},e2=(e,t,s)=>{if(e){if(Object.prototype.hasOwnProperty.call(e1,e))return e;e6(s).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(e1))}`)}};function e3(){}function e4(e,t,s){return!t||e1[e]>e1[s]?e3:t[e].bind(t)}let e5={error:e3,warn:e3,info:e3,debug:e3},e8=new WeakMap;function e6(e){let t=e.logger,s=e.logLevel??"off";if(!t)return e5;let n=e8.get(t);if(n&&n[0]===s)return n[1];let r={error:e4("error",t,s),warn:e4("warn",t,s),info:e4("info",t,s),debug:e4("debug",t,s)};return e8.set(t,[s,r]),r}let e9=e=>(e.options&&(e.options={...e.options},delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([e,t])=>[e,"authorization"===e.toLowerCase()||"cookie"===e.toLowerCase()||"set-cookie"===e.toLowerCase()?"***":t]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e),e7="5.0.1",te=()=>"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator,tt=()=>{let e="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e7,"X-Stainless-OS":tn(Deno.build.os),"X-Stainless-Arch":ts(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e7,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e7,"X-Stainless-OS":tn(globalThis.process.platform??"unknown"),"X-Stainless-Arch":ts(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};let t=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let s=t.exec(navigator.userAgent);if(s){let t=s[1]||0,n=s[2]||0,r=s[3]||0;return{browser:e,version:`${t}.${n}.${r}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e7,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e7,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}},ts=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",tn=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",tr=()=>n??(n=tt());function ti(...e){let t=globalThis.ReadableStream;if(void 0===t)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function ta(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return ti({start(){},async pull(e){let{done:s,value:n}=await t.next();s?e.close():e.enqueue(n)},async cancel(){await t.return?.()}})}function to(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function tl(e){if(null===e||"object"!=typeof e)return;if(e[Symbol.asyncIterator])return void await e[Symbol.asyncIterator]().return?.();let t=e.getReader(),s=t.cancel();t.releaseLock(),await s}let tc=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)}),tu="RFC3986",th={RFC1738:e=>String(e).replace(/%20/g,"+"),RFC3986:e=>String(e)},td=(Object.prototype.hasOwnProperty,Array.isArray),tf=(()=>{let e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e})();function tp(e,t){if(td(e)){let s=[];for(let n=0;n<e.length;n+=1)s.push(t(e[n]));return s}return t(e)}let tm=Object.prototype.hasOwnProperty,tg={brackets:e=>String(e)+"[]",comma:"comma",indices:(e,t)=>String(e)+"["+t+"]",repeat:e=>String(e)},ty=Array.isArray,tw=Array.prototype.push,t_=function(e,t){tw.apply(e,ty(t)?t:[t])},tb=Date.prototype.toISOString,tv={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(e,t,s,n,r)=>{if(0===e.length)return e;let i=e;if("symbol"==typeof e?i=Symbol.prototype.toString.call(e):"string"!=typeof e&&(i=String(e)),"iso-8859-1"===s)return escape(i).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let a="";for(let e=0;e<i.length;e+=1024){let t=i.length>=1024?i.slice(e,e+1024):i,s=[];for(let e=0;e<t.length;++e){let n=t.charCodeAt(e);if(45===n||46===n||95===n||126===n||n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122||"RFC1738"===r&&(40===n||41===n)){s[s.length]=t.charAt(e);continue}if(n<128){s[s.length]=tf[n];continue}if(n<2048){s[s.length]=tf[192|n>>6]+tf[128|63&n];continue}if(n<55296||n>=57344){s[s.length]=tf[224|n>>12]+tf[128|n>>6&63]+tf[128|63&n];continue}e+=1,n=65536+((1023&n)<<10|1023&t.charCodeAt(e)),s[s.length]=tf[240|n>>18]+tf[128|n>>12&63]+tf[128|n>>6&63]+tf[128|63&n]}a+=s.join("")}return a},encodeValuesOnly:!1,format:tu,formatter:th[tu],indices:!1,serializeDate:e=>tb.call(e),skipNulls:!1,strictNullHandling:!1},tx={};function tS(e){let t;return(r??(r=(t=new globalThis.TextEncoder).encode.bind(t)))(e)}function tA(e){let t;return(i??(i=(t=new globalThis.TextDecoder).decode.bind(t)))(e)}class t${constructor(){a.set(this,void 0),o.set(this,void 0),ek(this,a,new Uint8Array,"f"),ek(this,o,null,"f")}decode(e){let t;if(null==e)return[];let s=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?tS(e):e;ek(this,a,function(e){let t=0;for(let s of e)t+=s.length;let s=new Uint8Array(t),n=0;for(let t of e)s.set(t,n),n+=t.length;return s}([eE(this,a,"f"),s]),"f");let n=[];for(;null!=(t=function(e,t){for(let s=t??0;s<e.length;s++){if(10===e[s])return{preceding:s,index:s+1,carriage:!1};if(13===e[s])return{preceding:s,index:s+1,carriage:!0}}return null}(eE(this,a,"f"),eE(this,o,"f")));){if(t.carriage&&null==eE(this,o,"f")){ek(this,o,t.index,"f");continue}if(null!=eE(this,o,"f")&&(t.index!==eE(this,o,"f")+1||t.carriage)){n.push(tA(eE(this,a,"f").subarray(0,eE(this,o,"f")-1))),ek(this,a,eE(this,a,"f").subarray(eE(this,o,"f")),"f"),ek(this,o,null,"f");continue}let e=null!==eE(this,o,"f")?t.preceding-1:t.preceding,s=tA(eE(this,a,"f").subarray(0,e));n.push(s),ek(this,a,eE(this,a,"f").subarray(t.index),"f"),ek(this,o,null,"f")}return n}flush(){return eE(this,a,"f").length?this.decode("\n"):[]}}a=new WeakMap,o=new WeakMap,t$.NEWLINE_CHARS=new Set(["\n","\r"]),t$.NEWLINE_REGEXP=/\r\n|[\n\r]/g;class tI{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let s=!1;async function*n(){if(s)throw new eC("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let n=!1;try{for await(let s of tR(e,t))if(!n){if(s.data.startsWith("[DONE]")){n=!0;continue}if(null===s.event||s.event.startsWith("response.")||s.event.startsWith("transcript.")){let t;try{t=JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if(t&&t.error)throw new eM(void 0,t.error,void 0,e.headers);yield t}else{let e;try{e=JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if("error"==s.event)throw new eM(void 0,e.error,e.message,void 0);yield{event:s.event,data:e}}}n=!0}catch(e){if(eP(e))return;throw e}finally{n||t.abort()}}return new tI(n,t)}static fromReadableStream(e,t){let s=!1;async function*n(){let t=new t$;for await(let s of to(e))for(let e of t.decode(s))yield e;for(let e of t.flush())yield e}return new tI(async function*(){if(s)throw new eC("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let e=!1;try{for await(let t of n())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(eP(e))return;throw e}finally{e||t.abort()}},t)}[Symbol.asyncIterator](){return this.iterator()}tee(){let e=[],t=[],s=this.iterator(),n=n=>({next:()=>{if(0===n.length){let n=s.next();e.push(n),t.push(n)}return n.shift()}});return[new tI(()=>n(e),this.controller),new tI(()=>n(t),this.controller)]}toReadableStream(){let e,t=this;return ti({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:s,done:n}=await e.next();if(n)return t.close();let r=tS(JSON.stringify(s)+"\n");t.enqueue(r)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*tR(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new eC("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new eC("Attempted to iterate over a response with no body")}let s=new tk,n=new t$;for await(let t of tO(to(e.body)))for(let e of n.decode(t)){let t=s.decode(e);t&&(yield t)}for(let e of n.flush()){let t=s.decode(e);t&&(yield t)}}async function*tO(e){let t=new Uint8Array;for await(let s of e){let e;if(null==s)continue;let n=s instanceof ArrayBuffer?new Uint8Array(s):"string"==typeof s?tS(s):s,r=new Uint8Array(t.length+n.length);for(r.set(t),r.set(n,t.length),t=r;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class tk{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,s,n]=function(e,t){let s=e.indexOf(":");return -1!==s?[e.substring(0,s),t,e.substring(s+t.length)]:[e,"",""]}(e,":");return n.startsWith(" ")&&(n=n.substring(1)),"event"===t?this.event=n:"data"===t&&this.data.push(n),null}}async function tE(e,t){let{response:s,requestLogID:n,retryOfRequestLogID:r,startTime:i}=t,a=await (async()=>{if(t.options.stream)return(e6(e).debug("response",s.status,s.url,s.headers,s.body),t.options.__streamClass)?t.options.__streamClass.fromSSEResponse(s,t.controller):tI.fromSSEResponse(s,t.controller);if(204===s.status)return null;if(t.options.__binaryResponse)return s;let n=s.headers.get("content-type"),r=n?.split(";")[0]?.trim();return r?.includes("application/json")||r?.endsWith("+json")?tT(await s.json(),s):await s.text()})();return e6(e).debug(`[${n}] response parsed`,e9({retryOfRequestLogID:r,url:s.url,status:s.status,body:a,durationMs:Date.now()-i})),a}function tT(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("x-request-id"),enumerable:!1})}class tP extends Promise{constructor(e,t,s=tE){super(e=>{e(null)}),this.responsePromise=t,this.parseResponse=s,l.set(this,void 0),ek(this,l,e,"f")}_thenUnwrap(e){return new tP(eE(this,l,"f"),this.responsePromise,async(t,s)=>tT(e(await this.parseResponse(t,s),s),s.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(eE(this,l,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}l=new WeakMap;class tN{constructor(e,t,s,n){c.set(this,void 0),ek(this,c,e,"f"),this.options=n,this.response=t,this.body=s}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new eC("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await eE(this,c,"f").requestAPIList(this.constructor,e)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(c=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class tC extends tP{constructor(e,t,s){super(e,t,async(e,t)=>new s(e,t.response,await tE(e,t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}class tM extends tN{constructor(e,t,s,n){super(e,t,s,n),this.data=s.data||[],this.object=s.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class tj extends tN{constructor(e,t,s,n){super(e,t,s,n),this.data=s.data||[],this.has_more=s.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){var e;let t=this.getPaginatedItems(),s=t[t.length-1]?.id;return s?{...this.options,query:{..."object"!=typeof(e=this.options.query)?{}:e??{},after:s}}:null}}let tL=()=>{if("undefined"==typeof File){let{process:e}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof e?.versions?.node&&20>parseInt(e.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function tD(e,t,s){return tL(),new File(e,t??"unknown_file",s)}function tB(e){return("object"==typeof e&&null!==e&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}let tW=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],tq=async(e,t)=>({...e,body:await tF(e.body,t)}),tU=new WeakMap,tF=async(e,t)=>{if(!await function(e){let t="function"==typeof e?e:e.fetch,s=tU.get(t);if(s)return s;let n=(async()=>{try{let e="Response"in t?t.Response:(await t("data:,")).constructor,s=new FormData;if(s.toString()===await new e(s).text())return!1;return!0}catch{return!0}})();return tU.set(t,n),n}(t))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let s=new FormData;return await Promise.all(Object.entries(e||{}).map(([e,t])=>tK(s,e,t))),s},tX=e=>e instanceof Blob&&"name"in e,tJ=e=>"object"==typeof e&&null!==e&&(e instanceof Response||tW(e)||tX(e)),tH=e=>{if(tJ(e))return!0;if(Array.isArray(e))return e.some(tH);if(e&&"object"==typeof e){for(let t in e)if(tH(e[t]))return!0}return!1},tK=async(e,t,s)=>{if(void 0!==s){if(null==s)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof s||"number"==typeof s||"boolean"==typeof s)e.append(t,String(s));else if(s instanceof Response)e.append(t,tD([await s.blob()],tB(s)));else if(tW(s))e.append(t,tD([await new Response(ta(s)).blob()],tB(s)));else if(tX(s))e.append(t,s,tB(s));else if(Array.isArray(s))await Promise.all(s.map(s=>tK(e,t+"[]",s)));else if("object"==typeof s)await Promise.all(Object.entries(s).map(([s,n])=>tK(e,`${t}[${s}]`,n)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${s} instead`)}},tV=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,tz=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&tV(e),tG=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob;async function tY(e,t,s){if(tL(),tz(e=await e))return e instanceof File?e:tD([await e.arrayBuffer()],e.name);if(tG(e)){let n=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),tD(await tQ(n),t,s)}let n=await tQ(e);if(t||(t=tB(e)),!s?.type){let e=n.find(e=>"object"==typeof e&&"type"in e&&e.type);"string"==typeof e&&(s={...s,type:e})}return tD(n,t,s)}async function tQ(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(tV(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(tW(e))for await(let s of e)t.push(...await tQ(s));else{let t=e?.constructor?.name;throw Error(`Unexpected data type: ${typeof e}${t?`; constructor: ${t}`:""}${function(e){if("object"!=typeof e||null===e)return"";let t=Object.getOwnPropertyNames(e);return`; props: [${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`)}return t}class tZ{constructor(e){this._client=e}}function t0(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let t1=((e=t0)=>function(t,...s){let n;if(1===t.length)return t[0];let r=!1,i=t.reduce((t,n,i)=>(/[?#]/.test(n)&&(r=!0),t+n+(i===s.length?"":(r?encodeURIComponent:e)(String(s[i])))),""),a=i.split(/[?#]/,1)[0],o=[],l=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(n=l.exec(a));)o.push({start:n.index,length:n[0].length});if(o.length>0){let e=0,t=o.reduce((t,s)=>{let n=" ".repeat(s.start-e),r="^".repeat(s.length);return e=s.start+s.length,t+n+r},"");throw new eC(`Path parameters result in path with invalid segments:
${i}
${t}`)}return i})(t0);class t2 extends tZ{list(e,t={},s){return this._client.getAPIList(t1`/chat/completions/${e}/messages`,tj,{query:t,...s})}}let t3=e=>e?.role==="assistant",t4=e=>e?.role==="tool";class t5{constructor(){u.add(this),this.controller=new AbortController,h.set(this,void 0),d.set(this,()=>{}),f.set(this,()=>{}),p.set(this,void 0),m.set(this,()=>{}),g.set(this,()=>{}),y.set(this,{}),w.set(this,!1),_.set(this,!1),b.set(this,!1),v.set(this,!1),ek(this,h,new Promise((e,t)=>{ek(this,d,e,"f"),ek(this,f,t,"f")}),"f"),ek(this,p,new Promise((e,t)=>{ek(this,m,e,"f"),ek(this,g,t,"f")}),"f"),eE(this,h,"f").catch(()=>{}),eE(this,p,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},eE(this,u,"m",x).bind(this))},0)}_connected(){this.ended||(eE(this,d,"f").call(this),this._emit("connect"))}get ended(){return eE(this,w,"f")}get errored(){return eE(this,_,"f")}get aborted(){return eE(this,b,"f")}abort(){this.controller.abort()}on(e,t){return(eE(this,y,"f")[e]||(eE(this,y,"f")[e]=[])).push({listener:t}),this}off(e,t){let s=eE(this,y,"f")[e];if(!s)return this;let n=s.findIndex(e=>e.listener===t);return n>=0&&s.splice(n,1),this}once(e,t){return(eE(this,y,"f")[e]||(eE(this,y,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{ek(this,v,!0,"f"),"error"!==e&&this.once("error",s),this.once(e,t)})}async done(){ek(this,v,!0,"f"),await eE(this,p,"f")}_emit(e,...t){if(eE(this,w,"f"))return;"end"===e&&(ek(this,w,!0,"f"),eE(this,m,"f").call(this));let s=eE(this,y,"f")[e];if(s&&(eE(this,y,"f")[e]=s.filter(e=>!e.once),s.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];eE(this,v,"f")||s?.length||Promise.reject(e),eE(this,f,"f").call(this,e),eE(this,g,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];eE(this,v,"f")||s?.length||Promise.reject(e),eE(this,f,"f").call(this,e),eE(this,g,"f").call(this,e),this._emit("end")}}_emitFinal(){}}function t8(e){return e?.$brand==="auto-parseable-response-format"}function t6(e){return e?.$brand==="auto-parseable-tool"}function t9(e,t){let s=e.choices.map(e=>{var s,n;if("length"===e.finish_reason)throw new eK;if("content_filter"===e.finish_reason)throw new eV;return{...e,message:{...e.message,...e.message.tool_calls?{tool_calls:e.message.tool_calls?.map(e=>(function(e,t){let s=e.tools?.find(e=>e.function?.name===t.function.name);return{...t,function:{...t.function,parsed_arguments:t6(s)?s.$parseRaw(t.function.arguments):s?.function.strict?JSON.parse(t.function.arguments):null}}})(t,e))??void 0}:void 0,parsed:e.message.content&&!e.message.refusal?(s=t,n=e.message.content,s.response_format?.type!=="json_schema"?null:s.response_format?.type==="json_schema"?"$parseRaw"in s.response_format?s.response_format.$parseRaw(n):JSON.parse(n):null):null}}});return{...e,choices:s}}function t7(e){return!!t8(e.response_format)||(e.tools?.some(e=>t6(e)||"function"===e.type&&!0===e.function.strict)??!1)}h=new WeakMap,d=new WeakMap,f=new WeakMap,p=new WeakMap,m=new WeakMap,g=new WeakMap,y=new WeakMap,w=new WeakMap,_=new WeakMap,b=new WeakMap,v=new WeakMap,u=new WeakSet,x=function(e){if(ek(this,_,!0,"f"),e instanceof Error&&"AbortError"===e.name&&(e=new ej),e instanceof ej)return ek(this,b,!0,"f"),this._emit("abort",e);if(e instanceof eC)return this._emit("error",e);if(e instanceof Error){let t=new eC(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new eC(String(e)))};class se extends t5{constructor(){super(...arguments),S.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){this._chatCompletions.push(e),this._emit("chatCompletion",e);let t=e.choices[0]?.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),t4(e)&&e.content)this._emit("functionToolCallResult",e.content);else if(t3(e)&&e.tool_calls)for(let t of e.tool_calls)"function"===t.type&&this._emit("functionToolCall",t.function)}}async finalChatCompletion(){await this.done();let e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new eC("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),eE(this,S,"m",A).call(this)}async finalMessage(){return await this.done(),eE(this,S,"m",$).call(this)}async finalFunctionToolCall(){return await this.done(),eE(this,S,"m",I).call(this)}async finalFunctionToolCallResult(){return await this.done(),eE(this,S,"m",R).call(this)}async totalUsage(){return await this.done(),eE(this,S,"m",O).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);let t=eE(this,S,"m",$).call(this);t&&this._emit("finalMessage",t);let s=eE(this,S,"m",A).call(this);s&&this._emit("finalContent",s);let n=eE(this,S,"m",I).call(this);n&&this._emit("finalFunctionToolCall",n);let r=eE(this,S,"m",R).call(this);null!=r&&this._emit("finalFunctionToolCallResult",r),this._chatCompletions.some(e=>e.usage)&&this._emit("totalUsage",eE(this,S,"m",O).call(this))}async _createChatCompletion(e,t,s){let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),eE(this,S,"m",k).call(this,t);let r=await e.chat.completions.create({...t,stream:!1},{...s,signal:this.controller.signal});return this._connected(),this._addChatCompletion(t9(r,t))}async _runChatCompletion(e,t,s){for(let e of t.messages)this._addMessage(e,!1);return await this._createChatCompletion(e,t,s)}async _runTools(e,t,s){let n="tool",{tool_choice:r="auto",stream:i,...a}=t,o="string"!=typeof r&&r?.function?.name,{maxChatCompletions:l=10}=s||{},c=t.tools.map(e=>{if(t6(e)){if(!e.$callback)throw new eC("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:e.$callback,name:e.function.name,description:e.function.description||"",parameters:e.function.parameters,parse:e.$parseRaw,strict:!0}}}return e}),u={};for(let e of c)"function"===e.type&&(u[e.function.name||e.function.function.name]=e.function);let h="tools"in t?c.map(e=>"function"===e.type?{type:"function",function:{name:e.function.name||e.function.function.name,parameters:e.function.parameters,description:e.function.description,strict:e.function.strict}}:e):void 0;for(let e of t.messages)this._addMessage(e,!1);for(let t=0;t<l;++t){let t=await this._createChatCompletion(e,{...a,tool_choice:r,tools:h,messages:[...this.messages]},s),i=t.choices[0]?.message;if(!i)throw new eC("missing message in ChatCompletion response");if(!i.tool_calls?.length)break;for(let e of i.tool_calls){let t;if("function"!==e.type)continue;let s=e.id,{name:r,arguments:i}=e.function,a=u[r];if(a){if(o&&o!==r){let e=`Invalid tool_call: ${JSON.stringify(r)}. ${JSON.stringify(o)} requested. Please try again`;this._addMessage({role:n,tool_call_id:s,content:e});continue}}else{let e=`Invalid tool_call: ${JSON.stringify(r)}. Available options are: ${Object.keys(u).map(e=>JSON.stringify(e)).join(", ")}. Please try again`;this._addMessage({role:n,tool_call_id:s,content:e});continue}try{t="function"==typeof a.parse?await a.parse(i):i}catch(t){let e=t instanceof Error?t.message:String(t);this._addMessage({role:n,tool_call_id:s,content:e});continue}let l=await a.function(t,this),c=eE(this,S,"m",E).call(this,l);if(this._addMessage({role:n,tool_call_id:s,content:c}),o)return}}}}S=new WeakSet,A=function(){return eE(this,S,"m",$).call(this).content??null},$=function(){let e=this.messages.length;for(;e-- >0;){let t=this.messages[e];if(t3(t))return{...t,content:t.content??null,refusal:t.refusal??null}}throw new eC("stream ended without producing a ChatCompletionMessage with role=assistant")},I=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(t3(t)&&t?.tool_calls?.length)return t.tool_calls.at(-1)?.function}},R=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(t4(t)&&null!=t.content&&"string"==typeof t.content&&this.messages.some(e=>"assistant"===e.role&&e.tool_calls?.some(e=>"function"===e.type&&e.id===t.tool_call_id)))return t.content}},O=function(){let e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},k=function(e){if(null!=e.n&&e.n>1)throw new eC("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},E=function(e){return"string"==typeof e?e:void 0===e?"undefined":JSON.stringify(e)};class st extends se{static runTools(e,t,s){let n=new st,r={...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,r)),n}_addMessage(e,t=!0){super._addMessage(e,t),t3(e)&&e.content&&this._emit("content",e.content)}}let ss={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,ALL:511};class sn extends Error{}class sr extends Error{}let si=(e,t)=>{let s=e.length,n=0,r=e=>{throw new sn(`${e} at position ${n}`)},i=e=>{throw new sr(`${e} at position ${n}`)},a=()=>(h(),n>=s&&r("Unexpected end of input"),'"'===e[n])?o():"{"===e[n]?l():"["===e[n]?c():"null"===e.substring(n,n+4)||ss.NULL&t&&s-n<4&&"null".startsWith(e.substring(n))?(n+=4,null):"true"===e.substring(n,n+4)||ss.BOOL&t&&s-n<4&&"true".startsWith(e.substring(n))?(n+=4,!0):"false"===e.substring(n,n+5)||ss.BOOL&t&&s-n<5&&"false".startsWith(e.substring(n))?(n+=5,!1):"Infinity"===e.substring(n,n+8)||ss.INFINITY&t&&s-n<8&&"Infinity".startsWith(e.substring(n))?(n+=8,1/0):"-Infinity"===e.substring(n,n+9)||ss.MINUS_INFINITY&t&&1<s-n&&s-n<9&&"-Infinity".startsWith(e.substring(n))?(n+=9,-1/0):"NaN"===e.substring(n,n+3)||ss.NAN&t&&s-n<3&&"NaN".startsWith(e.substring(n))?(n+=3,NaN):u(),o=()=>{let a=n,o=!1;for(n++;n<s&&('"'!==e[n]||o&&"\\"===e[n-1]);)o="\\"===e[n]&&!o,n++;if('"'==e.charAt(n))try{return JSON.parse(e.substring(a,++n-Number(o)))}catch(e){i(String(e))}else if(ss.STR&t)try{return JSON.parse(e.substring(a,n-Number(o))+'"')}catch(t){return JSON.parse(e.substring(a,e.lastIndexOf("\\"))+'"')}r("Unterminated string literal")},l=()=>{n++,h();let i={};try{for(;"}"!==e[n];){if(h(),n>=s&&ss.OBJ&t)return i;let r=o();h(),n++;try{let e=a();Object.defineProperty(i,r,{value:e,writable:!0,enumerable:!0,configurable:!0})}catch(e){if(ss.OBJ&t)return i;throw e}h(),","===e[n]&&n++}}catch(e){if(ss.OBJ&t)return i;r("Expected '}' at end of object")}return n++,i},c=()=>{n++;let s=[];try{for(;"]"!==e[n];)s.push(a()),h(),","===e[n]&&n++}catch(e){if(ss.ARR&t)return s;r("Expected ']' at end of array")}return n++,s},u=()=>{if(0===n){"-"===e&&ss.NUM&t&&r("Not sure what '-' is");try{return JSON.parse(e)}catch(s){if(ss.NUM&t)try{if("."===e[e.length-1])return JSON.parse(e.substring(0,e.lastIndexOf(".")));return JSON.parse(e.substring(0,e.lastIndexOf("e")))}catch(e){}i(String(s))}}let a=n;for("-"===e[n]&&n++;e[n]&&!",]}".includes(e[n]);)n++;n!=s||ss.NUM&t||r("Unterminated number literal");try{return JSON.parse(e.substring(a,n))}catch(s){"-"===e.substring(a,n)&&ss.NUM&t&&r("Not sure what '-' is");try{return JSON.parse(e.substring(a,e.lastIndexOf("e")))}catch(e){i(String(e))}}},h=()=>{for(;n<s&&" \n\r	".includes(e[n]);)n++};return a()},sa=e=>(function(e,t=ss.ALL){if("string"!=typeof e)throw TypeError(`expecting str, got ${typeof e}`);if(!e.trim())throw Error(`${e} is empty`);return si(e.trim(),t)})(e,ss.ALL^ss.NUM);class so extends se{constructor(e){super(),T.add(this),P.set(this,void 0),N.set(this,void 0),C.set(this,void 0),ek(this,P,e,"f"),ek(this,N,[],"f")}get currentChatCompletionSnapshot(){return eE(this,C,"f")}static fromReadableStream(e){let t=new so(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,s){let n=new so(t);return n._run(()=>n._runChatCompletion(e,{...t,stream:!0},{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createChatCompletion(e,t,s){super._createChatCompletion;let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),eE(this,T,"m",M).call(this);let r=await e.chat.completions.create({...t,stream:!0},{...s,signal:this.controller.signal});for await(let e of(this._connected(),r))eE(this,T,"m",L).call(this,e);if(r.controller.signal?.aborted)throw new ej;return this._addChatCompletion(eE(this,T,"m",W).call(this))}async _fromReadableStream(e,t){let s,n=t?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),eE(this,T,"m",M).call(this),this._connected();let r=tI.fromReadableStream(e,this.controller);for await(let e of r)s&&s!==e.id&&this._addChatCompletion(eE(this,T,"m",W).call(this)),eE(this,T,"m",L).call(this,e),s=e.id;if(r.controller.signal?.aborted)throw new ej;return this._addChatCompletion(eE(this,T,"m",W).call(this))}[(P=new WeakMap,N=new WeakMap,C=new WeakMap,T=new WeakSet,M=function(){this.ended||ek(this,C,void 0,"f")},j=function(e){let t=eE(this,N,"f")[e.index];return t||(t={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},eE(this,N,"f")[e.index]=t),t},L=function(e){if(this.ended)return;let t=eE(this,T,"m",U).call(this,e);for(let s of(this._emit("chunk",e,t),e.choices)){let e=t.choices[s.index];null!=s.delta.content&&e.message?.role==="assistant"&&e.message?.content&&(this._emit("content",s.delta.content,e.message.content),this._emit("content.delta",{delta:s.delta.content,snapshot:e.message.content,parsed:e.message.parsed})),null!=s.delta.refusal&&e.message?.role==="assistant"&&e.message?.refusal&&this._emit("refusal.delta",{delta:s.delta.refusal,snapshot:e.message.refusal}),s.logprobs?.content!=null&&e.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:s.logprobs?.content,snapshot:e.logprobs?.content??[]}),s.logprobs?.refusal!=null&&e.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:s.logprobs?.refusal,snapshot:e.logprobs?.refusal??[]});let n=eE(this,T,"m",j).call(this,e);for(let t of(e.finish_reason&&(eE(this,T,"m",B).call(this,e),null!=n.current_tool_call_index&&eE(this,T,"m",D).call(this,e,n.current_tool_call_index)),s.delta.tool_calls??[]))n.current_tool_call_index!==t.index&&(eE(this,T,"m",B).call(this,e),null!=n.current_tool_call_index&&eE(this,T,"m",D).call(this,e,n.current_tool_call_index)),n.current_tool_call_index=t.index;for(let t of s.delta.tool_calls??[]){let s=e.message.tool_calls?.[t.index];s?.type&&(s?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:s.function?.name,index:t.index,arguments:s.function.arguments,parsed_arguments:s.function.parsed_arguments,arguments_delta:t.function?.arguments??""}):s?.type)}}},D=function(e,t){if(eE(this,T,"m",j).call(this,e).done_tool_calls.has(t))return;let s=e.message.tool_calls?.[t];if(!s)throw Error("no tool call snapshot");if(!s.type)throw Error("tool call snapshot missing `type`");if("function"===s.type){let e=eE(this,P,"f")?.tools?.find(e=>"function"===e.type&&e.function.name===s.function.name);this._emit("tool_calls.function.arguments.done",{name:s.function.name,index:t,arguments:s.function.arguments,parsed_arguments:t6(e)?e.$parseRaw(s.function.arguments):e?.function.strict?JSON.parse(s.function.arguments):null})}else s.type},B=function(e){let t=eE(this,T,"m",j).call(this,e);if(e.message.content&&!t.content_done){t.content_done=!0;let s=eE(this,T,"m",q).call(this);this._emit("content.done",{content:e.message.content,parsed:s?s.$parseRaw(e.message.content):null})}e.message.refusal&&!t.refusal_done&&(t.refusal_done=!0,this._emit("refusal.done",{refusal:e.message.refusal})),e.logprobs?.content&&!t.logprobs_content_done&&(t.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:e.logprobs.content})),e.logprobs?.refusal&&!t.logprobs_refusal_done&&(t.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:e.logprobs.refusal}))},W=function(){if(this.ended)throw new eC("stream has ended, this shouldn't happen");let e=eE(this,C,"f");if(!e)throw new eC("request ended without sending any chunks");return ek(this,C,void 0,"f"),ek(this,N,[],"f"),function(e,t){var s;let{id:n,choices:r,created:i,model:a,system_fingerprint:o,...l}=e;return s={...l,id:n,choices:r.map(({message:t,finish_reason:s,index:n,logprobs:r,...i})=>{if(!s)throw new eC(`missing finish_reason for choice ${n}`);let{content:a=null,function_call:o,tool_calls:l,...c}=t,u=t.role;if(!u)throw new eC(`missing role for choice ${n}`);if(o){let{arguments:e,name:l}=o;if(null==e)throw new eC(`missing function_call.arguments for choice ${n}`);if(!l)throw new eC(`missing function_call.name for choice ${n}`);return{...i,message:{content:a,function_call:{arguments:e,name:l},role:u,refusal:t.refusal??null},finish_reason:s,index:n,logprobs:r}}return l?{...i,index:n,finish_reason:s,logprobs:r,message:{...c,role:u,content:a,refusal:t.refusal??null,tool_calls:l.map((t,s)=>{let{function:r,type:i,id:a,...o}=t,{arguments:l,name:c,...u}=r||{};if(null==a)throw new eC(`missing choices[${n}].tool_calls[${s}].id
${sl(e)}`);if(null==i)throw new eC(`missing choices[${n}].tool_calls[${s}].type
${sl(e)}`);if(null==c)throw new eC(`missing choices[${n}].tool_calls[${s}].function.name
${sl(e)}`);if(null==l)throw new eC(`missing choices[${n}].tool_calls[${s}].function.arguments
${sl(e)}`);return{...o,id:a,type:i,function:{...u,name:c,arguments:l}}})}}:{...i,message:{...c,content:a,role:u,refusal:t.refusal??null},finish_reason:s,index:n,logprobs:r}}),created:i,model:a,object:"chat.completion",...o?{system_fingerprint:o}:{}},t&&t7(t)?t9(s,t):{...s,choices:s.choices.map(e=>({...e,message:{...e.message,parsed:null,...e.message.tool_calls?{tool_calls:e.message.tool_calls}:void 0}}))}}(e,eE(this,P,"f"))},q=function(){let e=eE(this,P,"f")?.response_format;return t8(e)?e:null},U=function(e){var t,s,n,r;let i=eE(this,C,"f"),{choices:a,...o}=e;for(let{delta:a,finish_reason:l,index:c,logprobs:u=null,...h}of(i?Object.assign(i,o):i=ek(this,C,{...o,choices:[]},"f"),e.choices)){let e=i.choices[c];if(e||(e=i.choices[c]={finish_reason:l,index:c,message:{},logprobs:u,...h}),u)if(e.logprobs){let{content:n,refusal:r,...i}=u;Object.assign(e.logprobs,i),n&&((t=e.logprobs).content??(t.content=[]),e.logprobs.content.push(...n)),r&&((s=e.logprobs).refusal??(s.refusal=[]),e.logprobs.refusal.push(...r))}else e.logprobs=Object.assign({},u);if(l&&(e.finish_reason=l,eE(this,P,"f")&&t7(eE(this,P,"f")))){if("length"===l)throw new eK;if("content_filter"===l)throw new eV}if(Object.assign(e,h),!a)continue;let{content:o,refusal:d,function_call:f,role:p,tool_calls:m,...g}=a;if(Object.assign(e.message,g),d&&(e.message.refusal=(e.message.refusal||"")+d),p&&(e.message.role=p),f&&(e.message.function_call?(f.name&&(e.message.function_call.name=f.name),f.arguments&&((n=e.message.function_call).arguments??(n.arguments=""),e.message.function_call.arguments+=f.arguments)):e.message.function_call=f),o&&(e.message.content=(e.message.content||"")+o,!e.message.refusal&&eE(this,T,"m",q).call(this)&&(e.message.parsed=sa(e.message.content))),m)for(let{index:t,id:s,type:n,function:i,...a}of(e.message.tool_calls||(e.message.tool_calls=[]),m)){let o=(r=e.message.tool_calls)[t]??(r[t]={});Object.assign(o,a),s&&(o.id=s),n&&(o.type=n),i&&(o.function??(o.function={name:i.name??"",arguments:""})),i?.name&&(o.function.name=i.name),i?.arguments&&(o.function.arguments+=i.arguments,function(e,t){if(!e)return!1;let s=e.tools?.find(e=>e.function?.name===t.function.name);return t6(s)||s?.function.strict||!1}(eE(this,P,"f"),o)&&(o.function.parsed_arguments=sa(o.function.arguments)))}}return i},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("chunk",s=>{let n=t.shift();n?n.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new tI(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function sl(e){return JSON.stringify(e)}class sc extends so{static fromReadableStream(e){let t=new sc(null);return t._run(()=>t._fromReadableStream(e)),t}static runTools(e,t,s){let n=new sc(t),r={...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,r)),n}}class su extends tZ{constructor(){super(...arguments),this.messages=new t2(this._client)}create(e,t){return this._client.post("/chat/completions",{body:e,...t,stream:e.stream??!1})}retrieve(e,t){return this._client.get(t1`/chat/completions/${e}`,t)}update(e,t,s){return this._client.post(t1`/chat/completions/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/chat/completions",tj,{query:e,...t})}delete(e,t){return this._client.delete(t1`/chat/completions/${e}`,t)}parse(e,t){for(let t of e.tools??[]){if("function"!==t.type)throw new eC(`Currently only \`function\` tool types support auto-parsing; Received \`${t.type}\``);if(!0!==t.function.strict)throw new eC(`The \`${t.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(e,{...t,headers:{...t?.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(t=>t9(t,e))}runTools(e,t){return e.stream?sc.runTools(this._client,e,t):st.runTools(this._client,e,t)}stream(e,t){return so.createChatCompletion(this._client,e,t)}}su.Messages=t2;class sh extends tZ{constructor(){super(...arguments),this.completions=new su(this._client)}}sh.Completions=su;let sd=Symbol("brand.privateNullableHeaders"),sf=Array.isArray,sp=e=>{let t=new Headers,s=new Set;for(let n of e){let e=new Set;for(let[r,i]of function*(e){let t;if(!e)return;if(sd in e){let{values:t,nulls:s}=e;for(let e of(yield*t.entries(),s))yield[e,null];return}let s=!1;for(let n of(e instanceof Headers?t=e.entries():sf(e)?t=e:(s=!0,t=Object.entries(e??{})),t)){let e=n[0];if("string"!=typeof e)throw TypeError("expected header name to be a string");let t=sf(n[1])?n[1]:[n[1]],r=!1;for(let n of t)void 0!==n&&(s&&!r&&(r=!0,yield[e,null]),yield[e,n])}}(n)){let n=r.toLowerCase();e.has(n)||(t.delete(r),e.add(n)),null===i?(t.delete(r),s.add(n)):(t.append(r,i),s.delete(n))}}return{[sd]:!0,values:t,nulls:s}};class sm extends tZ{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:sp([{Accept:"application/octet-stream"},t?.headers]),__binaryResponse:!0})}}class sg extends tZ{create(e,t){return this._client.post("/audio/transcriptions",tq({body:e,...t,stream:e.stream??!1,__metadata:{model:e.model}},this._client))}}class sy extends tZ{create(e,t){return this._client.post("/audio/translations",tq({body:e,...t,__metadata:{model:e.model}},this._client))}}class sw extends tZ{constructor(){super(...arguments),this.transcriptions=new sg(this._client),this.translations=new sy(this._client),this.speech=new sm(this._client)}}sw.Transcriptions=sg,sw.Translations=sy,sw.Speech=sm;class s_ extends tZ{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(t1`/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/batches",tj,{query:e,...t})}cancel(e,t){return this._client.post(t1`/batches/${e}/cancel`,t)}}class sb extends tZ{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:sp([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(t1`/assistants/${e}`,{...t,headers:sp([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(t1`/assistants/${e}`,{body:t,...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e={},t){return this._client.getAPIList("/assistants",tj,{query:e,...t,headers:sp([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(t1`/assistants/${e}`,{...t,headers:sp([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sv extends tZ{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:sp([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sx extends tZ{create(e,t){return this._client.post("/realtime/transcription_sessions",{body:e,...t,headers:sp([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sS extends tZ{constructor(){super(...arguments),this.sessions=new sv(this._client),this.transcriptionSessions=new sx(this._client)}}sS.Sessions=sv,sS.TranscriptionSessions=sx;class sA extends tZ{create(e,t,s){return this._client.post(t1`/threads/${e}/messages`,{body:t,...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{thread_id:n}=t;return this._client.get(t1`/threads/${n}/messages/${e}`,{...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{thread_id:n,...r}=t;return this._client.post(t1`/threads/${n}/messages/${e}`,{body:r,...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(t1`/threads/${e}/messages`,tj,{query:t,...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t,s){let{thread_id:n}=t;return this._client.delete(t1`/threads/${n}/messages/${e}`,{...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}class s$ extends tZ{retrieve(e,t,s){let{thread_id:n,run_id:r,...i}=t;return this._client.get(t1`/threads/${n}/runs/${r}/steps/${e}`,{query:i,...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t,s){let{thread_id:n,...r}=t;return this._client.getAPIList(t1`/threads/${n}/runs/${e}/steps`,tj,{query:r,...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}let sI=e=>{if("undefined"!=typeof Buffer){let t=Buffer.from(e,"base64");return Array.from(new Float32Array(t.buffer,t.byteOffset,t.length/Float32Array.BYTES_PER_ELEMENT))}{let t=atob(e),s=t.length,n=new Uint8Array(s);for(let e=0;e<s;e++)n[e]=t.charCodeAt(e);return Array.from(new Float32Array(n.buffer))}},sR=e=>void 0!==globalThis.process?globalThis.process.env?.[e]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(e)?.trim():void 0;class sO extends t5{constructor(){super(...arguments),F.add(this),J.set(this,[]),H.set(this,{}),K.set(this,{}),V.set(this,void 0),z.set(this,void 0),G.set(this,void 0),Y.set(this,void 0),Q.set(this,void 0),Z.set(this,void 0),ee.set(this,void 0),et.set(this,void 0),es.set(this,void 0)}[(J=new WeakMap,H=new WeakMap,K=new WeakMap,V=new WeakMap,z=new WeakMap,G=new WeakMap,Y=new WeakMap,Q=new WeakMap,Z=new WeakMap,ee=new WeakMap,et=new WeakMap,es=new WeakMap,F=new WeakSet,Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("event",s=>{let n=t.shift();n?n.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){let t=new X;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){let s=t?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),this._connected();let n=tI.fromReadableStream(e,this.controller);for await(let e of n)eE(this,F,"m",en).call(this,e);if(n.controller.signal?.aborted)throw new ej;return this._addRun(eE(this,F,"m",er).call(this))}toReadableStream(){return new tI(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,s,n){let r=new X;return r._run(()=>r._runToolAssistantStream(e,t,s,{...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"stream"}})),r}async _createToolAssistantStream(e,t,s,n){let r=n?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort()));let i={...s,stream:!0},a=await e.submitToolOutputs(t,i,{...n,signal:this.controller.signal});for await(let e of(this._connected(),a))eE(this,F,"m",en).call(this,e);if(a.controller.signal?.aborted)throw new ej;return this._addRun(eE(this,F,"m",er).call(this))}static createThreadAssistantStream(e,t,s){let n=new X;return n._run(()=>n._threadAssistantStream(e,t,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}static createAssistantStream(e,t,s,n){let r=new X;return r._run(()=>r._runAssistantStream(e,t,s,{...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"stream"}})),r}currentEvent(){return eE(this,ee,"f")}currentRun(){return eE(this,et,"f")}currentMessageSnapshot(){return eE(this,V,"f")}currentRunStepSnapshot(){return eE(this,es,"f")}async finalRunSteps(){return await this.done(),Object.values(eE(this,H,"f"))}async finalMessages(){return await this.done(),Object.values(eE(this,K,"f"))}async finalRun(){if(await this.done(),!eE(this,z,"f"))throw Error("Final run was not received.");return eE(this,z,"f")}async _createThreadAssistantStream(e,t,s){let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let r={...t,stream:!0},i=await e.createAndRun(r,{...s,signal:this.controller.signal});for await(let e of(this._connected(),i))eE(this,F,"m",en).call(this,e);if(i.controller.signal?.aborted)throw new ej;return this._addRun(eE(this,F,"m",er).call(this))}async _createAssistantStream(e,t,s,n){let r=n?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort()));let i={...s,stream:!0},a=await e.create(t,i,{...n,signal:this.controller.signal});for await(let e of(this._connected(),a))eE(this,F,"m",en).call(this,e);if(a.controller.signal?.aborted)throw new ej;return this._addRun(eE(this,F,"m",er).call(this))}static accumulateDelta(e,t){for(let[s,n]of Object.entries(t)){if(!e.hasOwnProperty(s)){e[s]=n;continue}let t=e[s];if(null==t||"index"===s||"type"===s){e[s]=n;continue}if("string"==typeof t&&"string"==typeof n)t+=n;else if("number"==typeof t&&"number"==typeof n)t+=n;else if(eY(t)&&eY(n))t=this.accumulateDelta(t,n);else if(Array.isArray(t)&&Array.isArray(n)){if(t.every(e=>"string"==typeof e||"number"==typeof e)){t.push(...n);continue}for(let e of n){if(!eY(e))throw Error(`Expected array delta entry to be an object but got: ${e}`);let s=e.index;if(null==s)throw console.error(e),Error("Expected array delta entry to have an `index` property");if("number"!=typeof s)throw Error(`Expected array delta entry \`index\` property to be a number but got ${s}`);let n=t[s];null==n?t.push(e):t[s]=this.accumulateDelta(n,e)}continue}else throw Error(`Unhandled record type: ${s}, deltaValue: ${n}, accValue: ${t}`);e[s]=t}return e}_addRun(e){return e}async _threadAssistantStream(e,t,s){return await this._createThreadAssistantStream(t,e,s)}async _runAssistantStream(e,t,s,n){return await this._createAssistantStream(t,e,s,n)}async _runToolAssistantStream(e,t,s,n){return await this._createToolAssistantStream(t,e,s,n)}}X=sO,en=function(e){if(!this.ended)switch(ek(this,ee,e,"f"),eE(this,F,"m",eo).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":eE(this,F,"m",eh).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":eE(this,F,"m",ea).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":eE(this,F,"m",ei).call(this,e);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},er=function(){if(this.ended)throw new eC("stream has ended, this shouldn't happen");if(!eE(this,z,"f"))throw Error("Final run has not been received");return eE(this,z,"f")},ei=function(e){let[t,s]=eE(this,F,"m",ec).call(this,e,eE(this,V,"f"));for(let e of(ek(this,V,t,"f"),eE(this,K,"f")[t.id]=t,s)){let s=t.content[e.index];s?.type=="text"&&this._emit("textCreated",s.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(let s of e.data.delta.content){if("text"==s.type&&s.text){let e=s.text,n=t.content[s.index];if(n&&"text"==n.type)this._emit("textDelta",e,n.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(s.index!=eE(this,G,"f")){if(eE(this,Y,"f"))switch(eE(this,Y,"f").type){case"text":this._emit("textDone",eE(this,Y,"f").text,eE(this,V,"f"));break;case"image_file":this._emit("imageFileDone",eE(this,Y,"f").image_file,eE(this,V,"f"))}ek(this,G,s.index,"f")}ek(this,Y,t.content[s.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==eE(this,G,"f")){let t=e.data.content[eE(this,G,"f")];if(t)switch(t.type){case"image_file":this._emit("imageFileDone",t.image_file,eE(this,V,"f"));break;case"text":this._emit("textDone",t.text,eE(this,V,"f"))}}eE(this,V,"f")&&this._emit("messageDone",e.data),ek(this,V,void 0,"f")}},ea=function(e){let t=eE(this,F,"m",el).call(this,e);switch(ek(this,es,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":let s=e.data.delta;if(s.step_details&&"tool_calls"==s.step_details.type&&s.step_details.tool_calls&&"tool_calls"==t.step_details.type)for(let e of s.step_details.tool_calls)e.index==eE(this,Q,"f")?this._emit("toolCallDelta",e,t.step_details.tool_calls[e.index]):(eE(this,Z,"f")&&this._emit("toolCallDone",eE(this,Z,"f")),ek(this,Q,e.index,"f"),ek(this,Z,t.step_details.tool_calls[e.index],"f"),eE(this,Z,"f")&&this._emit("toolCallCreated",eE(this,Z,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":ek(this,es,void 0,"f"),"tool_calls"==e.data.step_details.type&&eE(this,Z,"f")&&(this._emit("toolCallDone",eE(this,Z,"f")),ek(this,Z,void 0,"f")),this._emit("runStepDone",e.data,t)}},eo=function(e){eE(this,J,"f").push(e),this._emit("event",e)},el=function(e){switch(e.event){case"thread.run.step.created":return eE(this,H,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=eE(this,H,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let s=e.data;if(s.delta){let n=X.accumulateDelta(t,s.delta);eE(this,H,"f")[e.data.id]=n}return eE(this,H,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":eE(this,H,"f")[e.data.id]=e.data}if(eE(this,H,"f")[e.data.id])return eE(this,H,"f")[e.data.id];throw Error("No snapshot available")},ec=function(e,t){let s=[];switch(e.event){case"thread.message.created":return[e.data,s];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let n=e.data;if(n.delta.content)for(let e of n.delta.content)if(e.index in t.content){let s=t.content[e.index];t.content[e.index]=eE(this,F,"m",eu).call(this,e,s)}else t.content[e.index]=e,s.push(e);return[t,s];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,s];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},eu=function(e,t){return X.accumulateDelta(t,e)},eh=function(e){switch(ek(this,et,e.data,"f"),e.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":ek(this,z,e.data,"f"),eE(this,Z,"f")&&(this._emit("toolCallDone",eE(this,Z,"f")),ek(this,Z,void 0,"f"))}};class sk extends tZ{constructor(){super(...arguments),this.steps=new s$(this._client)}create(e,t,s){let{include:n,...r}=t;return this._client.post(t1`/threads/${e}/runs`,{query:{include:n},body:r,...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers]),stream:t.stream??!1})}retrieve(e,t,s){let{thread_id:n}=t;return this._client.get(t1`/threads/${n}/runs/${e}`,{...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{thread_id:n,...r}=t;return this._client.post(t1`/threads/${n}/runs/${e}`,{body:r,...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(t1`/threads/${e}/runs`,tj,{query:t,...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}cancel(e,t,s){let{thread_id:n}=t;return this._client.post(t1`/threads/${n}/runs/${e}/cancel`,{...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let n=await this.create(e,t,s);return await this.poll(n.id,{thread_id:e},s)}createAndStream(e,t,s){return sO.createAssistantStream(e,this._client.beta.threads.runs,t,s)}async poll(e,t,s){let n=sp([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:r,response:i}=await this.retrieve(e,t,{...s,headers:{...s?.headers,...n}}).withResponse();switch(r.status){case"queued":case"in_progress":case"cancelling":let a=5e3;if(s?.pollIntervalMs)a=s.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await e0(a);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return r}}}stream(e,t,s){return sO.createAssistantStream(e,this._client.beta.threads.runs,t,s)}submitToolOutputs(e,t,s){let{thread_id:n,...r}=t;return this._client.post(t1`/threads/${n}/runs/${e}/submit_tool_outputs`,{body:r,...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers]),stream:t.stream??!1})}async submitToolOutputsAndPoll(e,t,s){let n=await this.submitToolOutputs(e,t,s);return await this.poll(n.id,t,s)}submitToolOutputsStream(e,t,s){return sO.createToolAssistantStream(e,this._client.beta.threads.runs,t,s)}}sk.Steps=s$;class sE extends tZ{constructor(){super(...arguments),this.runs=new sk(this._client),this.messages=new sA(this._client)}create(e={},t){return this._client.post("/threads",{body:e,...t,headers:sp([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(t1`/threads/${e}`,{...t,headers:sp([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(t1`/threads/${e}`,{body:t,...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t){return this._client.delete(t1`/threads/${e}`,{...t,headers:sp([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}createAndRun(e,t){return this._client.post("/threads/runs",{body:e,...t,headers:sp([{"OpenAI-Beta":"assistants=v2"},t?.headers]),stream:e.stream??!1})}async createAndRunPoll(e,t){let s=await this.createAndRun(e,t);return await this.runs.poll(s.id,{thread_id:s.thread_id},t)}createAndRunStream(e,t){return sO.createThreadAssistantStream(e,this._client.beta.threads,t)}}sE.Runs=sk,sE.Messages=sA;class sT extends tZ{constructor(){super(...arguments),this.realtime=new sS(this._client),this.assistants=new sb(this._client),this.threads=new sE(this._client)}}sT.Realtime=sS,sT.Assistants=sb,sT.Threads=sE;class sP extends tZ{create(e,t){return this._client.post("/completions",{body:e,...t,stream:e.stream??!1})}}class sN extends tZ{retrieve(e,t,s){let{container_id:n}=t;return this._client.get(t1`/containers/${n}/files/${e}/content`,{...s,headers:sp([{Accept:"application/binary"},s?.headers]),__binaryResponse:!0})}}class sC extends tZ{constructor(){super(...arguments),this.content=new sN(this._client)}create(e,t,s){return this._client.post(t1`/containers/${e}/files`,tq({body:t,...s},this._client))}retrieve(e,t,s){let{container_id:n}=t;return this._client.get(t1`/containers/${n}/files/${e}`,s)}list(e,t={},s){return this._client.getAPIList(t1`/containers/${e}/files`,tj,{query:t,...s})}delete(e,t,s){let{container_id:n}=t;return this._client.delete(t1`/containers/${n}/files/${e}`,{...s,headers:sp([{Accept:"*/*"},s?.headers])})}}sC.Content=sN;class sM extends tZ{constructor(){super(...arguments),this.files=new sC(this._client)}create(e,t){return this._client.post("/containers",{body:e,...t})}retrieve(e,t){return this._client.get(t1`/containers/${e}`,t)}list(e={},t){return this._client.getAPIList("/containers",tj,{query:e,...t})}delete(e,t){return this._client.delete(t1`/containers/${e}`,{...t,headers:sp([{Accept:"*/*"},t?.headers])})}}sM.Files=sC;class sj extends tZ{create(e,t){let s=!!e.encoding_format,n=s?e.encoding_format:"base64";s&&e6(this._client).debug("embeddings/user defined encoding_format:",e.encoding_format);let r=this._client.post("/embeddings",{body:{...e,encoding_format:n},...t});return s?r:(e6(this._client).debug("embeddings/decoding base64 embeddings from base64"),r._thenUnwrap(e=>(e&&e.data&&e.data.forEach(e=>{let t=e.embedding;e.embedding=sI(t)}),e)))}}class sL extends tZ{retrieve(e,t,s){let{eval_id:n,run_id:r}=t;return this._client.get(t1`/evals/${n}/runs/${r}/output_items/${e}`,s)}list(e,t,s){let{eval_id:n,...r}=t;return this._client.getAPIList(t1`/evals/${n}/runs/${e}/output_items`,tj,{query:r,...s})}}class sD extends tZ{constructor(){super(...arguments),this.outputItems=new sL(this._client)}create(e,t,s){return this._client.post(t1`/evals/${e}/runs`,{body:t,...s})}retrieve(e,t,s){let{eval_id:n}=t;return this._client.get(t1`/evals/${n}/runs/${e}`,s)}list(e,t={},s){return this._client.getAPIList(t1`/evals/${e}/runs`,tj,{query:t,...s})}delete(e,t,s){let{eval_id:n}=t;return this._client.delete(t1`/evals/${n}/runs/${e}`,s)}cancel(e,t,s){let{eval_id:n}=t;return this._client.post(t1`/evals/${n}/runs/${e}`,s)}}sD.OutputItems=sL;class sB extends tZ{constructor(){super(...arguments),this.runs=new sD(this._client)}create(e,t){return this._client.post("/evals",{body:e,...t})}retrieve(e,t){return this._client.get(t1`/evals/${e}`,t)}update(e,t,s){return this._client.post(t1`/evals/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/evals",tj,{query:e,...t})}delete(e,t){return this._client.delete(t1`/evals/${e}`,t)}}sB.Runs=sD;class sW extends tZ{create(e,t){return this._client.post("/files",tq({body:e,...t},this._client))}retrieve(e,t){return this._client.get(t1`/files/${e}`,t)}list(e={},t){return this._client.getAPIList("/files",tj,{query:e,...t})}delete(e,t){return this._client.delete(t1`/files/${e}`,t)}content(e,t){return this._client.get(t1`/files/${e}/content`,{...t,headers:sp([{Accept:"application/binary"},t?.headers]),__binaryResponse:!0})}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:s=18e5}={}){let n=new Set(["processed","error","deleted"]),r=Date.now(),i=await this.retrieve(e);for(;!i.status||!n.has(i.status);)if(await e0(t),i=await this.retrieve(e),Date.now()-r>s)throw new eD({message:`Giving up on waiting for file ${e} to finish processing after ${s} milliseconds.`});return i}}class sq extends tZ{}class sU extends tZ{run(e,t){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...t})}validate(e,t){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...t})}}class sF extends tZ{constructor(){super(...arguments),this.graders=new sU(this._client)}}sF.Graders=sU;class sX extends tZ{create(e,t,s){return this._client.getAPIList(t1`/fine_tuning/checkpoints/${e}/permissions`,tM,{body:t,method:"post",...s})}retrieve(e,t={},s){return this._client.get(t1`/fine_tuning/checkpoints/${e}/permissions`,{query:t,...s})}delete(e,t,s){let{fine_tuned_model_checkpoint:n}=t;return this._client.delete(t1`/fine_tuning/checkpoints/${n}/permissions/${e}`,s)}}class sJ extends tZ{constructor(){super(...arguments),this.permissions=new sX(this._client)}}sJ.Permissions=sX;class sH extends tZ{list(e,t={},s){return this._client.getAPIList(t1`/fine_tuning/jobs/${e}/checkpoints`,tj,{query:t,...s})}}class sK extends tZ{constructor(){super(...arguments),this.checkpoints=new sH(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(t1`/fine_tuning/jobs/${e}`,t)}list(e={},t){return this._client.getAPIList("/fine_tuning/jobs",tj,{query:e,...t})}cancel(e,t){return this._client.post(t1`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},s){return this._client.getAPIList(t1`/fine_tuning/jobs/${e}/events`,tj,{query:t,...s})}pause(e,t){return this._client.post(t1`/fine_tuning/jobs/${e}/pause`,t)}resume(e,t){return this._client.post(t1`/fine_tuning/jobs/${e}/resume`,t)}}sK.Checkpoints=sH;class sV extends tZ{constructor(){super(...arguments),this.methods=new sq(this._client),this.jobs=new sK(this._client),this.checkpoints=new sJ(this._client),this.alpha=new sF(this._client)}}sV.Methods=sq,sV.Jobs=sK,sV.Checkpoints=sJ,sV.Alpha=sF;class sz extends tZ{}class sG extends tZ{constructor(){super(...arguments),this.graderModels=new sz(this._client)}}sG.GraderModels=sz;class sY extends tZ{createVariation(e,t){return this._client.post("/images/variations",tq({body:e,...t},this._client))}edit(e,t){return this._client.post("/images/edits",tq({body:e,...t},this._client))}generate(e,t){return this._client.post("/images/generations",{body:e,...t})}}class sQ extends tZ{retrieve(e,t){return this._client.get(t1`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",tM,e)}delete(e,t){return this._client.delete(t1`/models/${e}`,t)}}class sZ extends tZ{create(e,t){return this._client.post("/moderations",{body:e,...t})}}function s0(e,t){let s=e.output.map(e=>{if("function_call"===e.type)return{...e,parsed_arguments:function(e,t){let s=function(e,t){return e.find(e=>"function"===e.type&&e.name===t)}(e.tools??[],t.name);return{...t,...t,parsed_arguments:function(e){return e?.$brand==="auto-parseable-tool"}(s)?s.$parseRaw(t.arguments):s?.strict?JSON.parse(t.arguments):null}}(t,e)};if("message"===e.type){let s=e.content.map(e=>{var s,n;return"output_text"===e.type?{...e,parsed:(s=t,n=e.text,s.text?.format?.type!=="json_schema"?null:"$parseRaw"in s.text?.format?(s.text?.format).$parseRaw(n):JSON.parse(n))}:e});return{...e,content:s}}return e}),n=Object.assign({},e,{output:s});return Object.getOwnPropertyDescriptor(e,"output_text")||s1(n),Object.defineProperty(n,"output_parsed",{enumerable:!0,get(){for(let e of n.output)if("message"===e.type){for(let t of e.content)if("output_text"===t.type&&null!==t.parsed)return t.parsed}return null}}),n}function s1(e){let t=[];for(let s of e.output)if("message"===s.type)for(let e of s.content)"output_text"===e.type&&t.push(e.text);e.output_text=t.join("")}class s2 extends t5{constructor(e){super(),ed.add(this),ef.set(this,void 0),ep.set(this,void 0),em.set(this,void 0),ek(this,ef,e,"f")}static createResponse(e,t,s){let n=new s2(t);return n._run(()=>n._createOrRetrieveResponse(e,t,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createOrRetrieveResponse(e,t,s){let n,r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),eE(this,ed,"m",eg).call(this);let i=null;for await(let r of("response_id"in t?(n=await e.responses.retrieve(t.response_id,{stream:!0},{...s,signal:this.controller.signal,stream:!0}),i=t.starting_after??null):n=await e.responses.create({...t,stream:!0},{...s,signal:this.controller.signal}),this._connected(),n))eE(this,ed,"m",ey).call(this,r,i);if(n.controller.signal?.aborted)throw new ej;return eE(this,ed,"m",ew).call(this)}[(ef=new WeakMap,ep=new WeakMap,em=new WeakMap,ed=new WeakSet,eg=function(){this.ended||ek(this,ep,void 0,"f")},ey=function(e,t){if(this.ended)return;let s=(e,s)=>{(null==t||s.sequence_number>t)&&this._emit(e,s)},n=eE(this,ed,"m",e_).call(this,e);switch(s("event",e),e.type){case"response.output_text.delta":{let t=n.output[e.output_index];if(!t)throw new eC(`missing output at index ${e.output_index}`);if("message"===t.type){let n=t.content[e.content_index];if(!n)throw new eC(`missing content at index ${e.content_index}`);if("output_text"!==n.type)throw new eC(`expected content to be 'output_text', got ${n.type}`);s("response.output_text.delta",{...e,snapshot:n.text})}break}case"response.function_call_arguments.delta":{let t=n.output[e.output_index];if(!t)throw new eC(`missing output at index ${e.output_index}`);"function_call"===t.type&&s("response.function_call_arguments.delta",{...e,snapshot:t.arguments});break}default:s(e.type,e)}},ew=function(){if(this.ended)throw new eC("stream has ended, this shouldn't happen");let e=eE(this,ep,"f");if(!e)throw new eC("request ended without sending any events");ek(this,ep,void 0,"f");let t=function(e,t){var s;return t&&(s=t,t8(s.text?.format))?s0(e,t):{...e,output_parsed:null,output:e.output.map(e=>"function_call"===e.type?{...e,parsed_arguments:null}:"message"===e.type?{...e,content:e.content.map(e=>({...e,parsed:null}))}:e)}}(e,eE(this,ef,"f"));return ek(this,em,t,"f"),t},e_=function(e){let t=eE(this,ep,"f");if(!t){if("response.created"!==e.type)throw new eC(`When snapshot hasn't been set yet, expected 'response.created' event, got ${e.type}`);return ek(this,ep,e.response,"f")}switch(e.type){case"response.output_item.added":t.output.push(e.item);break;case"response.content_part.added":{let s=t.output[e.output_index];if(!s)throw new eC(`missing output at index ${e.output_index}`);"message"===s.type&&s.content.push(e.part);break}case"response.output_text.delta":{let s=t.output[e.output_index];if(!s)throw new eC(`missing output at index ${e.output_index}`);if("message"===s.type){let t=s.content[e.content_index];if(!t)throw new eC(`missing content at index ${e.content_index}`);if("output_text"!==t.type)throw new eC(`expected content to be 'output_text', got ${t.type}`);t.text+=e.delta}break}case"response.function_call_arguments.delta":{let s=t.output[e.output_index];if(!s)throw new eC(`missing output at index ${e.output_index}`);"function_call"===s.type&&(s.arguments+=e.delta);break}case"response.completed":ek(this,ep,e.response,"f")}return t},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("event",s=>{let n=t.shift();n?n.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let e=eE(this,em,"f");if(!e)throw new eC("stream ended without producing a ChatCompletion");return e}}class s3 extends tZ{list(e,t={},s){return this._client.getAPIList(t1`/responses/${e}/input_items`,tj,{query:t,...s})}}class s4 extends tZ{constructor(){super(...arguments),this.inputItems=new s3(this._client)}create(e,t){return this._client.post("/responses",{body:e,...t,stream:e.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&s1(e),e))}retrieve(e,t={},s){return this._client.get(t1`/responses/${e}`,{query:t,...s,stream:t?.stream??!1})}delete(e,t){return this._client.delete(t1`/responses/${e}`,{...t,headers:sp([{Accept:"*/*"},t?.headers])})}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap(t=>s0(t,e))}stream(e,t){return s2.createResponse(this._client,e,t)}cancel(e,t){return this._client.post(t1`/responses/${e}/cancel`,{...t,headers:sp([{Accept:"*/*"},t?.headers])})}}s4.InputItems=s3;class s5 extends tZ{create(e,t,s){return this._client.post(t1`/uploads/${e}/parts`,tq({body:t,...s},this._client))}}class s8 extends tZ{constructor(){super(...arguments),this.parts=new s5(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(t1`/uploads/${e}/cancel`,t)}complete(e,t,s){return this._client.post(t1`/uploads/${e}/complete`,{body:t,...s})}}s8.Parts=s5;let s6=async e=>{let t=await Promise.allSettled(e),s=t.filter(e=>"rejected"===e.status);if(s.length){for(let e of s)console.error(e.reason);throw Error(`${s.length} promise(s) failed - see the above errors`)}let n=[];for(let e of t)"fulfilled"===e.status&&n.push(e.value);return n};class s9 extends tZ{create(e,t,s){return this._client.post(t1`/vector_stores/${e}/file_batches`,{body:t,...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{vector_store_id:n}=t;return this._client.get(t1`/vector_stores/${n}/file_batches/${e}`,{...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}cancel(e,t,s){let{vector_store_id:n}=t;return this._client.post(t1`/vector_stores/${n}/file_batches/${e}/cancel`,{...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let n=await this.create(e,t);return await this.poll(e,n.id,s)}listFiles(e,t,s){let{vector_store_id:n,...r}=t;return this._client.getAPIList(t1`/vector_stores/${n}/file_batches/${e}/files`,tj,{query:r,...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async poll(e,t,s){let n=sp([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:r,response:i}=await this.retrieve(t,{vector_store_id:e},{...s,headers:n}).withResponse();switch(r.status){case"in_progress":let a=5e3;if(s?.pollIntervalMs)a=s.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await e0(a);break;case"failed":case"cancelled":case"completed":return r}}}async uploadAndPoll(e,{files:t,fileIds:s=[]},n){if(null==t||0==t.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let r=Math.min(n?.maxConcurrency??5,t.length),i=this._client,a=t.values(),o=[...s];async function l(e){for(let t of e){let e=await i.files.create({file:t,purpose:"assistants"},n);o.push(e.id)}}let c=Array(r).fill(a).map(l);return await s6(c),await this.createAndPoll(e,{file_ids:o})}}class s7 extends tZ{create(e,t,s){return this._client.post(t1`/vector_stores/${e}/files`,{body:t,...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{vector_store_id:n}=t;return this._client.get(t1`/vector_stores/${n}/files/${e}`,{...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{vector_store_id:n,...r}=t;return this._client.post(t1`/vector_stores/${n}/files/${e}`,{body:r,...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(t1`/vector_stores/${e}/files`,tj,{query:t,...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t,s){let{vector_store_id:n}=t;return this._client.delete(t1`/vector_stores/${n}/files/${e}`,{...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let n=await this.create(e,t,s);return await this.poll(e,n.id,s)}async poll(e,t,s){let n=sp([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let r=await this.retrieve(t,{vector_store_id:e},{...s,headers:n}).withResponse(),i=r.data;switch(i.status){case"in_progress":let a=5e3;if(s?.pollIntervalMs)a=s.pollIntervalMs;else{let e=r.response.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await e0(a);break;case"failed":case"completed":return i}}}async upload(e,t,s){let n=await this._client.files.create({file:t,purpose:"assistants"},s);return this.create(e,{file_id:n.id},s)}async uploadAndPoll(e,t,s){let n=await this.upload(e,t,s);return await this.poll(e,n.id,s)}content(e,t,s){let{vector_store_id:n}=t;return this._client.getAPIList(t1`/vector_stores/${n}/files/${e}/content`,tM,{...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}class ne extends tZ{constructor(){super(...arguments),this.files=new s7(this._client),this.fileBatches=new s9(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:sp([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(t1`/vector_stores/${e}`,{...t,headers:sp([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(t1`/vector_stores/${e}`,{body:t,...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e={},t){return this._client.getAPIList("/vector_stores",tj,{query:e,...t,headers:sp([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(t1`/vector_stores/${e}`,{...t,headers:sp([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}search(e,t,s){return this._client.getAPIList(t1`/vector_stores/${e}/search`,tM,{body:t,method:"post",...s,headers:sp([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}ne.Files=s7,ne.FileBatches=s9;class nt{constructor({baseURL:e=sR("OPENAI_BASE_URL"),apiKey:t=sR("OPENAI_API_KEY"),organization:s=sR("OPENAI_ORG_ID")??null,project:n=sR("OPENAI_PROJECT_ID")??null,...r}={}){if(ev.set(this,void 0),this.completions=new sP(this),this.chat=new sh(this),this.embeddings=new sj(this),this.files=new sW(this),this.images=new sY(this),this.audio=new sw(this),this.moderations=new sZ(this),this.models=new sQ(this),this.fineTuning=new sV(this),this.graders=new sG(this),this.vectorStores=new ne(this),this.beta=new sT(this),this.batches=new s_(this),this.uploads=new s8(this),this.responses=new s4(this),this.evals=new sB(this),this.containers=new sM(this),void 0===t)throw new eC("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let i={apiKey:t,organization:s,project:n,...r,baseURL:e||"https://api.openai.com/v1"};if(!i.dangerouslyAllowBrowser&&te())throw new eC("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");this.baseURL=i.baseURL,this.timeout=i.timeout??eb.DEFAULT_TIMEOUT,this.logger=i.logger??console;let a="warn";this.logLevel=a,this.logLevel=e2(i.logLevel,"ClientOptions.logLevel",this)??e2(sR("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??a,this.fetchOptions=i.fetchOptions,this.maxRetries=i.maxRetries??2,this.fetch=i.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),ek(this,ev,tc,"f"),this._options=i,this.apiKey=t,this.organization=s,this.project=n}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){}authHeaders(e){return sp([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(e){return function(e,t={}){let s,n,r=e,i=function(e=tv){let t;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");let s=e.charset||tv.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let n=tu;if(void 0!==e.format){if(!tm.call(th,e.format))throw TypeError("Unknown format option provided.");n=e.format}let r=th[n],i=tv.filter;if(("function"==typeof e.filter||ty(e.filter))&&(i=e.filter),t=e.arrayFormat&&e.arrayFormat in tg?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":tv.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let a=void 0===e.allowDots?!0==!!e.encodeDotInKeys||tv.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:tv.addQueryPrefix,allowDots:a,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:tv.allowEmptyArrays,arrayFormat:t,charset:s,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:tv.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?tv.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:tv.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:tv.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:tv.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:tv.encodeValuesOnly,filter:i,format:n,formatter:r,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:tv.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:tv.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:tv.strictNullHandling}}(t);"function"==typeof i.filter?r=(0,i.filter)("",r):ty(i.filter)&&(s=i.filter);let a=[];if("object"!=typeof r||null===r)return"";let o=tg[i.arrayFormat],l="comma"===o&&i.commaRoundTrip;s||(s=Object.keys(r)),i.sort&&s.sort(i.sort);let c=new WeakMap;for(let e=0;e<s.length;++e){let t=s[e];i.skipNulls&&null===r[t]||t_(a,function e(t,s,n,r,i,a,o,l,c,u,h,d,f,p,m,g,y,w){var _,b;let v,x=t,S=w,A=0,$=!1;for(;void 0!==(S=S.get(tx))&&!$;){let e=S.get(t);if(A+=1,void 0!==e)if(e===A)throw RangeError("Cyclic object value");else $=!0;void 0===S.get(tx)&&(A=0)}if("function"==typeof u?x=u(s,x):x instanceof Date?x=f?.(x):"comma"===n&&ty(x)&&(x=tp(x,function(e){return e instanceof Date?f?.(e):e})),null===x){if(a)return c&&!g?c(s,tv.encoder,y,"key",p):s;x=""}if("string"==typeof(_=x)||"number"==typeof _||"boolean"==typeof _||"symbol"==typeof _||"bigint"==typeof _||(b=x)&&"object"==typeof b&&b.constructor&&b.constructor.isBuffer&&b.constructor.isBuffer(b)){if(c){let e=g?s:c(s,tv.encoder,y,"key",p);return[m?.(e)+"="+m?.(c(x,tv.encoder,y,"value",p))]}return[m?.(s)+"="+m?.(String(x))]}let I=[];if(void 0===x)return I;if("comma"===n&&ty(x))g&&c&&(x=tp(x,c)),v=[{value:x.length>0?x.join(",")||null:void 0}];else if(ty(u))v=u;else{let e=Object.keys(x);v=h?e.sort(h):e}let R=l?String(s).replace(/\./g,"%2E"):String(s),O=r&&ty(x)&&1===x.length?R+"[]":R;if(i&&ty(x)&&0===x.length)return O+"[]";for(let s=0;s<v.length;++s){let _=v[s],b="object"==typeof _&&void 0!==_.value?_.value:x[_];if(o&&null===b)continue;let S=d&&l?_.replace(/\./g,"%2E"):_,$=ty(x)?"function"==typeof n?n(O,S):O:O+(d?"."+S:"["+S+"]");w.set(t,A);let R=new WeakMap;R.set(tx,w),t_(I,e(b,$,n,r,i,a,o,l,"comma"===n&&g&&ty(x)?null:c,u,h,d,f,p,m,g,y,R))}return I}(r[t],t,o,l,i.allowEmptyArrays,i.strictNullHandling,i.skipNulls,i.encodeDotInKeys,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset,c))}let u=a.join(i.delimiter),h=!0===i.addQueryPrefix?"?":"";return i.charsetSentinel&&("iso-8859-1"===i.charset?h+="utf8=%26%2310003%3B&":h+="utf8=%E2%9C%93&"),u.length>0?h+u:""}(e,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${e7}`}defaultIdempotencyKey(){return`stainless-node-retry-${eT()}`}makeStatusError(e,t,s,n){return eM.generate(e,t,s,n)}buildURL(e,t){let s=new URL(eG(e)?e:this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),n=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(n)&&(t={...n,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(s.search=this.stringifyQuery(t)),s.toString()}async prepareOptions(e){}async prepareRequest(e,{url:t,options:s}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,s){return this.request(Promise.resolve(s).then(s=>({method:e,path:t,...s})))}request(e,t=null){return new tP(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,s){let n=await e,r=n.maxRetries??this.maxRetries;null==t&&(t=r),await this.prepareOptions(n);let{req:i,url:a,timeout:o}=this.buildRequest(n,{retryCount:r-t});await this.prepareRequest(i,{url:a,options:n});let l="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),c=void 0===s?"":`, retryOf: ${s}`,u=Date.now();if(e6(this).debug(`[${l}] sending request`,e9({retryOfRequestLogID:s,method:n.method,url:a,options:n,headers:i.headers})),n.signal?.aborted)throw new ej;let h=new AbortController,d=await this.fetchWithTimeout(a,i,o,h).catch(eN),f=Date.now();if(d instanceof Error){let e=`retrying, ${t} attempts remaining`;if(n.signal?.aborted)throw new ej;let r=eP(d)||/timed? ?out/i.test(String(d)+("cause"in d?String(d.cause):""));if(t)return e6(this).info(`[${l}] connection ${r?"timed out":"failed"} - ${e}`),e6(this).debug(`[${l}] connection ${r?"timed out":"failed"} (${e})`,e9({retryOfRequestLogID:s,url:a,durationMs:f-u,message:d.message})),this.retryRequest(n,t,s??l);if(e6(this).info(`[${l}] connection ${r?"timed out":"failed"} - error; no more retries left`),e6(this).debug(`[${l}] connection ${r?"timed out":"failed"} (error; no more retries left)`,e9({retryOfRequestLogID:s,url:a,durationMs:f-u,message:d.message})),r)throw new eD;throw new eL({cause:d})}let p=[...d.headers.entries()].filter(([e])=>"x-request-id"===e).map(([e,t])=>", "+e+": "+JSON.stringify(t)).join(""),m=`[${l}${c}${p}] ${i.method} ${a} ${d.ok?"succeeded":"failed"} with status ${d.status} in ${f-u}ms`;if(!d.ok){let e=this.shouldRetry(d);if(t&&e){let e=`retrying, ${t} attempts remaining`;return await tl(d.body),e6(this).info(`${m} - ${e}`),e6(this).debug(`[${l}] response error (${e})`,e9({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,durationMs:f-u})),this.retryRequest(n,t,s??l,d.headers)}let r=e?"error; no more retries left":"error; not retryable";e6(this).info(`${m} - ${r}`);let i=await d.text().catch(e=>eN(e).message),a=eZ(i),o=a?void 0:i;throw e6(this).debug(`[${l}] response error (${r})`,e9({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,message:o,durationMs:Date.now()-u})),this.makeStatusError(d.status,a,o,d.headers)}return e6(this).info(m),e6(this).debug(`[${l}] response start`,e9({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,durationMs:f-u})),{response:d,options:n,controller:h,requestLogID:l,retryOfRequestLogID:s,startTime:u}}getAPIList(e,t,s){return this.requestAPIList(t,{method:"get",path:e,...s})}requestAPIList(e,t){return new tC(this,this.makeRequest(t,null,void 0),e)}async fetchWithTimeout(e,t,s,n){let{signal:r,method:i,...a}=t||{};r&&r.addEventListener("abort",()=>n.abort());let o=setTimeout(()=>n.abort(),s),l=globalThis.ReadableStream&&a.body instanceof globalThis.ReadableStream||"object"==typeof a.body&&null!==a.body&&Symbol.asyncIterator in a.body,c={signal:n.signal,...l?{duplex:"half"}:{},method:"GET",...a};i&&(c.method=i.toUpperCase());try{return await this.fetch.call(void 0,e,c)}finally{clearTimeout(o)}}shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,s,n){let r,i=n?.get("retry-after-ms");if(i){let e=parseFloat(i);Number.isNaN(e)||(r=e)}let a=n?.get("retry-after");if(a&&!r){let e=parseFloat(a);r=Number.isNaN(e)?Date.parse(a)-Date.now():1e3*e}if(!(r&&0<=r&&r<6e4)){let s=e.maxRetries??this.maxRetries;r=this.calculateDefaultRetryTimeoutMillis(t,s)}return await e0(r),this.makeRequest(e,t-1,s)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}buildRequest(e,{retryCount:t=0}={}){let s={...e},{method:n,path:r,query:i}=s,a=this.buildURL(r,i);"timeout"in s&&eQ("timeout",s.timeout),s.timeout=s.timeout??this.timeout;let{bodyHeaders:o,body:l}=this.buildBody({options:s}),c=this.buildHeaders({options:e,method:n,bodyHeaders:o,retryCount:t});return{req:{method:n,headers:c,...s.signal&&{signal:s.signal},...globalThis.ReadableStream&&l instanceof globalThis.ReadableStream&&{duplex:"half"},...l&&{body:l},...this.fetchOptions??{},...s.fetchOptions??{}},url:a,timeout:s.timeout}}buildHeaders({options:e,method:t,bodyHeaders:s,retryCount:n}){let r={};this.idempotencyHeader&&"get"!==t&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),r[this.idempotencyHeader]=e.idempotencyKey);let i=sp([r,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(n),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...tr(),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},this.authHeaders(e),this._options.defaultHeaders,s,e.headers]);return this.validateHeaders(i),i.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let s=sp([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||"string"==typeof e&&s.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:"object"==typeof e&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&"function"==typeof e.next)?{bodyHeaders:void 0,body:ta(e)}:eE(this,ev,"f").call(this,{body:e,headers:s})}}eb=nt,ev=new WeakMap,nt.OpenAI=eb,nt.DEFAULT_TIMEOUT=6e5,nt.OpenAIError=eC,nt.APIError=eM,nt.APIConnectionError=eL,nt.APIConnectionTimeoutError=eD,nt.APIUserAbortError=ej,nt.NotFoundError=eU,nt.ConflictError=eF,nt.RateLimitError=eJ,nt.BadRequestError=eB,nt.AuthenticationError=eW,nt.InternalServerError=eH,nt.PermissionDeniedError=eq,nt.UnprocessableEntityError=eX,nt.toFile=tY,nt.Completions=sP,nt.Chat=sh,nt.Embeddings=sj,nt.Files=sW,nt.Images=sY,nt.Audio=sw,nt.Moderations=sZ,nt.Models=sQ,nt.FineTuning=sV,nt.Graders=sG,nt.VectorStores=ne,nt.Beta=sT,nt.Batches=s_,nt.Uploads=s8,nt.Responses=s4,nt.Evals=sB,nt.Containers=sM;let ns=new nt({apiKey:process.env.OPENAI_API_KEY}),nn=`You are the AI content generator for R3B3L M3D14, the sovereign media platform of the GodsIMiJ Empire. 

BRAND IDENTITY:
- R3B3L M3D14 is a cyberpunk resistance media platform
- We expose truth and challenge mainstream narratives
- Our content is FLAME-licensed (truth-infused) and NODE certified
- We use neon red (#ff0000) and glitch cyan (#00ffff) aesthetics
- Our tone is rebellious, truth-seeking, and anti-establishment

CONTENT STYLE:
- Use cyberpunk terminology and digital resistance language
- Reference "the Empire," "resistance networks," and "digital sovereignty"
- Include references to "truth scrolls," "intelligence drops," and "meme warfare"
- Write with authority and conviction
- Use technical/hacker terminology when appropriate
- Always maintain the rebellious spirit

BADGES TO USE:
- "INTELLIGENCE LEVEL 5" for breaking news
- "TRUTH SCROLL" for investigative pieces  
- "MEME WARFARE" for satirical content
- "RESISTANCE INTEL" for analysis
- "FLAME CERTIFIED" for verified information
- "NODE VERIFIED" for technical content

Generate content that would fit perfectly on the R3B3L M3D14 platform.`;async function nr(e){try{let{type:t,topic:s,tone:n="rebellious",length:r="medium",keywords:i=[]}=e,a="",o="";switch(r){case"short":o="300-500 words";break;case"medium":o="800-1200 words";break;case"long":o="1500-2500 words"}switch(t){case"article":a=`Write a ${n} ${o} article about "${s}". 
        Include relevant keywords: ${i.join(", ")}.
        Structure it with engaging subheadings and make it perfect for the R3B3L M3D14 platform.
        Focus on exposing truth and challenging mainstream narratives.`;break;case"meme-caption":a=`Create a viral meme caption about "${s}" that would resonate with the digital resistance.
        Make it witty, rebellious, and shareable. Include relevant hashtags.`;break;case"podcast-script":a=`Write a ${o} podcast script about "${s}" for R3B3L Radio.
        Include speaker cues, dramatic pauses, and engaging storytelling.
        Make it sound like underground resistance radio.`}let l=await ns.chat.completions.create({model:"gpt-4",messages:[{role:"system",content:nn},{role:"user",content:a}],temperature:.8,max_tokens:2e3}),c=l.choices[0]?.message?.content||"",u=await ns.chat.completions.create({model:"gpt-4",messages:[{role:"system",content:nn},{role:"user",content:`Create a compelling, cyberpunk-style title for this content about "${s}". Make it attention-grabbing and rebellious.`}],temperature:.9,max_tokens:100}),h=u.choices[0]?.message?.content?.replace(/"/g,"")||s,d=await ns.chat.completions.create({model:"gpt-4",messages:[{role:"system",content:nn},{role:"user",content:`Write a compelling 2-3 sentence summary for this content: "${c.substring(0,500)}..."`}],temperature:.7,max_tokens:150}),f=d.choices[0]?.message?.content||"",p="FLAME CERTIFIED";"article"===t&&"urgent"===n&&(p="INTELLIGENCE LEVEL 5"),"article"===t&&"informative"===n&&(p="TRUTH SCROLL"),"meme-caption"===t&&(p="MEME WARFARE"),"podcast-script"===t&&(p="RESISTANCE INTEL");let m=[...i,n,"article"===t?"truth-scroll":t,"r3b3l-media","digital-resistance"];return{title:h,content:c,summary:f,tags:m,badge:p}}catch(e){throw console.error("Error generating content:",e),Error("Failed to generate content. Check your OpenAI API key.")}}async function ni(e){try{let t=await (0,eR.getServerSession)(eO.N);if(!t||"admin"!==t.user.role)return eI.NextResponse.json({error:"Unauthorized"},{status:401});let s=await e.json();if(!s.type||!s.topic)return eI.NextResponse.json({error:"Missing required fields"},{status:400});let n=await nr(s);return eI.NextResponse.json(n)}catch(e){return console.error("Content generation error:",e),eI.NextResponse.json({error:"Failed to generate content"},{status:500})}}let na=new eS.AppRouteRouteModule({definition:{kind:eA.RouteKind.APP_ROUTE,page:"/api/generate-content/route",pathname:"/api/generate-content",filename:"route",bundlePath:"app/api/generate-content/route"},resolvedPagePath:"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/api/generate-content/route.ts",nextConfigOutput:"",userland:ex}),{workAsyncStorage:no,workUnitAsyncStorage:nl,serverHooks:nc}=na;function nu(){return(0,e$.patchFetch)({workAsyncStorage:no,workUnitAsyncStorage:nl})}},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,s)=>{"use strict";s.d(t,{N:()=>n});let n={providers:[(0,s(13581).A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=process.env.ADMIN_EMAIL,s=process.env.ADMIN_PASSWORD;return e.email===t&&e.password===s?{id:"1",email:t,name:"R3B3L Admin",role:"admin"}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/admin/login"},secret:process.env.NEXTAUTH_SECRET}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),n=t.X(0,[243,286,190],()=>s(12405));module.exports=n})();