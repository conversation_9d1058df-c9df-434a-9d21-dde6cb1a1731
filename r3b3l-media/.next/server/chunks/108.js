exports.id=108,exports.ids=[108],exports.modules={12032:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},13622:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/components/Providers.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/components/Providers.tsx","default")},15727:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23)),Promise.resolve().then(s.bind(s,30004)),Promise.resolve().then(s.bind(s,13622))},17552:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(60687),i=s(82136);function o({children:e}){return(0,r.jsx)(i.SessionProvider,{children:e})}},25184:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},29190:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var r=s(60687),i=s(85814),o=s.n(i),n=s(43210);function l(){let[e,t]=(0,n.useState)(!1);return(0,r.jsx)("nav",{className:"bg-black/90 backdrop-blur-sm border-b border-gray-800 sticky top-0 z-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(o(),{href:"/",className:"text-2xl font-bold neon-text",children:"R3B3L M3D14"})}),(0,r.jsx)("div",{className:"hidden md:block",children:(0,r.jsxs)("div",{className:"ml-10 flex items-baseline space-x-4",children:[(0,r.jsx)(o(),{href:"/",className:"text-gray-300 hover:text-white hover:neon-text px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Home"}),(0,r.jsx)(o(),{href:"/articles",className:"text-gray-300 hover:text-white hover:neon-text px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Articles"}),(0,r.jsx)(o(),{href:"/about",className:"text-gray-300 hover:text-white hover:neon-text px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"About"}),(0,r.jsx)("div",{className:"glitch-text px-3 py-2 text-sm font-medium",children:"\uD83D\uDD25 FLAME-LICENSED"})]})}),(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsx)("button",{onClick:()=>t(!e),className:"text-gray-400 hover:text-white hover:bg-gray-700 px-2 py-1 rounded-md",children:(0,r.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e?(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),e&&(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-800",children:[(0,r.jsx)(o(),{href:"/",className:"text-gray-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium",onClick:()=>t(!1),children:"Home"}),(0,r.jsx)(o(),{href:"/articles",className:"text-gray-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium",onClick:()=>t(!1),children:"Articles"}),(0,r.jsx)(o(),{href:"/about",className:"text-gray-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium",onClick:()=>t(!1),children:"About"})]})})]})})}},30004:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/components/Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/components/Navbar.tsx","default")},40327:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,85814,23)),Promise.resolve().then(s.bind(s,29190)),Promise.resolve().then(s.bind(s,17552))},61135:()=>{},98042:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>d});var r=s(37413);s(61135);var i=s(30004),o=s(4536),n=s.n(o);function l(){return(0,r.jsxs)("footer",{className:"bg-black/90 border-t border-gray-800 mt-auto",children:[(0,r.jsxs)("div",{className:"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"text-xl font-bold neon-text",children:"R3B3L M3D14"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"Sovereign media platform for the GodsIMiJ Empire. Truth-infused content for the ReBeLuTioN."}),(0,r.jsx)("div",{className:"glitch-text text-sm",children:"\uD83D\uDD25 FLAME-LICENSED CONTENT"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-white",children:"Navigation"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"/",className:"text-gray-400 hover:text-white hover:neon-text transition-colors",children:"Home"})}),(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"/articles",className:"text-gray-400 hover:text-white hover:neon-text transition-colors",children:"Articles"})}),(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"/about",className:"text-gray-400 hover:text-white hover:neon-text transition-colors",children:"About"})})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-white",children:"The Empire"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{className:"text-gray-400",children:"GodsIMiJ Empire"}),(0,r.jsx)("li",{className:"text-gray-400",children:"ReBeLuTioN Media Division"}),(0,r.jsx)("li",{className:"text-gray-400",children:"Anti-Mainstream Intelligence"}),(0,r.jsx)("li",{className:"glitch-text",children:"NODE Certified"})]})]})]}),(0,r.jsxs)("div",{className:"mt-8 pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center",children:[(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"\xa9 2024 R3B3L M3D14. All rights reserved to the Empire."}),(0,r.jsxs)("div",{className:"mt-4 md:mt-0 flex space-x-4 text-sm",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Powered by"}),(0,r.jsx)("span",{className:"neon-text",children:"FLAME"}),(0,r.jsx)("span",{className:"text-gray-400",children:"•"}),(0,r.jsx)("span",{className:"glitch-text",children:"NODE SEALED"})]})]})]}),(0,r.jsx)("div",{className:"node-seal",children:"NODE CERTIFIED • FLAME LICENSED"})]})}var a=s(13622);let d={title:"R3B3L M3D14 | Sovereign Media Platform",description:"Truth-infused articles, memes, podcasts, and counter-narratives. Official media arm of the GodsIMiJ Empire ReBeLuTioN.",keywords:["rebel media","alternative news","counter-narrative","flame licensed","node certified"],authors:[{name:"R3B3L M3D14 Team"}],creator:"GodsIMiJ Empire",publisher:"R3B3L M3D14",robots:"index, follow",openGraph:{title:"R3B3L M3D14 | Sovereign Media Platform",description:"Truth-infused content for the ReBeLuTioN",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"R3B3L M3D14",description:"Sovereign media platform for truth-seekers"}};function c({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:"min-h-screen flex flex-col bg-black text-white",children:(0,r.jsxs)(a.default,{children:[(0,r.jsx)(i.default,{}),(0,r.jsx)("main",{className:"flex-grow",children:e}),(0,r.jsx)(l,{})]})})})}}};