exports.id=379,exports.ids=[379],exports.modules={648:(e,t,i)=>{"use strict";e.exports=new(i(3016))("tag:yaml.org,2002:str",{kind:"scalar",construct:function(e){return null!==e?e:""}})},908:(module,exports,__webpack_require__)=>{"use strict";let yaml=__webpack_require__(1748),engines=exports=module.exports;engines.yaml={parse:yaml.safeLoad.bind(yaml),stringify:yaml.safeDump.bind(yaml)},engines.json={parse:JSON.parse.bind(JSON),stringify:function(e,t){let i=Object.assign({replacer:null,space:2},t);return JSON.stringify(e,i.replacer,i.space)}},engines.javascript={parse:function parse(str,options,wrap){try{return!1!==wrap&&(str="(function() {\nreturn "+str.trim()+";\n}());"),eval(str)||{}}catch(err){if(!1!==wrap&&/(unexpected|identifier)/i.test(err.message))return parse(str,options,!1);throw SyntaxError(err)}},stringify:function(){throw Error("stringifying JavaScript is not supported")}}},927:e=>{"use strict";e.exports=function(e){return null!=e&&("object"==typeof e||"function"==typeof e)}},1053:e=>{"use strict";e.exports=function(e){return"string"==typeof e&&"\uFEFF"===e.charAt(0)?e.slice(1):e}},1687:(e,t,i)=>{"use strict";try{var n;n=i(8759)}catch(e){"undefined"!=typeof window&&(n=window.esprima)}function r(e){if(null===e)return!1;try{var t="("+e+")",i=n.parse(t,{range:!0});if("Program"!==i.type||1!==i.body.length||"ExpressionStatement"!==i.body[0].type||"ArrowFunctionExpression"!==i.body[0].expression.type&&"FunctionExpression"!==i.body[0].expression.type)return!1;return!0}catch(e){return!1}}function s(e){var t,i="("+e+")",r=n.parse(i,{range:!0}),s=[];if("Program"!==r.type||1!==r.body.length||"ExpressionStatement"!==r.body[0].type||"ArrowFunctionExpression"!==r.body[0].expression.type&&"FunctionExpression"!==r.body[0].expression.type)throw Error("Failed to resolve function");return(r.body[0].expression.params.forEach(function(e){s.push(e.name)}),t=r.body[0].expression.body.range,"BlockStatement"===r.body[0].expression.body.type)?Function(s,i.slice(t[0]+1,t[1]-1)):Function(s,"return "+i.slice(t[0],t[1]))}function a(e){return e.toString()}function o(e){return"[object Function]"===Object.prototype.toString.call(e)}e.exports=new(i(3016))("tag:yaml.org,2002:js/function",{kind:"scalar",resolve:r,construct:s,predicate:o,represent:a})},1735:(e,t,i)=>{"use strict";var n=i(3016),r=RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),s=RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");e.exports=new n("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:function(e){return null!==e&&(null!==r.exec(e)||null!==s.exec(e))},construct:function(e){var t,i,n,a,o,u,c,h,l=0,p=null;if(null===(t=r.exec(e))&&(t=s.exec(e)),null===t)throw Error("Date resolve error");if(i=+t[1],n=t[2]-1,a=+t[3],!t[4])return new Date(Date.UTC(i,n,a));if(o=+t[4],u=+t[5],c=+t[6],t[7]){for(l=t[7].slice(0,3);l.length<3;)l+="0";l*=1}return t[9]&&(p=(60*t[10]+ +(t[11]||0))*6e4,"-"===t[9]&&(p=-p)),h=new Date(Date.UTC(i,n,a,o,u,c,l)),p&&h.setTime(h.getTime()-p),h},instanceOf:Date,represent:function(e){return e.toISOString()}})},1748:(e,t,i)=>{"use strict";e.exports=i(7577)},1857:(e,t,i)=>{"use strict";var n=i(2533),r=i(6711),s=i(9813),a=i(6165),o=Object.prototype.toString,u=Object.prototype.hasOwnProperty,c=9,h=10,l=13,p=32,d=33,f=34,m=35,x=37,D=38,y=39,g=42,E=44,C=45,A=58,v=61,S=62,F=63,k=64,w=91,b=93,B=96,T=123,I=124,N=125,M={};M[0]="\\0",M[7]="\\a",M[8]="\\b",M[9]="\\t",M[10]="\\n",M[11]="\\v",M[12]="\\f",M[13]="\\r",M[27]="\\e",M[34]='\\"',M[92]="\\\\",M[133]="\\N",M[160]="\\_",M[8232]="\\L",M[8233]="\\P";var P=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"];function L(e,t){var i,n,r,s,a,o,c;if(null===t)return{};for(r=0,i={},s=(n=Object.keys(t)).length;r<s;r+=1)o=String(t[a=n[r]]),"!!"===a.slice(0,2)&&(a="tag:yaml.org,2002:"+a.slice(2)),(c=e.compiledTypeMap.fallback[a])&&u.call(c.styleAliases,o)&&(o=c.styleAliases[o]),i[a]=o;return i}function O(e){var t,i,s;if(t=e.toString(16).toUpperCase(),e<=255)i="x",s=2;else if(e<=65535)i="u",s=4;else if(e<=0xffffffff)i="U",s=8;else throw new r("code point within a string may not be greater than 0xFFFFFFFF");return"\\"+i+n.repeat("0",s-t.length)+t}function X(e){this.schema=e.schema||s,this.indent=Math.max(1,e.indent||2),this.noArrayIndent=e.noArrayIndent||!1,this.skipInvalid=e.skipInvalid||!1,this.flowLevel=n.isNothing(e.flowLevel)?-1:e.flowLevel,this.styleMap=L(this.schema,e.styles||null),this.sortKeys=e.sortKeys||!1,this.lineWidth=e.lineWidth||80,this.noRefs=e.noRefs||!1,this.noCompatMode=e.noCompatMode||!1,this.condenseFlow=e.condenseFlow||!1,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}function J(e,t){for(var i,r=n.repeat(" ",t),s=0,a=-1,o="",u=e.length;s<u;)-1===(a=e.indexOf("\n",s))?(i=e.slice(s),s=u):(i=e.slice(s,a+1),s=a+1),i.length&&"\n"!==i&&(o+=r),o+=i;return o}function U(e,t){return"\n"+n.repeat(" ",e.indent*t)}function j(e,t){var i,n;for(i=0,n=e.implicitTypes.length;i<n;i+=1)if(e.implicitTypes[i].resolve(t))return!0;return!1}function z(e){return e===p||e===c}function R(e){return 32<=e&&e<=126||161<=e&&e<=55295&&8232!==e&&8233!==e||57344<=e&&e<=65533&&65279!==e||65536<=e&&e<=1114111}function K(e){return R(e)&&!z(e)&&65279!==e&&e!==l&&e!==h}function _(e,t){return R(e)&&65279!==e&&e!==E&&e!==w&&e!==b&&e!==T&&e!==N&&e!==A&&(e!==m||t&&K(t))}function H(e){return R(e)&&65279!==e&&!z(e)&&e!==C&&e!==F&&e!==A&&e!==E&&e!==w&&e!==b&&e!==T&&e!==N&&e!==m&&e!==D&&e!==g&&e!==d&&e!==I&&e!==v&&e!==S&&e!==y&&e!==f&&e!==x&&e!==k&&e!==B}function W(e){return/^\n* /.test(e)}var G=1,Y=2,V=3,q=4,$=5;function Z(e,t,i,n,r){var s,a,o,u=!1,c=!1,l=-1!==n,p=-1,d=H(e.charCodeAt(0))&&!z(e.charCodeAt(e.length-1));if(t)for(s=0;s<e.length;s++){if(!R(a=e.charCodeAt(s)))return $;o=s>0?e.charCodeAt(s-1):null,d=d&&_(a,o)}else{for(s=0;s<e.length;s++){if((a=e.charCodeAt(s))===h)u=!0,l&&(c=c||s-p-1>n&&" "!==e[p+1],p=s);else if(!R(a))return $;o=s>0?e.charCodeAt(s-1):null,d=d&&_(a,o)}c=c||l&&s-p-1>n&&" "!==e[p+1]}return u||c?i>9&&W(e)?$:c?q:V:d&&!r(e)?G:Y}function Q(e,t,i,n){e.dump=function(){if(0===t.length)return"''";if(!e.noCompatMode&&-1!==P.indexOf(t))return"'"+t+"'";var s=e.indent*Math.max(1,i),a=-1===e.lineWidth?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-s);function o(t){return j(e,t)}switch(Z(t,n||e.flowLevel>-1&&i>=e.flowLevel,e.indent,a,o)){case G:return t;case Y:return"'"+t.replace(/'/g,"''")+"'";case V:return"|"+ee(t,e.indent)+et(J(t,s));case q:return">"+ee(t,e.indent)+et(J(ei(t,a),s));case $:return'"'+er(t,a)+'"';default:throw new r("impossible error: invalid scalar style")}}()}function ee(e,t){var i=W(e)?String(t):"",n="\n"===e[e.length-1];return i+(n&&("\n"===e[e.length-2]||"\n"===e)?"+":n?"":"-")+"\n"}function et(e){return"\n"===e[e.length-1]?e.slice(0,-1):e}function ei(e,t){for(var i,n,r=/(\n+)([^\n]*)/g,s=function(){var i=e.indexOf("\n");return r.lastIndex=i=-1!==i?i:e.length,en(e.slice(0,i),t)}(),a="\n"===e[0]||" "===e[0];n=r.exec(e);){var o=n[1],u=n[2];i=" "===u[0],s+=o+(a||i||""===u?"":"\n")+en(u,t),a=i}return s}function en(e,t){if(""===e||" "===e[0])return e;for(var i,n,r=/ [^ ]/g,s=0,a=0,o=0,u="";i=r.exec(e);)(o=i.index)-s>t&&(n=a>s?a:o,u+="\n"+e.slice(s,n),s=n+1),a=o;return u+="\n",e.length-s>t&&a>s?u+=e.slice(s,a)+"\n"+e.slice(a+1):u+=e.slice(s),u.slice(1)}function er(e){for(var t,i,n,r="",s=0;s<e.length;s++){if((t=e.charCodeAt(s))>=55296&&t<=56319&&(i=e.charCodeAt(s+1))>=56320&&i<=57343){r+=O((t-55296)*1024+i-56320+65536),s++;continue}r+=!(n=M[t])&&R(t)?e[s]:n||O(t)}return r}function es(e,t,i){var n,r,s="",a=e.tag;for(n=0,r=i.length;n<r;n+=1)eh(e,t,i[n],!1,!1)&&(0!==n&&(s+=","+(e.condenseFlow?"":" ")),s+=e.dump);e.tag=a,e.dump="["+s+"]"}function ea(e,t,i,n){var r,s,a="",o=e.tag;for(r=0,s=i.length;r<s;r+=1)eh(e,t+1,i[r],!0,!0)&&(n&&0===r||(a+=U(e,t)),e.dump&&h===e.dump.charCodeAt(0)?a+="-":a+="- ",a+=e.dump);e.tag=o,e.dump=a||"[]"}function eo(e,t,i){var n,r,s,a,o,u="",c=e.tag,h=Object.keys(i);for(n=0,r=h.length;n<r;n+=1)o="",0!==n&&(o+=", "),e.condenseFlow&&(o+='"'),a=i[s=h[n]],eh(e,t,s,!1,!1)&&(e.dump.length>1024&&(o+="? "),o+=e.dump+(e.condenseFlow?'"':"")+":"+(e.condenseFlow?"":" "),eh(e,t,a,!1,!1)&&(o+=e.dump,u+=o));e.tag=c,e.dump="{"+u+"}"}function eu(e,t,i,n){var s,a,o,u,c,l,p="",d=e.tag,f=Object.keys(i);if(!0===e.sortKeys)f.sort();else if("function"==typeof e.sortKeys)f.sort(e.sortKeys);else if(e.sortKeys)throw new r("sortKeys must be a boolean or a function");for(s=0,a=f.length;s<a;s+=1)l="",n&&0===s||(l+=U(e,t)),u=i[o=f[s]],eh(e,t+1,o,!0,!0,!0)&&((c=null!==e.tag&&"?"!==e.tag||e.dump&&e.dump.length>1024)&&(e.dump&&h===e.dump.charCodeAt(0)?l+="?":l+="? "),l+=e.dump,c&&(l+=U(e,t)),eh(e,t+1,u,!0,c)&&(e.dump&&h===e.dump.charCodeAt(0)?l+=":":l+=": ",l+=e.dump,p+=l));e.tag=d,e.dump=p||"{}"}function ec(e,t,i){var n,s,a,c,h,l;for(a=0,c=(s=i?e.explicitTypes:e.implicitTypes).length;a<c;a+=1)if(((h=s[a]).instanceOf||h.predicate)&&(!h.instanceOf||"object"==typeof t&&t instanceof h.instanceOf)&&(!h.predicate||h.predicate(t))){if(e.tag=i?h.tag:"?",h.represent){if(l=e.styleMap[h.tag]||h.defaultStyle,"[object Function]"===o.call(h.represent))n=h.represent(t,l);else if(u.call(h.represent,l))n=h.represent[l](t,l);else throw new r("!<"+h.tag+'> tag resolver accepts not "'+l+'" style');e.dump=n}return!0}return!1}function eh(e,t,i,n,s,a){e.tag=null,e.dump=i,ec(e,i,!1)||ec(e,i,!0);var u=o.call(e.dump);n&&(n=e.flowLevel<0||e.flowLevel>t);var c,h,l="[object Object]"===u||"[object Array]"===u;if(l&&(h=-1!==(c=e.duplicates.indexOf(i))),(null!==e.tag&&"?"!==e.tag||h||2!==e.indent&&t>0)&&(s=!1),h&&e.usedDuplicates[c])e.dump="*ref_"+c;else{if(l&&h&&!e.usedDuplicates[c]&&(e.usedDuplicates[c]=!0),"[object Object]"===u)n&&0!==Object.keys(e.dump).length?(eu(e,t,e.dump,s),h&&(e.dump="&ref_"+c+e.dump)):(eo(e,t,e.dump),h&&(e.dump="&ref_"+c+" "+e.dump));else if("[object Array]"===u){var p=e.noArrayIndent&&t>0?t-1:t;n&&0!==e.dump.length?(ea(e,p,e.dump,s),h&&(e.dump="&ref_"+c+e.dump)):(es(e,p,e.dump),h&&(e.dump="&ref_"+c+" "+e.dump))}else if("[object String]"===u)"?"!==e.tag&&Q(e,e.dump,t,a);else{if(e.skipInvalid)return!1;throw new r("unacceptable kind of an object to dump "+u)}null!==e.tag&&"?"!==e.tag&&(e.dump="!<"+e.tag+"> "+e.dump)}return!0}function el(e,t){var i,n,r=[],s=[];for(ep(e,r,s),i=0,n=s.length;i<n;i+=1)t.duplicates.push(r[s[i]]);t.usedDuplicates=Array(n)}function ep(e,t,i){var n,r,s;if(null!==e&&"object"==typeof e)if(-1!==(r=t.indexOf(e)))-1===i.indexOf(r)&&i.push(r);else if(t.push(e),Array.isArray(e))for(r=0,s=e.length;r<s;r+=1)ep(e[r],t,i);else for(r=0,s=(n=Object.keys(e)).length;r<s;r+=1)ep(e[n[r]],t,i)}function ed(e,t){var i=new X(t=t||{});return(i.noRefs||el(e,i),eh(i,0,e,!0,!0))?i.dump+"\n":""}function ef(e,t){return ed(e,n.extend({schema:a},t))}e.exports.dump=ed,e.exports.safeDump=ef},1929:e=>{"use strict";function t(e){switch(e.toLowerCase()){case"js":case"javascript":return"javascript";case"coffee":case"coffeescript":case"cson":return"coffee";case"yaml":case"yml":return"yaml";default:return e}}e.exports=function(e,i){let n=i.engines[e]||i.engines[t(e)];if(void 0===n)throw Error('gray-matter engine "'+e+'" is not registered');return"function"==typeof n&&(n={parse:n}),n}},2533:e=>{"use strict";function t(e){return null==e}function i(e){return"object"==typeof e&&null!==e}function n(e){return Array.isArray(e)?e:t(e)?[]:[e]}function r(e,t){var i,n,r,s;if(t)for(i=0,n=(s=Object.keys(t)).length;i<n;i+=1)e[r=s[i]]=t[r];return e}function s(e,t){var i,n="";for(i=0;i<t;i+=1)n+=e;return n}function a(e){return 0===e&&Number.NEGATIVE_INFINITY===1/e}e.exports.isNothing=t,e.exports.isObject=i,e.exports.toArray=n,e.exports.repeat=s,e.exports.isNegativeZero=a,e.exports.extend=r},2631:(e,t,i)=>{"use strict";function n(e){if(null===e)return!1;var t=e.length;return 4===t&&("true"===e||"True"===e||"TRUE"===e)||5===t&&("false"===e||"False"===e||"FALSE"===e)}function r(e){return"true"===e||"True"===e||"TRUE"===e}function s(e){return"[object Boolean]"===Object.prototype.toString.call(e)}e.exports=new(i(3016))("tag:yaml.org,2002:bool",{kind:"scalar",resolve:n,construct:r,predicate:s,represent:{lowercase:function(e){return e?"true":"false"},uppercase:function(e){return e?"TRUE":"FALSE"},camelcase:function(e){return e?"True":"False"}},defaultStyle:"lowercase"})},2833:(e,t,i)=>{"use strict";var n=i(2533),r=i(3016),s=RegExp("^(?:[-+]?(?:0|[1-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\\.[0-9_]*|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$"),a=/^[-+]?[0-9]+e/;e.exports=new r("tag:yaml.org,2002:float",{kind:"scalar",resolve:function(e){return null!==e&&!!s.test(e)&&"_"!==e[e.length-1]},construct:function(e){var t,i,n,r;return(i="-"===(t=e.replace(/_/g,"").toLowerCase())[0]?-1:1,r=[],"+-".indexOf(t[0])>=0&&(t=t.slice(1)),".inf"===t)?1===i?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:".nan"===t?NaN:t.indexOf(":")>=0?(t.split(":").forEach(function(e){r.unshift(parseFloat(e,10))}),t=0,n=1,r.forEach(function(e){t+=e*n,n*=60}),i*t):i*parseFloat(t,10)},predicate:function(e){return"[object Number]"===Object.prototype.toString.call(e)&&(e%1!=0||n.isNegativeZero(e))},represent:function(e,t){var i;if(isNaN(e))switch(t){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===e)switch(t){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===e)switch(t){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(n.isNegativeZero(e))return"-0.0";return i=e.toString(10),a.test(i)?i.replace("e",".e"):i},defaultStyle:"lowercase"})},3016:(e,t,i)=>{"use strict";var n=i(6711),r=["kind","resolve","construct","instanceOf","predicate","represent","defaultStyle","styleAliases"],s=["scalar","sequence","mapping"];function a(e){var t={};return null!==e&&Object.keys(e).forEach(function(i){e[i].forEach(function(e){t[String(e)]=i})}),t}e.exports=function(e,t){if(Object.keys(t=t||{}).forEach(function(t){if(-1===r.indexOf(t))throw new n('Unknown option "'+t+'" is met in definition of "'+e+'" YAML type.')}),this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){return!0},this.construct=t.construct||function(e){return e},this.instanceOf=t.instanceOf||null,this.predicate=t.predicate||null,this.represent=t.represent||null,this.defaultStyle=t.defaultStyle||null,this.styleAliases=a(t.styleAliases||null),-1===s.indexOf(this.kind))throw new n('Unknown kind "'+this.kind+'" is specified for "'+e+'" YAML type.')}},3052:e=>{var t=Object.prototype.toString;function i(e){return"function"==typeof e.constructor?e.constructor.name:null}function n(e){return Array.isArray?Array.isArray(e):e instanceof Array}function r(e){return e instanceof Error||"string"==typeof e.message&&e.constructor&&"number"==typeof e.constructor.stackTraceLimit}function s(e){return e instanceof Date||"function"==typeof e.toDateString&&"function"==typeof e.getDate&&"function"==typeof e.setDate}function a(e){return e instanceof RegExp||"string"==typeof e.flags&&"boolean"==typeof e.ignoreCase&&"boolean"==typeof e.multiline&&"boolean"==typeof e.global}function o(e,t){return"GeneratorFunction"===i(e)}function u(e){return"function"==typeof e.throw&&"function"==typeof e.return&&"function"==typeof e.next}function c(e){try{if("number"==typeof e.length&&"function"==typeof e.callee)return!0}catch(e){if(-1!==e.message.indexOf("callee"))return!0}return!1}function h(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}e.exports=function(e){if(void 0===e)return"undefined";if(null===e)return"null";var l=typeof e;if("boolean"===l)return"boolean";if("string"===l)return"string";if("number"===l)return"number";if("symbol"===l)return"symbol";if("function"===l)return o(e)?"generatorfunction":"function";if(n(e))return"array";if(h(e))return"buffer";if(c(e))return"arguments";if(s(e))return"date";if(r(e))return"error";if(a(e))return"regexp";switch(i(e)){case"Symbol":return"symbol";case"Promise":return"promise";case"WeakMap":return"weakmap";case"WeakSet":return"weakset";case"Map":return"map";case"Set":return"set";case"Int8Array":return"int8array";case"Uint8Array":return"uint8array";case"Uint8ClampedArray":return"uint8clampedarray";case"Int16Array":return"int16array";case"Uint16Array":return"uint16array";case"Int32Array":return"int32array";case"Uint32Array":return"uint32array";case"Float32Array":return"float32array";case"Float64Array":return"float64array"}if(u(e))return"generator";switch(l=t.call(e)){case"[object Object]":return"object";case"[object Map Iterator]":return"mapiterator";case"[object Set Iterator]":return"setiterator";case"[object String Iterator]":return"stringiterator";case"[object Array Iterator]":return"arrayiterator"}return l.slice(8,-1).toLowerCase().replace(/\s/g,"")}},3128:(e,t,i)=>{"use strict";e.exports=new(i(3016))("tag:yaml.org,2002:seq",{kind:"sequence",construct:function(e){return null!==e?e:[]}})},3233:(e,t,i)=>{"use strict";var n=i(927);function r(e,t){for(var i in t)s(t,i)&&(e[i]=t[i])}function s(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.exports=function(e){n(e)||(e={});for(var t=arguments.length,i=1;i<t;i++){var s=arguments[i];n(s)&&r(e,s)}return e}},3270:(e,t,i)=>{"use strict";try{var n;n=i(9428).Buffer}catch(e){}var r=i(3016),s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\n\r";e.exports=new r("tag:yaml.org,2002:binary",{kind:"scalar",resolve:function(e){if(null===e)return!1;var t,i,n=0,r=e.length,a=s;for(i=0;i<r;i++)if(!((t=a.indexOf(e.charAt(i)))>64)){if(t<0)return!1;n+=6}return n%8==0},construct:function(e){var t,i,r=e.replace(/[\r\n=]/g,""),a=r.length,o=s,u=0,c=[];for(t=0;t<a;t++)t%4==0&&t&&(c.push(u>>16&255),c.push(u>>8&255),c.push(255&u)),u=u<<6|o.indexOf(r.charAt(t));return(0==(i=a%4*6)?(c.push(u>>16&255),c.push(u>>8&255),c.push(255&u)):18===i?(c.push(u>>10&255),c.push(u>>2&255)):12===i&&c.push(u>>4&255),n)?n.from?n.from(c):new n(c):c},predicate:function(e){return n&&n.isBuffer(e)},represent:function(e){var t,i,n="",r=0,a=e.length,o=s;for(t=0;t<a;t++)t%3==0&&t&&(n+=o[r>>18&63],n+=o[r>>12&63],n+=o[r>>6&63],n+=o[63&r]),r=(r<<8)+e[t];return 0==(i=a%3)?(n+=o[r>>18&63],n+=o[r>>12&63],n+=o[r>>6&63],n+=o[63&r]):2===i?(n+=o[r>>10&63],n+=o[r>>4&63],n+=o[r<<2&63],n+=o[64]):1===i&&(n+=o[r>>2&63],n+=o[r<<4&63],n+=o[64],n+=o[64]),n}})},3785:(e,t,i)=>{"use strict";var n=i(2533);function r(e,t,i,n,r){this.name=e,this.buffer=t,this.position=i,this.line=n,this.column=r}r.prototype.getSnippet=function(e,t){var i,r,s,a,o;if(!this.buffer)return null;for(e=e||4,t=t||75,i="",r=this.position;r>0&&-1==="\0\r\n\x85\u2028\u2029".indexOf(this.buffer.charAt(r-1));)if(r-=1,this.position-r>t/2-1){i=" ... ",r+=5;break}for(s="",a=this.position;a<this.buffer.length&&-1==="\0\r\n\x85\u2028\u2029".indexOf(this.buffer.charAt(a));)if((a+=1)-this.position>t/2-1){s=" ... ",a-=5;break}return o=this.buffer.slice(r,a),n.repeat(" ",e)+i+o+s+"\n"+n.repeat(" ",e+this.position-r+i.length)+"^"},r.prototype.toString=function(e){var t,i="";return this.name&&(i+='in "'+this.name+'" '),i+="at line "+(this.line+1)+", column "+(this.column+1),!e&&(t=this.getSnippet())&&(i+=":\n"+t),i},e.exports=r},3944:(e,t,i)=>{"use strict";var n=i(3052),r=i(3233);function s(e,t){return e.slice(0,t.length)===t&&e.charAt(t.length+1)!==t.slice(-1)}function a(e){if("object"!==n(e)&&(e={content:e}),"string"!=typeof e.content&&!h(e.content))throw TypeError("expected a buffer or string");return e.content=e.content.toString(),e.sections=[],e}function o(e,t){return e?e.slice(t.length).trim():""}function u(){return{key:"",data:"",content:""}}function c(e){return e}function h(e){return!!e&&!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}e.exports=function(e,t){"function"==typeof t&&(t={parse:t});var i=a(e),n=r({},{section_delimiter:"---",parse:c},t),h=n.section_delimiter,l=i.content.split(/\r?\n/),p=null,d=u(),f=[],m=[];function x(e){i.content=e,p=[],f=[]}function D(e){m.length&&(d.key=o(m[0],h),d.content=e,n.parse(d,p),p.push(d),d=u(),f=[],m=[])}for(var y=0;y<l.length;y++){var g=l[y],E=m.length,C=g.trim();if(s(C,h)){if(3===C.length&&0!==y){if(0===E||2===E){f.push(g);continue}m.push(C),d.data=f.join("\n"),f=[];continue}null===p&&x(f.join("\n")),2===E&&D(f.join("\n")),m.push(C);continue}f.push(g)}return null===p?x(f.join("\n")):D(f.join("\n")),i.sections=p,i}},4246:(e,t,i)=>{"use strict";function n(e){if(null===e||0===e.length)return!1;var t=e,i=/\/([gim]*)$/.exec(e),n="";return("/"!==t[0]||(i&&(n=i[1]),!(n.length>3)&&"/"===t[t.length-n.length-1]))&&!0}function r(e){var t=e,i=/\/([gim]*)$/.exec(e),n="";return"/"===t[0]&&(i&&(n=i[1]),t=t.slice(1,t.length-n.length-1)),new RegExp(t,n)}function s(e){var t="/"+e.source+"/";return e.global&&(t+="g"),e.multiline&&(t+="m"),e.ignoreCase&&(t+="i"),t}function a(e){return"[object RegExp]"===Object.prototype.toString.call(e)}e.exports=new(i(3016))("tag:yaml.org,2002:js/regexp",{kind:"scalar",resolve:n,construct:r,predicate:a,represent:s})},4951:(e,t,i)=>{"use strict";var n=i(3016),r=Object.prototype.hasOwnProperty;e.exports=new n("tag:yaml.org,2002:set",{kind:"mapping",resolve:function(e){if(null===e)return!0;var t,i=e;for(t in i)if(r.call(i,t)&&null!==i[t])return!1;return!0},construct:function(e){return null!==e?e:{}}})},5019:(e,t,i)=>{"use strict";var n=i(2533),r=i(6711),s=i(3785),a=i(6165),o=i(9813),u=Object.prototype.hasOwnProperty,c=1,h=2,l=3,p=4,d=1,f=2,m=3,x=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,D=/[\x85\u2028\u2029]/,y=/[,\[\]\{\}]/,g=/^(?:!|!!|![a-z\-]+!)$/i,E=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function C(e){return Object.prototype.toString.call(e)}function A(e){return 10===e||13===e}function v(e){return 9===e||32===e}function S(e){return 9===e||32===e||10===e||13===e}function F(e){return 44===e||91===e||93===e||123===e||125===e}function k(e){var t;return 48<=e&&e<=57?e-48:97<=(t=32|e)&&t<=102?t-97+10:-1}function w(e){return 120===e?2:117===e?4:8*(85===e)}function b(e){return 48<=e&&e<=57?e-48:-1}function B(e){return 48===e?"\0":97===e?"\x07":98===e?"\b":116===e||9===e?"	":110===e?"\n":118===e?"\v":102===e?"\f":114===e?"\r":101===e?"\x1b":32===e?" ":34===e?'"':47===e?"/":92===e?"\\":78===e?"\x85":95===e?"\xa0":76===e?"\u2028":80===e?"\u2029":""}function T(e){return e<=65535?String.fromCharCode(e):String.fromCharCode((e-65536>>10)+55296,(e-65536&1023)+56320)}for(var I=Array(256),N=Array(256),M=0;M<256;M++)I[M]=+!!B(M),N[M]=B(M);function P(e,t){this.input=e,this.filename=t.filename||null,this.schema=t.schema||o,this.onWarning=t.onWarning||null,this.legacy=t.legacy||!1,this.json=t.json||!1,this.listener=t.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=e.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.documents=[]}function L(e,t){return new r(t,new s(e.filename,e.input,e.position,e.line,e.position-e.lineStart))}function O(e,t){throw L(e,t)}function X(e,t){e.onWarning&&e.onWarning.call(null,L(e,t))}var J={YAML:function(e,t,i){var n,r,s;null!==e.version&&O(e,"duplication of %YAML directive"),1!==i.length&&O(e,"YAML directive accepts exactly one argument"),null===(n=/^([0-9]+)\.([0-9]+)$/.exec(i[0]))&&O(e,"ill-formed argument of the YAML directive"),r=parseInt(n[1],10),s=parseInt(n[2],10),1!==r&&O(e,"unacceptable YAML version of the document"),e.version=i[0],e.checkLineBreaks=s<2,1!==s&&2!==s&&X(e,"unsupported YAML version of the document")},TAG:function(e,t,i){var n,r;2!==i.length&&O(e,"TAG directive accepts exactly two arguments"),n=i[0],r=i[1],g.test(n)||O(e,"ill-formed tag handle (first argument) of the TAG directive"),u.call(e.tagMap,n)&&O(e,'there is a previously declared suffix for "'+n+'" tag handle'),E.test(r)||O(e,"ill-formed tag prefix (second argument) of the TAG directive"),e.tagMap[n]=r}};function U(e,t,i,n){var r,s,a,o;if(t<i){if(o=e.input.slice(t,i),n)for(r=0,s=o.length;r<s;r+=1)9===(a=o.charCodeAt(r))||32<=a&&a<=1114111||O(e,"expected valid JSON character");else x.test(o)&&O(e,"the stream contains non-printable characters");e.result+=o}}function j(e,t,i,r){var s,a,o,c;for(n.isObject(i)||O(e,"cannot merge mappings; the provided source object is unacceptable"),o=0,c=(s=Object.keys(i)).length;o<c;o+=1)a=s[o],u.call(t,a)||(t[a]=i[a],r[a]=!0)}function z(e,t,i,n,r,s,a,o){var c,h;if(Array.isArray(r))for(c=0,h=(r=Array.prototype.slice.call(r)).length;c<h;c+=1)Array.isArray(r[c])&&O(e,"nested arrays are not supported inside keys"),"object"==typeof r&&"[object Object]"===C(r[c])&&(r[c]="[object Object]");if("object"==typeof r&&"[object Object]"===C(r)&&(r="[object Object]"),r=String(r),null===t&&(t={}),"tag:yaml.org,2002:merge"===n)if(Array.isArray(s))for(c=0,h=s.length;c<h;c+=1)j(e,t,s[c],i);else j(e,t,s,i);else!e.json&&!u.call(i,r)&&u.call(t,r)&&(e.line=a||e.line,e.position=o||e.position,O(e,"duplicated mapping key")),t[r]=s,delete i[r];return t}function R(e){var t;10===(t=e.input.charCodeAt(e.position))?e.position++:13===t?(e.position++,10===e.input.charCodeAt(e.position)&&e.position++):O(e,"a line break is expected"),e.line+=1,e.lineStart=e.position}function K(e,t,i){for(var n=0,r=e.input.charCodeAt(e.position);0!==r;){for(;v(r);)r=e.input.charCodeAt(++e.position);if(t&&35===r)do r=e.input.charCodeAt(++e.position);while(10!==r&&13!==r&&0!==r);if(A(r))for(R(e),r=e.input.charCodeAt(e.position),n++,e.lineIndent=0;32===r;)e.lineIndent++,r=e.input.charCodeAt(++e.position);else break}return -1!==i&&0!==n&&e.lineIndent<i&&X(e,"deficient indentation"),n}function _(e){var t,i=e.position;return!!((45===(t=e.input.charCodeAt(i))||46===t)&&t===e.input.charCodeAt(i+1)&&t===e.input.charCodeAt(i+2)&&(i+=3,0===(t=e.input.charCodeAt(i))||S(t)))||!1}function H(e,t){1===t?e.result+=" ":t>1&&(e.result+=n.repeat("\n",t-1))}function W(e,t,i){var n,r,s,a,o,u,c,h,l=e.kind,p=e.result;if(S(h=e.input.charCodeAt(e.position))||F(h)||35===h||38===h||42===h||33===h||124===h||62===h||39===h||34===h||37===h||64===h||96===h||(63===h||45===h)&&(S(n=e.input.charCodeAt(e.position+1))||i&&F(n)))return!1;for(e.kind="scalar",e.result="",r=s=e.position,a=!1;0!==h;){if(58===h){if(S(n=e.input.charCodeAt(e.position+1))||i&&F(n))break}else if(35===h){if(S(e.input.charCodeAt(e.position-1)))break}else if(e.position===e.lineStart&&_(e)||i&&F(h))break;else if(A(h)){if(o=e.line,u=e.lineStart,c=e.lineIndent,K(e,!1,-1),e.lineIndent>=t){a=!0,h=e.input.charCodeAt(e.position);continue}e.position=s,e.line=o,e.lineStart=u,e.lineIndent=c;break}a&&(U(e,r,s,!1),H(e,e.line-o),r=s=e.position,a=!1),v(h)||(s=e.position+1),h=e.input.charCodeAt(++e.position)}return U(e,r,s,!1),!!e.result||(e.kind=l,e.result=p,!1)}function G(e,t){var i,n,r;if(39!==(i=e.input.charCodeAt(e.position)))return!1;for(e.kind="scalar",e.result="",e.position++,n=r=e.position;0!==(i=e.input.charCodeAt(e.position));)if(39===i){if(U(e,n,e.position,!0),39!==(i=e.input.charCodeAt(++e.position)))return!0;n=e.position,e.position++,r=e.position}else A(i)?(U(e,n,r,!0),H(e,K(e,!1,t)),n=r=e.position):e.position===e.lineStart&&_(e)?O(e,"unexpected end of the document within a single quoted scalar"):(e.position++,r=e.position);O(e,"unexpected end of the stream within a single quoted scalar")}function Y(e,t){var i,n,r,s,a,o;if(34!==(o=e.input.charCodeAt(e.position)))return!1;for(e.kind="scalar",e.result="",e.position++,i=n=e.position;0!==(o=e.input.charCodeAt(e.position));)if(34===o)return U(e,i,e.position,!0),e.position++,!0;else if(92===o){if(U(e,i,e.position,!0),A(o=e.input.charCodeAt(++e.position)))K(e,!1,t);else if(o<256&&I[o])e.result+=N[o],e.position++;else if((a=w(o))>0){for(r=a,s=0;r>0;r--)(a=k(o=e.input.charCodeAt(++e.position)))>=0?s=(s<<4)+a:O(e,"expected hexadecimal character");e.result+=T(s),e.position++}else O(e,"unknown escape sequence");i=n=e.position}else A(o)?(U(e,i,n,!0),H(e,K(e,!1,t)),i=n=e.position):e.position===e.lineStart&&_(e)?O(e,"unexpected end of the document within a double quoted scalar"):(e.position++,n=e.position);O(e,"unexpected end of the stream within a double quoted scalar")}function V(e,t){var i,n,r,s,a,o,u,h,l,p,d=!0,f=e.tag,m=e.anchor,x={};if(91===(p=e.input.charCodeAt(e.position)))r=93,o=!1,n=[];else{if(123!==p)return!1;r=125,o=!0,n={}}for(null!==e.anchor&&(e.anchorMap[e.anchor]=n),p=e.input.charCodeAt(++e.position);0!==p;){if(K(e,!0,t),(p=e.input.charCodeAt(e.position))===r)return e.position++,e.tag=f,e.anchor=m,e.kind=o?"mapping":"sequence",e.result=n,!0;d||O(e,"missed comma between flow collection entries"),h=u=l=null,s=a=!1,63===p&&S(e.input.charCodeAt(e.position+1))&&(s=a=!0,e.position++,K(e,!0,t)),i=e.line,ei(e,t,c,!1,!0),h=e.tag,u=e.result,K(e,!0,t),p=e.input.charCodeAt(e.position),(a||e.line===i)&&58===p&&(s=!0,p=e.input.charCodeAt(++e.position),K(e,!0,t),ei(e,t,c,!1,!0),l=e.result),o?z(e,n,x,h,u,l):s?n.push(z(e,null,x,h,u,l)):n.push(u),K(e,!0,t),44===(p=e.input.charCodeAt(e.position))?(d=!0,p=e.input.charCodeAt(++e.position)):d=!1}O(e,"unexpected end of the stream within a flow collection")}function q(e,t){var i,r,s,a,o=d,u=!1,c=!1,h=t,l=0,p=!1;if(124===(a=e.input.charCodeAt(e.position)))r=!1;else{if(62!==a)return!1;r=!0}for(e.kind="scalar",e.result="";0!==a;)if(43===(a=e.input.charCodeAt(++e.position))||45===a)d===o?o=43===a?m:f:O(e,"repeat of a chomping mode identifier");else if((s=b(a))>=0)0===s?O(e,"bad explicit indentation width of a block scalar; it cannot be less than one"):c?O(e,"repeat of an indentation width identifier"):(h=t+s-1,c=!0);else break;if(v(a)){do a=e.input.charCodeAt(++e.position);while(v(a));if(35===a)do a=e.input.charCodeAt(++e.position);while(!A(a)&&0!==a)}for(;0!==a;){for(R(e),e.lineIndent=0,a=e.input.charCodeAt(e.position);(!c||e.lineIndent<h)&&32===a;)e.lineIndent++,a=e.input.charCodeAt(++e.position);if(!c&&e.lineIndent>h&&(h=e.lineIndent),A(a)){l++;continue}if(e.lineIndent<h){o===m?e.result+=n.repeat("\n",u?1+l:l):o===d&&u&&(e.result+="\n");break}for(r?v(a)?(p=!0,e.result+=n.repeat("\n",u?1+l:l)):p?(p=!1,e.result+=n.repeat("\n",l+1)):0===l?u&&(e.result+=" "):e.result+=n.repeat("\n",l):e.result+=n.repeat("\n",u?1+l:l),u=!0,c=!0,l=0,i=e.position;!A(a)&&0!==a;)a=e.input.charCodeAt(++e.position);U(e,i,e.position,!1)}return!0}function $(e,t){var i,n,r=e.tag,s=e.anchor,a=[],o=!1;for(null!==e.anchor&&(e.anchorMap[e.anchor]=a),n=e.input.charCodeAt(e.position);0!==n&&45===n&&S(e.input.charCodeAt(e.position+1));){if(o=!0,e.position++,K(e,!0,-1)&&e.lineIndent<=t){a.push(null),n=e.input.charCodeAt(e.position);continue}if(i=e.line,ei(e,t,l,!1,!0),a.push(e.result),K(e,!0,-1),n=e.input.charCodeAt(e.position),(e.line===i||e.lineIndent>t)&&0!==n)O(e,"bad indentation of a sequence entry");else if(e.lineIndent<t)break}return!!o&&(e.tag=r,e.anchor=s,e.kind="sequence",e.result=a,!0)}function Z(e,t,i){var n,r,s,a,o,u=e.tag,c=e.anchor,l={},d={},f=null,m=null,x=null,D=!1,y=!1;for(null!==e.anchor&&(e.anchorMap[e.anchor]=l),o=e.input.charCodeAt(e.position);0!==o;){if(n=e.input.charCodeAt(e.position+1),s=e.line,a=e.position,(63===o||58===o)&&S(n))63===o?(D&&(z(e,l,d,f,m,null),f=m=x=null),y=!0,D=!0,r=!0):D?(D=!1,r=!0):O(e,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line"),e.position+=1,o=n;else if(ei(e,i,h,!1,!0))if(e.line===s){for(o=e.input.charCodeAt(e.position);v(o);)o=e.input.charCodeAt(++e.position);if(58===o)S(o=e.input.charCodeAt(++e.position))||O(e,"a whitespace character is expected after the key-value separator within a block mapping"),D&&(z(e,l,d,f,m,null),f=m=x=null),y=!0,D=!1,r=!1,f=e.tag,m=e.result;else{if(!y)return e.tag=u,e.anchor=c,!0;O(e,"can not read an implicit mapping pair; a colon is missed")}}else{if(!y)return e.tag=u,e.anchor=c,!0;O(e,"can not read a block mapping entry; a multiline key may not be an implicit key")}else break;if((e.line===s||e.lineIndent>t)&&(ei(e,t,p,!0,r)&&(D?m=e.result:x=e.result),D||(z(e,l,d,f,m,x,s,a),f=m=x=null),K(e,!0,-1),o=e.input.charCodeAt(e.position)),e.lineIndent>t&&0!==o)O(e,"bad indentation of a mapping entry");else if(e.lineIndent<t)break}return D&&z(e,l,d,f,m,null),y&&(e.tag=u,e.anchor=c,e.kind="mapping",e.result=l),y}function Q(e){var t,i,n,r,s=!1,a=!1;if(33!==(r=e.input.charCodeAt(e.position)))return!1;if(null!==e.tag&&O(e,"duplication of a tag property"),60===(r=e.input.charCodeAt(++e.position))?(s=!0,r=e.input.charCodeAt(++e.position)):33===r?(a=!0,i="!!",r=e.input.charCodeAt(++e.position)):i="!",t=e.position,s){do r=e.input.charCodeAt(++e.position);while(0!==r&&62!==r);e.position<e.length?(n=e.input.slice(t,e.position),r=e.input.charCodeAt(++e.position)):O(e,"unexpected end of the stream within a verbatim tag")}else{for(;0!==r&&!S(r);)33===r&&(a?O(e,"tag suffix cannot contain exclamation marks"):(i=e.input.slice(t-1,e.position+1),g.test(i)||O(e,"named tag handle cannot contain such characters"),a=!0,t=e.position+1)),r=e.input.charCodeAt(++e.position);n=e.input.slice(t,e.position),y.test(n)&&O(e,"tag suffix cannot contain flow indicator characters")}return n&&!E.test(n)&&O(e,"tag name cannot contain such characters: "+n),s?e.tag=n:u.call(e.tagMap,i)?e.tag=e.tagMap[i]+n:"!"===i?e.tag="!"+n:"!!"===i?e.tag="tag:yaml.org,2002:"+n:O(e,'undeclared tag handle "'+i+'"'),!0}function ee(e){var t,i;if(38!==(i=e.input.charCodeAt(e.position)))return!1;for(null!==e.anchor&&O(e,"duplication of an anchor property"),i=e.input.charCodeAt(++e.position),t=e.position;0!==i&&!S(i)&&!F(i);)i=e.input.charCodeAt(++e.position);return e.position===t&&O(e,"name of an anchor node must contain at least one character"),e.anchor=e.input.slice(t,e.position),!0}function et(e){var t,i,n;if(42!==(n=e.input.charCodeAt(e.position)))return!1;for(n=e.input.charCodeAt(++e.position),t=e.position;0!==n&&!S(n)&&!F(n);)n=e.input.charCodeAt(++e.position);return e.position===t&&O(e,"name of an alias node must contain at least one character"),i=e.input.slice(t,e.position),u.call(e.anchorMap,i)||O(e,'unidentified alias "'+i+'"'),e.result=e.anchorMap[i],K(e,!0,-1),!0}function ei(e,t,i,n,r){var s,a,o,d,f,m,x,D,y=1,g=!1,E=!1;if(null!==e.listener&&e.listener("open",e),e.tag=null,e.anchor=null,e.kind=null,e.result=null,s=a=o=p===i||l===i,n&&K(e,!0,-1)&&(g=!0,e.lineIndent>t?y=1:e.lineIndent===t?y=0:e.lineIndent<t&&(y=-1)),1===y)for(;Q(e)||ee(e);)K(e,!0,-1)?(g=!0,o=s,e.lineIndent>t?y=1:e.lineIndent===t?y=0:e.lineIndent<t&&(y=-1)):o=!1;if(o&&(o=g||r),(1===y||p===i)&&(x=c===i||h===i?t:t+1,D=e.position-e.lineStart,1===y?o&&($(e,D)||Z(e,D,x))||V(e,x)?E=!0:(a&&q(e,x)||G(e,x)||Y(e,x)?E=!0:et(e)?(E=!0,(null!==e.tag||null!==e.anchor)&&O(e,"alias node should not have any properties")):W(e,x,c===i)&&(E=!0,null===e.tag&&(e.tag="?")),null!==e.anchor&&(e.anchorMap[e.anchor]=e.result)):0===y&&(E=o&&$(e,D))),null!==e.tag&&"!"!==e.tag)if("?"===e.tag){for(null!==e.result&&"scalar"!==e.kind&&O(e,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+e.kind+'"'),d=0,f=e.implicitTypes.length;d<f;d+=1)if((m=e.implicitTypes[d]).resolve(e.result)){e.result=m.construct(e.result),e.tag=m.tag,null!==e.anchor&&(e.anchorMap[e.anchor]=e.result);break}}else u.call(e.typeMap[e.kind||"fallback"],e.tag)?(m=e.typeMap[e.kind||"fallback"][e.tag],null!==e.result&&m.kind!==e.kind&&O(e,"unacceptable node kind for !<"+e.tag+'> tag; it should be "'+m.kind+'", not "'+e.kind+'"'),m.resolve(e.result)?(e.result=m.construct(e.result),null!==e.anchor&&(e.anchorMap[e.anchor]=e.result)):O(e,"cannot resolve a node with !<"+e.tag+"> explicit tag")):O(e,"unknown tag !<"+e.tag+">");return null!==e.listener&&e.listener("close",e),null!==e.tag||null!==e.anchor||E}function en(e){var t,i,n,r,s=e.position,a=!1;for(e.version=null,e.checkLineBreaks=e.legacy,e.tagMap={},e.anchorMap={};0!==(r=e.input.charCodeAt(e.position))&&(K(e,!0,-1),r=e.input.charCodeAt(e.position),!(e.lineIndent>0)&&37===r);){for(a=!0,r=e.input.charCodeAt(++e.position),t=e.position;0!==r&&!S(r);)r=e.input.charCodeAt(++e.position);for(i=e.input.slice(t,e.position),n=[],i.length<1&&O(e,"directive name must not be less than one character in length");0!==r;){for(;v(r);)r=e.input.charCodeAt(++e.position);if(35===r){do r=e.input.charCodeAt(++e.position);while(0!==r&&!A(r));break}if(A(r))break;for(t=e.position;0!==r&&!S(r);)r=e.input.charCodeAt(++e.position);n.push(e.input.slice(t,e.position))}0!==r&&R(e),u.call(J,i)?J[i](e,i,n):X(e,'unknown document directive "'+i+'"')}if(K(e,!0,-1),0===e.lineIndent&&45===e.input.charCodeAt(e.position)&&45===e.input.charCodeAt(e.position+1)&&45===e.input.charCodeAt(e.position+2)?(e.position+=3,K(e,!0,-1)):a&&O(e,"directives end mark is expected"),ei(e,e.lineIndent-1,p,!1,!0),K(e,!0,-1),e.checkLineBreaks&&D.test(e.input.slice(s,e.position))&&X(e,"non-ASCII line breaks are interpreted as content"),e.documents.push(e.result),e.position===e.lineStart&&_(e)){46===e.input.charCodeAt(e.position)&&(e.position+=3,K(e,!0,-1));return}e.position<e.length-1&&O(e,"end of the stream or a document separator is expected")}function er(e,t){e=String(e),t=t||{},0!==e.length&&(10!==e.charCodeAt(e.length-1)&&13!==e.charCodeAt(e.length-1)&&(e+="\n"),65279===e.charCodeAt(0)&&(e=e.slice(1)));var i=new P(e,t),n=e.indexOf("\0");for(-1!==n&&(i.position=n,O(i,"null byte is not allowed in input")),i.input+="\0";32===i.input.charCodeAt(i.position);)i.lineIndent+=1,i.position+=1;for(;i.position<i.length-1;)en(i);return i.documents}function es(e,t,i){null!==t&&"object"==typeof t&&void 0===i&&(i=t,t=null);var n=er(e,i);if("function"!=typeof t)return n;for(var r=0,s=n.length;r<s;r+=1)t(n[r])}function ea(e,t){var i=er(e,t);if(0!==i.length){if(1===i.length)return i[0];throw new r("expected a single document in the stream, but found more")}}function eo(e,t,i){return"object"==typeof t&&null!==t&&void 0===i&&(i=t,t=null),es(e,t,n.extend({schema:a},i))}function eu(e,t){return ea(e,n.extend({schema:a},t))}e.exports.loadAll=es,e.exports.load=ea,e.exports.safeLoadAll=eo,e.exports.safeLoad=eu},5167:(e,t,i)=>{"use strict";var n=i(2533),r=i(6711),s=i(3016);function a(e,t,i){var n=[];return e.include.forEach(function(e){i=a(e,t,i)}),e[t].forEach(function(e){i.forEach(function(t,i){t.tag===e.tag&&t.kind===e.kind&&n.push(i)}),i.push(e)}),i.filter(function(e,t){return -1===n.indexOf(t)})}function o(){var e,t,i={scalar:{},sequence:{},mapping:{},fallback:{}};function n(e){i[e.kind][e.tag]=i.fallback[e.tag]=e}for(e=0,t=arguments.length;e<t;e+=1)arguments[e].forEach(n);return i}function u(e){this.include=e.include||[],this.implicit=e.implicit||[],this.explicit=e.explicit||[],this.implicit.forEach(function(e){if(e.loadKind&&"scalar"!==e.loadKind)throw new r("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.")}),this.compiledImplicit=a(this,"implicit",[]),this.compiledExplicit=a(this,"explicit",[]),this.compiledTypeMap=o(this.compiledImplicit,this.compiledExplicit)}u.DEFAULT=null,u.create=function(){var e,t;switch(arguments.length){case 1:e=u.DEFAULT,t=arguments[0];break;case 2:e=arguments[0],t=arguments[1];break;default:throw new r("Wrong number of arguments for Schema.create function")}if(e=n.toArray(e),t=n.toArray(t),!e.every(function(e){return e instanceof u}))throw new r("Specified list of super schemas (or a single Schema object) contains a non-Schema object.");if(!t.every(function(e){return e instanceof s}))throw new r("Specified list of YAML types (or a single Type object) contains a non-Type object.");return new u({include:e,explicit:t})},e.exports=u},5582:(e,t,i)=>{"use strict";let n=i(1053),r=i(3052);t.define=function(e,t,i){Reflect.defineProperty(e,t,{enumerable:!1,configurable:!0,writable:!0,value:i})},t.isBuffer=function(e){return"buffer"===r(e)},t.isObject=function(e){return"object"===r(e)},t.toBuffer=function(e){return"string"==typeof e?Buffer.from(e):e},t.toString=function(e){if(t.isBuffer(e))return n(String(e));if("string"!=typeof e)throw TypeError("expected input to be a string or buffer");return n(e)},t.arrayify=function(e){return e?Array.isArray(e)?e:[e]:[]},t.startsWith=function(e,t,i){return"number"!=typeof i&&(i=t.length),e.slice(0,i)===t}},5762:(e,t,i)=>{"use strict";var n=i(2533);function r(e){return 48<=e&&e<=57||65<=e&&e<=70||97<=e&&e<=102}function s(e){return 48<=e&&e<=55}function a(e){return 48<=e&&e<=57}function o(e){if(null===e)return!1;var t,i=e.length,n=0,o=!1;if(!i)return!1;if(("-"===(t=e[n])||"+"===t)&&(t=e[++n]),"0"===t){if(n+1===i)return!0;if("b"===(t=e[++n])){for(n++;n<i;n++)if("_"!==(t=e[n])){if("0"!==t&&"1"!==t)return!1;o=!0}return o&&"_"!==t}if("x"===t){for(n++;n<i;n++)if("_"!==(t=e[n])){if(!r(e.charCodeAt(n)))return!1;o=!0}return o&&"_"!==t}for(;n<i;n++)if("_"!==(t=e[n])){if(!s(e.charCodeAt(n)))return!1;o=!0}return o&&"_"!==t}if("_"===t)return!1;for(;n<i;n++)if("_"!==(t=e[n])){if(":"===t)break;if(!a(e.charCodeAt(n)))return!1;o=!0}return!!o&&"_"!==t&&(":"!==t||/^(:[0-5]?[0-9])+$/.test(e.slice(n)))}function u(e){var t,i,n=e,r=1,s=[];return(-1!==n.indexOf("_")&&(n=n.replace(/_/g,"")),("-"===(t=n[0])||"+"===t)&&("-"===t&&(r=-1),t=(n=n.slice(1))[0]),"0"===n)?0:"0"===t?"b"===n[1]?r*parseInt(n.slice(2),2):"x"===n[1]?r*parseInt(n,16):r*parseInt(n,8):-1!==n.indexOf(":")?(n.split(":").forEach(function(e){s.unshift(parseInt(e,10))}),n=0,i=1,s.forEach(function(e){n+=e*i,i*=60}),r*n):r*parseInt(n,10)}function c(e){return"[object Number]"===Object.prototype.toString.call(e)&&e%1==0&&!n.isNegativeZero(e)}e.exports=new(i(3016))("tag:yaml.org,2002:int",{kind:"scalar",resolve:o,construct:u,predicate:c,represent:{binary:function(e){return e>=0?"0b"+e.toString(2):"-0b"+e.toString(2).slice(1)},octal:function(e){return e>=0?"0"+e.toString(8):"-0"+e.toString(8).slice(1)},decimal:function(e){return e.toString(10)},hexadecimal:function(e){return e>=0?"0x"+e.toString(16).toUpperCase():"-0x"+e.toString(16).toUpperCase().slice(1)}},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}})},5953:(e,t,i)=>{"use strict";let n=i(908),r=i(5582);e.exports=function(e){let t=Object.assign({},e);return t.delimiters=r.arrayify(t.delims||t.delimiters||"---"),1===t.delimiters.length&&t.delimiters.push(t.delimiters[0]),t.language=(t.language||t.lang||"yaml").toLowerCase(),t.engines=Object.assign({},n,t.parsers,t.engines),t}},6165:(e,t,i)=>{"use strict";e.exports=new(i(5167))({include:[i(8453)],implicit:[i(1735),i(9123)],explicit:[i(3270),i(6378),i(6774),i(4951)]})},6378:(e,t,i)=>{"use strict";var n=i(3016),r=Object.prototype.hasOwnProperty,s=Object.prototype.toString;e.exports=new n("tag:yaml.org,2002:omap",{kind:"sequence",resolve:function(e){if(null===e)return!0;var t,i,n,a,o,u=[],c=e;for(t=0,i=c.length;t<i;t+=1){if(n=c[t],o=!1,"[object Object]"!==s.call(n))return!1;for(a in n)if(r.call(n,a))if(o)return!1;else o=!0;if(!o||-1!==u.indexOf(a))return!1;u.push(a)}return!0},construct:function(e){return null!==e?e:[]}})},6567:(e,t,i)=>{"use strict";e.exports=new(i(5167))({explicit:[i(648),i(3128),i(8285)]})},6622:(e,t,i)=>{"use strict";function n(e){if(null===e)return!0;var t=e.length;return 1===t&&"~"===e||4===t&&("null"===e||"Null"===e||"NULL"===e)}function r(){return null}function s(e){return null===e}e.exports=new(i(3016))("tag:yaml.org,2002:null",{kind:"scalar",resolve:n,construct:r,predicate:s,represent:{canonical:function(){return"~"},lowercase:function(){return"null"},uppercase:function(){return"NULL"},camelcase:function(){return"Null"}},defaultStyle:"lowercase"})},6711:e=>{"use strict";function t(e,t){Error.call(this),this.name="YAMLException",this.reason=e,this.mark=t,this.message=(this.reason||"(unknown reason)")+(this.mark?" "+this.mark.toString():""),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack||""}t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t.prototype.toString=function(e){var t=this.name+": ";return t+=this.reason||"(unknown reason)",!e&&this.mark&&(t+=" "+this.mark.toString()),t},e.exports=t},6774:(e,t,i)=>{"use strict";var n=i(3016),r=Object.prototype.toString;e.exports=new n("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:function(e){if(null===e)return!0;var t,i,n,s,a,o=e;for(t=0,a=Array(o.length),i=o.length;t<i;t+=1){if(n=o[t],"[object Object]"!==r.call(n)||1!==(s=Object.keys(n)).length)return!1;a[t]=[s[0],n[s[0]]]}return!0},construct:function(e){if(null===e)return[];var t,i,n,r,s,a=e;for(t=0,s=Array(a.length),i=a.length;t<i;t+=1)r=Object.keys(n=a[t]),s[t]=[r[0],n[r[0]]];return s}})},6872:(e,t,i)=>{"use strict";let n=i(1929),r=i(5953);e.exports=function(e,t,i){let s=r(i),a=n(e,s);if("function"!=typeof a.parse)throw TypeError('expected "'+e+'.parse" to be a function');return a.parse(t,s)}},6929:(e,t,i)=>{"use strict";let n=i(3052),r=i(9666),s=i(5582);e.exports=function(e){return"object"!==n(e)&&(e={content:e}),"object"!==n(e.data)&&(e.data={}),e.contents&&null==e.content&&(e.content=e.contents),s.define(e,"orig",s.toBuffer(e.content)),s.define(e,"language",e.language||""),s.define(e,"matter",e.matter||""),s.define(e,"stringify",function(t,i){return i&&i.language&&(e.language=i.language),r(e,t,i)}),e.content=s.toString(e.content),e.isEmpty=!1,e.excerpt="",e}},7490:(e,t,i)=>{"use strict";let n=i(5953);e.exports=function(e,t){let i=n(t);if(null==e.data&&(e.data={}),"function"==typeof i.excerpt)return i.excerpt(e,i);let r=e.data.excerpt_separator||i.excerpt_separator;if(null==r&&(!1===i.excerpt||null==i.excerpt))return e;let s="string"==typeof i.excerpt?i.excerpt:r||i.delimiters[0],a=e.content.indexOf(s);return -1!==a&&(e.excerpt=e.content.slice(0,a)),e}},7577:(e,t,i)=>{"use strict";var n=i(5019),r=i(1857);function s(e){return function(){throw Error("Function "+e+" is deprecated and cannot be used.")}}e.exports.Type=i(3016),e.exports.Schema=i(5167),e.exports.FAILSAFE_SCHEMA=i(6567),e.exports.JSON_SCHEMA=i(7724),e.exports.CORE_SCHEMA=i(8453),e.exports.DEFAULT_SAFE_SCHEMA=i(6165),e.exports.DEFAULT_FULL_SCHEMA=i(9813),e.exports.load=n.load,e.exports.loadAll=n.loadAll,e.exports.safeLoad=n.safeLoad,e.exports.safeLoadAll=n.safeLoadAll,e.exports.dump=r.dump,e.exports.safeDump=r.safeDump,e.exports.YAMLException=i(6711),e.exports.MINIMAL_SCHEMA=i(6567),e.exports.SAFE_SCHEMA=i(6165),e.exports.DEFAULT_SCHEMA=i(9813),e.exports.scan=s("scan"),e.exports.parse=s("parse"),e.exports.compose=s("compose"),e.exports.addConstructor=s("addConstructor")},7651:(e,t,i)=>{"use strict";function n(){return!0}function r(){}function s(){return""}function a(e){return void 0===e}e.exports=new(i(3016))("tag:yaml.org,2002:js/undefined",{kind:"scalar",resolve:n,construct:r,predicate:a,represent:s})},7724:(e,t,i)=>{"use strict";e.exports=new(i(5167))({include:[i(6567)],implicit:[i(6622),i(2631),i(5762),i(2833)]})},8285:(e,t,i)=>{"use strict";e.exports=new(i(3016))("tag:yaml.org,2002:map",{kind:"mapping",construct:function(e){return null!==e?e:{}}})},8453:(e,t,i)=>{"use strict";e.exports=new(i(5167))({include:[i(7724)]})},8759:function(e){!function(t,i){e.exports=i()}(0,function(){return function(e){var t={};function i(n){if(t[n])return t[n].exports;var r=t[n]={exports:{},id:n,loaded:!1};return e[n].call(r.exports,r,r.exports,i),r.loaded=!0,r.exports}return i.m=e,i.c=t,i.p="",i(0)}([function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=i(1),r=i(3),s=i(8),a=i(15);function o(e,t,i){var a,o=null,u=function(e,t){i&&i(e,t),o&&o.visit(e,t)},c="function"==typeof i?u:null,h=!1;if(t){h="boolean"==typeof t.comment&&t.comment;var l="boolean"==typeof t.attachComment&&t.attachComment;(h||l)&&((o=new n.CommentHandler).attach=l,t.comment=!0,c=u)}var p=!1;t&&"string"==typeof t.sourceType&&(p="module"===t.sourceType),a=t&&"boolean"==typeof t.jsx&&t.jsx?new r.JSXParser(e,t,c):new s.Parser(e,t,c);var d=p?a.parseModule():a.parseScript();return h&&o&&(d.comments=o.comments),a.config.tokens&&(d.tokens=a.tokens),a.config.tolerant&&(d.errors=a.errorHandler.errors),d}t.parse=o,t.parseModule=function(e,t,i){var n=t||{};return n.sourceType="module",o(e,n,i)},t.parseScript=function(e,t,i){var n=t||{};return n.sourceType="script",o(e,n,i)},t.tokenize=function(e,t,i){var n,r=new a.Tokenizer(e,t);n=[];try{for(;;){var s=r.getNextToken();if(!s)break;i&&(s=i(s)),n.push(s)}}catch(e){r.errorHandler.tolerate(e)}return r.errorHandler.tolerant&&(n.errors=r.errors()),n},t.Syntax=i(2).Syntax,t.version="4.0.1"},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=i(2);t.CommentHandler=function(){function e(){this.attach=!1,this.comments=[],this.stack=[],this.leading=[],this.trailing=[]}return e.prototype.insertInnerComments=function(e,t){if(e.type===n.Syntax.BlockStatement&&0===e.body.length){for(var i=[],r=this.leading.length-1;r>=0;--r){var s=this.leading[r];t.end.offset>=s.start&&(i.unshift(s.comment),this.leading.splice(r,1),this.trailing.splice(r,1))}i.length&&(e.innerComments=i)}},e.prototype.findTrailingComments=function(e){var t=[];if(this.trailing.length>0){for(var i=this.trailing.length-1;i>=0;--i){var n=this.trailing[i];n.start>=e.end.offset&&t.unshift(n.comment)}return this.trailing.length=0,t}var r=this.stack[this.stack.length-1];if(r&&r.node.trailingComments){var s=r.node.trailingComments[0];s&&s.range[0]>=e.end.offset&&(t=r.node.trailingComments,delete r.node.trailingComments)}return t},e.prototype.findLeadingComments=function(e){for(var t,i=[];this.stack.length>0;){var n=this.stack[this.stack.length-1];if(n&&n.start>=e.start.offset)t=n.node,this.stack.pop();else break}if(t){for(var r=t.leadingComments?t.leadingComments.length:0,s=r-1;s>=0;--s){var a=t.leadingComments[s];a.range[1]<=e.start.offset&&(i.unshift(a),t.leadingComments.splice(s,1))}return t.leadingComments&&0===t.leadingComments.length&&delete t.leadingComments,i}for(var s=this.leading.length-1;s>=0;--s){var n=this.leading[s];n.start<=e.start.offset&&(i.unshift(n.comment),this.leading.splice(s,1))}return i},e.prototype.visitNode=function(e,t){if(e.type!==n.Syntax.Program||!(e.body.length>0)){this.insertInnerComments(e,t);var i=this.findTrailingComments(t),r=this.findLeadingComments(t);r.length>0&&(e.leadingComments=r),i.length>0&&(e.trailingComments=i),this.stack.push({node:e,start:t.start.offset})}},e.prototype.visitComment=function(e,t){var i="L"===e.type[0]?"Line":"Block",n={type:i,value:e.value};if(e.range&&(n.range=e.range),e.loc&&(n.loc=e.loc),this.comments.push(n),this.attach){var r={comment:{type:i,value:e.value,range:[t.start.offset,t.end.offset]},start:t.start.offset};e.loc&&(r.comment.loc=e.loc),e.type=i,this.leading.push(r),this.trailing.push(r)}},e.prototype.visit=function(e,t){"LineComment"===e.type||"BlockComment"===e.type?this.visitComment(e,t):this.attach&&this.visitNode(e,t)},e}()},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Syntax={AssignmentExpression:"AssignmentExpression",AssignmentPattern:"AssignmentPattern",ArrayExpression:"ArrayExpression",ArrayPattern:"ArrayPattern",ArrowFunctionExpression:"ArrowFunctionExpression",AwaitExpression:"AwaitExpression",BlockStatement:"BlockStatement",BinaryExpression:"BinaryExpression",BreakStatement:"BreakStatement",CallExpression:"CallExpression",CatchClause:"CatchClause",ClassBody:"ClassBody",ClassDeclaration:"ClassDeclaration",ClassExpression:"ClassExpression",ConditionalExpression:"ConditionalExpression",ContinueStatement:"ContinueStatement",DoWhileStatement:"DoWhileStatement",DebuggerStatement:"DebuggerStatement",EmptyStatement:"EmptyStatement",ExportAllDeclaration:"ExportAllDeclaration",ExportDefaultDeclaration:"ExportDefaultDeclaration",ExportNamedDeclaration:"ExportNamedDeclaration",ExportSpecifier:"ExportSpecifier",ExpressionStatement:"ExpressionStatement",ForStatement:"ForStatement",ForOfStatement:"ForOfStatement",ForInStatement:"ForInStatement",FunctionDeclaration:"FunctionDeclaration",FunctionExpression:"FunctionExpression",Identifier:"Identifier",IfStatement:"IfStatement",ImportDeclaration:"ImportDeclaration",ImportDefaultSpecifier:"ImportDefaultSpecifier",ImportNamespaceSpecifier:"ImportNamespaceSpecifier",ImportSpecifier:"ImportSpecifier",Literal:"Literal",LabeledStatement:"LabeledStatement",LogicalExpression:"LogicalExpression",MemberExpression:"MemberExpression",MetaProperty:"MetaProperty",MethodDefinition:"MethodDefinition",NewExpression:"NewExpression",ObjectExpression:"ObjectExpression",ObjectPattern:"ObjectPattern",Program:"Program",Property:"Property",RestElement:"RestElement",ReturnStatement:"ReturnStatement",SequenceExpression:"SequenceExpression",SpreadElement:"SpreadElement",Super:"Super",SwitchCase:"SwitchCase",SwitchStatement:"SwitchStatement",TaggedTemplateExpression:"TaggedTemplateExpression",TemplateElement:"TemplateElement",TemplateLiteral:"TemplateLiteral",ThisExpression:"ThisExpression",ThrowStatement:"ThrowStatement",TryStatement:"TryStatement",UnaryExpression:"UnaryExpression",UpdateExpression:"UpdateExpression",VariableDeclaration:"VariableDeclaration",VariableDeclarator:"VariableDeclarator",WhileStatement:"WhileStatement",WithStatement:"WithStatement",YieldExpression:"YieldExpression"}},function(e,t,i){"use strict";var n=this&&this.__extends||function(){var e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])};return function(t,i){function n(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0});var r=i(4),s=i(5),a=i(6),o=i(7),u=i(8),c=i(13),h=i(14);function l(e){var t;switch(e.type){case a.JSXSyntax.JSXIdentifier:t=e.name;break;case a.JSXSyntax.JSXNamespacedName:var i=e;t=l(i.namespace)+":"+l(i.name);break;case a.JSXSyntax.JSXMemberExpression:var n=e;t=l(n.object)+"."+l(n.property)}return t}c.TokenName[100]="JSXIdentifier",c.TokenName[101]="JSXText",t.JSXParser=function(e){function t(t,i,n){return e.call(this,t,i,n)||this}return n(t,e),t.prototype.parsePrimaryExpression=function(){return this.match("<")?this.parseJSXRoot():e.prototype.parsePrimaryExpression.call(this)},t.prototype.startJSX=function(){this.scanner.index=this.startMarker.index,this.scanner.lineNumber=this.startMarker.line,this.scanner.lineStart=this.startMarker.index-this.startMarker.column},t.prototype.finishJSX=function(){this.nextToken()},t.prototype.reenterJSX=function(){this.startJSX(),this.expectJSX("}"),this.config.tokens&&this.tokens.pop()},t.prototype.createJSXNode=function(){return this.collectComments(),{index:this.scanner.index,line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}},t.prototype.createJSXChildNode=function(){return{index:this.scanner.index,line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}},t.prototype.scanXHTMLEntity=function(e){for(var t="&",i=!0,n=!1,s=!1,a=!1;!this.scanner.eof()&&i&&!n;){var o=this.scanner.source[this.scanner.index];if(o===e)break;if(n=";"===o,t+=o,++this.scanner.index,!n)switch(t.length){case 2:s="#"===o;break;case 3:s&&(i=(a="x"===o)||r.Character.isDecimalDigit(o.charCodeAt(0)),s=s&&!a);break;default:i=(i=i&&!(s&&!r.Character.isDecimalDigit(o.charCodeAt(0))))&&!(a&&!r.Character.isHexDigit(o.charCodeAt(0)))}}if(i&&n&&t.length>2){var u=t.substr(1,t.length-2);s&&u.length>1?t=String.fromCharCode(parseInt(u.substr(1),10)):a&&u.length>2?t=String.fromCharCode(parseInt("0"+u.substr(1),16)):s||a||!h.XHTMLEntities[u]||(t=h.XHTMLEntities[u])}return t},t.prototype.lexJSX=function(){var e=this.scanner.source.charCodeAt(this.scanner.index);if(60===e||62===e||47===e||58===e||61===e||123===e||125===e){var t=this.scanner.source[this.scanner.index++];return{type:7,value:t,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:this.scanner.index-1,end:this.scanner.index}}if(34===e||39===e){for(var i=this.scanner.index,n=this.scanner.source[this.scanner.index++],s="";!this.scanner.eof();){var a=this.scanner.source[this.scanner.index++];if(a===n)break;"&"===a?s+=this.scanXHTMLEntity(n):s+=a}return{type:8,value:s,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:i,end:this.scanner.index}}if(46===e){var o=this.scanner.source.charCodeAt(this.scanner.index+1),u=this.scanner.source.charCodeAt(this.scanner.index+2),t=46===o&&46===u?"...":".",i=this.scanner.index;return this.scanner.index+=t.length,{type:7,value:t,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:i,end:this.scanner.index}}if(96===e)return{type:10,value:"",lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:this.scanner.index,end:this.scanner.index};if(r.Character.isIdentifierStart(e)&&92!==e){var i=this.scanner.index;for(++this.scanner.index;!this.scanner.eof();){var a=this.scanner.source.charCodeAt(this.scanner.index);if(r.Character.isIdentifierPart(a)&&92!==a)++this.scanner.index;else if(45===a)++this.scanner.index;else break}return{type:100,value:this.scanner.source.slice(i,this.scanner.index),lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:i,end:this.scanner.index}}return this.scanner.lex()},t.prototype.nextJSXToken=function(){this.collectComments(),this.startMarker.index=this.scanner.index,this.startMarker.line=this.scanner.lineNumber,this.startMarker.column=this.scanner.index-this.scanner.lineStart;var e=this.lexJSX();return this.lastMarker.index=this.scanner.index,this.lastMarker.line=this.scanner.lineNumber,this.lastMarker.column=this.scanner.index-this.scanner.lineStart,this.config.tokens&&this.tokens.push(this.convertToken(e)),e},t.prototype.nextJSXText=function(){this.startMarker.index=this.scanner.index,this.startMarker.line=this.scanner.lineNumber,this.startMarker.column=this.scanner.index-this.scanner.lineStart;for(var e=this.scanner.index,t="";!this.scanner.eof();){var i=this.scanner.source[this.scanner.index];if("{"===i||"<"===i)break;++this.scanner.index,t+=i,r.Character.isLineTerminator(i.charCodeAt(0))&&(++this.scanner.lineNumber,"\r"===i&&"\n"===this.scanner.source[this.scanner.index]&&++this.scanner.index,this.scanner.lineStart=this.scanner.index)}this.lastMarker.index=this.scanner.index,this.lastMarker.line=this.scanner.lineNumber,this.lastMarker.column=this.scanner.index-this.scanner.lineStart;var n={type:101,value:t,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:e,end:this.scanner.index};return t.length>0&&this.config.tokens&&this.tokens.push(this.convertToken(n)),n},t.prototype.peekJSXToken=function(){var e=this.scanner.saveState();this.scanner.scanComments();var t=this.lexJSX();return this.scanner.restoreState(e),t},t.prototype.expectJSX=function(e){var t=this.nextJSXToken();(7!==t.type||t.value!==e)&&this.throwUnexpectedToken(t)},t.prototype.matchJSX=function(e){var t=this.peekJSXToken();return 7===t.type&&t.value===e},t.prototype.parseJSXIdentifier=function(){var e=this.createJSXNode(),t=this.nextJSXToken();return 100!==t.type&&this.throwUnexpectedToken(t),this.finalize(e,new s.JSXIdentifier(t.value))},t.prototype.parseJSXElementName=function(){var e=this.createJSXNode(),t=this.parseJSXIdentifier();if(this.matchJSX(":")){var i=t;this.expectJSX(":");var n=this.parseJSXIdentifier();t=this.finalize(e,new s.JSXNamespacedName(i,n))}else if(this.matchJSX("."))for(;this.matchJSX(".");){var r=t;this.expectJSX(".");var a=this.parseJSXIdentifier();t=this.finalize(e,new s.JSXMemberExpression(r,a))}return t},t.prototype.parseJSXAttributeName=function(){var e,t=this.createJSXNode(),i=this.parseJSXIdentifier();if(this.matchJSX(":")){var n=i;this.expectJSX(":");var r=this.parseJSXIdentifier();e=this.finalize(t,new s.JSXNamespacedName(n,r))}else e=i;return e},t.prototype.parseJSXStringLiteralAttribute=function(){var e=this.createJSXNode(),t=this.nextJSXToken();8!==t.type&&this.throwUnexpectedToken(t);var i=this.getTokenRaw(t);return this.finalize(e,new o.Literal(t.value,i))},t.prototype.parseJSXExpressionAttribute=function(){var e=this.createJSXNode();this.expectJSX("{"),this.finishJSX(),this.match("}")&&this.tolerateError("JSX attributes must only be assigned a non-empty expression");var t=this.parseAssignmentExpression();return this.reenterJSX(),this.finalize(e,new s.JSXExpressionContainer(t))},t.prototype.parseJSXAttributeValue=function(){return this.matchJSX("{")?this.parseJSXExpressionAttribute():this.matchJSX("<")?this.parseJSXElement():this.parseJSXStringLiteralAttribute()},t.prototype.parseJSXNameValueAttribute=function(){var e=this.createJSXNode(),t=this.parseJSXAttributeName(),i=null;return this.matchJSX("=")&&(this.expectJSX("="),i=this.parseJSXAttributeValue()),this.finalize(e,new s.JSXAttribute(t,i))},t.prototype.parseJSXSpreadAttribute=function(){var e=this.createJSXNode();this.expectJSX("{"),this.expectJSX("..."),this.finishJSX();var t=this.parseAssignmentExpression();return this.reenterJSX(),this.finalize(e,new s.JSXSpreadAttribute(t))},t.prototype.parseJSXAttributes=function(){for(var e=[];!this.matchJSX("/")&&!this.matchJSX(">");){var t=this.matchJSX("{")?this.parseJSXSpreadAttribute():this.parseJSXNameValueAttribute();e.push(t)}return e},t.prototype.parseJSXOpeningElement=function(){var e=this.createJSXNode();this.expectJSX("<");var t=this.parseJSXElementName(),i=this.parseJSXAttributes(),n=this.matchJSX("/");return n&&this.expectJSX("/"),this.expectJSX(">"),this.finalize(e,new s.JSXOpeningElement(t,n,i))},t.prototype.parseJSXBoundaryElement=function(){var e=this.createJSXNode();if(this.expectJSX("<"),this.matchJSX("/")){this.expectJSX("/");var t=this.parseJSXElementName();return this.expectJSX(">"),this.finalize(e,new s.JSXClosingElement(t))}var i=this.parseJSXElementName(),n=this.parseJSXAttributes(),r=this.matchJSX("/");return r&&this.expectJSX("/"),this.expectJSX(">"),this.finalize(e,new s.JSXOpeningElement(i,r,n))},t.prototype.parseJSXEmptyExpression=function(){var e=this.createJSXChildNode();return this.collectComments(),this.lastMarker.index=this.scanner.index,this.lastMarker.line=this.scanner.lineNumber,this.lastMarker.column=this.scanner.index-this.scanner.lineStart,this.finalize(e,new s.JSXEmptyExpression)},t.prototype.parseJSXExpressionContainer=function(){var e,t=this.createJSXNode();return this.expectJSX("{"),this.matchJSX("}")?(e=this.parseJSXEmptyExpression(),this.expectJSX("}")):(this.finishJSX(),e=this.parseAssignmentExpression(),this.reenterJSX()),this.finalize(t,new s.JSXExpressionContainer(e))},t.prototype.parseJSXChildren=function(){for(var e=[];!this.scanner.eof();){var t=this.createJSXChildNode(),i=this.nextJSXText();if(i.start<i.end){var n=this.getTokenRaw(i),r=this.finalize(t,new s.JSXText(i.value,n));e.push(r)}if("{"===this.scanner.source[this.scanner.index]){var a=this.parseJSXExpressionContainer();e.push(a)}else break}return e},t.prototype.parseComplexJSXElement=function(e){for(var t=[];!this.scanner.eof();){e.children=e.children.concat(this.parseJSXChildren());var i=this.createJSXChildNode(),n=this.parseJSXBoundaryElement();if(n.type===a.JSXSyntax.JSXOpeningElement){var r=n;if(r.selfClosing){var o=this.finalize(i,new s.JSXElement(r,[],null));e.children.push(o)}else t.push(e),e={node:i,opening:r,closing:null,children:[]}}if(n.type===a.JSXSyntax.JSXClosingElement){e.closing=n;var u=l(e.opening.name);if(u!==l(e.closing.name)&&this.tolerateError("Expected corresponding JSX closing tag for %0",u),t.length>0){var o=this.finalize(e.node,new s.JSXElement(e.opening,e.children,e.closing));(e=t[t.length-1]).children.push(o),t.pop()}else break}}return e},t.prototype.parseJSXElement=function(){var e=this.createJSXNode(),t=this.parseJSXOpeningElement(),i=[],n=null;if(!t.selfClosing){var r=this.parseComplexJSXElement({node:e,opening:t,closing:n,children:i});i=r.children,n=r.closing}return this.finalize(e,new s.JSXElement(t,i,n))},t.prototype.parseJSXRoot=function(){this.config.tokens&&this.tokens.pop(),this.startJSX();var e=this.parseJSXElement();return this.finishJSX(),e},t.prototype.isStartOfExpression=function(){return e.prototype.isStartOfExpression.call(this)||this.match("<")},t}(u.Parser)},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i={NonAsciiIdentifierStart:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDEC0-\uDEF8]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]/,NonAsciiIdentifierPart:/[\xAA\xB5\xB7\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u08A0-\u08B4\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C81-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D01-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1369-\u1371\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1CD0-\u1CD2\u1CD4-\u1CF6\u1CF8\u1CF9\u1D00-\u1DF5\u1DFC-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C4\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDEC0-\uDEF8]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/};t.Character={fromCodePoint:function(e){return e<65536?String.fromCharCode(e):String.fromCharCode(55296+(e-65536>>10))+String.fromCharCode(56320+(e-65536&1023))},isWhiteSpace:function(e){return 32===e||9===e||11===e||12===e||160===e||e>=5760&&[5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279].indexOf(e)>=0},isLineTerminator:function(e){return 10===e||13===e||8232===e||8233===e},isIdentifierStart:function(e){return 36===e||95===e||e>=65&&e<=90||e>=97&&e<=122||92===e||e>=128&&i.NonAsciiIdentifierStart.test(t.Character.fromCodePoint(e))},isIdentifierPart:function(e){return 36===e||95===e||e>=65&&e<=90||e>=97&&e<=122||e>=48&&e<=57||92===e||e>=128&&i.NonAsciiIdentifierPart.test(t.Character.fromCodePoint(e))},isDecimalDigit:function(e){return e>=48&&e<=57},isHexDigit:function(e){return e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102},isOctalDigit:function(e){return e>=48&&e<=55}}},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=i(6);t.JSXClosingElement=function(){return function(e){this.type=n.JSXSyntax.JSXClosingElement,this.name=e}}(),t.JSXElement=function(){return function(e,t,i){this.type=n.JSXSyntax.JSXElement,this.openingElement=e,this.children=t,this.closingElement=i}}(),t.JSXEmptyExpression=function(){return function(){this.type=n.JSXSyntax.JSXEmptyExpression}}(),t.JSXExpressionContainer=function(){return function(e){this.type=n.JSXSyntax.JSXExpressionContainer,this.expression=e}}(),t.JSXIdentifier=function(){return function(e){this.type=n.JSXSyntax.JSXIdentifier,this.name=e}}(),t.JSXMemberExpression=function(){return function(e,t){this.type=n.JSXSyntax.JSXMemberExpression,this.object=e,this.property=t}}(),t.JSXAttribute=function(){return function(e,t){this.type=n.JSXSyntax.JSXAttribute,this.name=e,this.value=t}}(),t.JSXNamespacedName=function(){return function(e,t){this.type=n.JSXSyntax.JSXNamespacedName,this.namespace=e,this.name=t}}(),t.JSXOpeningElement=function(){return function(e,t,i){this.type=n.JSXSyntax.JSXOpeningElement,this.name=e,this.selfClosing=t,this.attributes=i}}(),t.JSXSpreadAttribute=function(){return function(e){this.type=n.JSXSyntax.JSXSpreadAttribute,this.argument=e}}(),t.JSXText=function(){return function(e,t){this.type=n.JSXSyntax.JSXText,this.value=e,this.raw=t}}()},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.JSXSyntax={JSXAttribute:"JSXAttribute",JSXClosingElement:"JSXClosingElement",JSXElement:"JSXElement",JSXEmptyExpression:"JSXEmptyExpression",JSXExpressionContainer:"JSXExpressionContainer",JSXIdentifier:"JSXIdentifier",JSXMemberExpression:"JSXMemberExpression",JSXNamespacedName:"JSXNamespacedName",JSXOpeningElement:"JSXOpeningElement",JSXSpreadAttribute:"JSXSpreadAttribute",JSXText:"JSXText"}},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=i(2);t.ArrayExpression=function(){return function(e){this.type=n.Syntax.ArrayExpression,this.elements=e}}(),t.ArrayPattern=function(){return function(e){this.type=n.Syntax.ArrayPattern,this.elements=e}}(),t.ArrowFunctionExpression=function(){return function(e,t,i){this.type=n.Syntax.ArrowFunctionExpression,this.id=null,this.params=e,this.body=t,this.generator=!1,this.expression=i,this.async=!1}}(),t.AssignmentExpression=function(){return function(e,t,i){this.type=n.Syntax.AssignmentExpression,this.operator=e,this.left=t,this.right=i}}(),t.AssignmentPattern=function(){return function(e,t){this.type=n.Syntax.AssignmentPattern,this.left=e,this.right=t}}(),t.AsyncArrowFunctionExpression=function(){return function(e,t,i){this.type=n.Syntax.ArrowFunctionExpression,this.id=null,this.params=e,this.body=t,this.generator=!1,this.expression=i,this.async=!0}}(),t.AsyncFunctionDeclaration=function(){return function(e,t,i){this.type=n.Syntax.FunctionDeclaration,this.id=e,this.params=t,this.body=i,this.generator=!1,this.expression=!1,this.async=!0}}(),t.AsyncFunctionExpression=function(){return function(e,t,i){this.type=n.Syntax.FunctionExpression,this.id=e,this.params=t,this.body=i,this.generator=!1,this.expression=!1,this.async=!0}}(),t.AwaitExpression=function(){return function(e){this.type=n.Syntax.AwaitExpression,this.argument=e}}(),t.BinaryExpression=function(){return function(e,t,i){var r="||"===e||"&&"===e;this.type=r?n.Syntax.LogicalExpression:n.Syntax.BinaryExpression,this.operator=e,this.left=t,this.right=i}}(),t.BlockStatement=function(){return function(e){this.type=n.Syntax.BlockStatement,this.body=e}}(),t.BreakStatement=function(){return function(e){this.type=n.Syntax.BreakStatement,this.label=e}}(),t.CallExpression=function(){return function(e,t){this.type=n.Syntax.CallExpression,this.callee=e,this.arguments=t}}(),t.CatchClause=function(){return function(e,t){this.type=n.Syntax.CatchClause,this.param=e,this.body=t}}(),t.ClassBody=function(){return function(e){this.type=n.Syntax.ClassBody,this.body=e}}(),t.ClassDeclaration=function(){return function(e,t,i){this.type=n.Syntax.ClassDeclaration,this.id=e,this.superClass=t,this.body=i}}(),t.ClassExpression=function(){return function(e,t,i){this.type=n.Syntax.ClassExpression,this.id=e,this.superClass=t,this.body=i}}(),t.ComputedMemberExpression=function(){return function(e,t){this.type=n.Syntax.MemberExpression,this.computed=!0,this.object=e,this.property=t}}(),t.ConditionalExpression=function(){return function(e,t,i){this.type=n.Syntax.ConditionalExpression,this.test=e,this.consequent=t,this.alternate=i}}(),t.ContinueStatement=function(){return function(e){this.type=n.Syntax.ContinueStatement,this.label=e}}(),t.DebuggerStatement=function(){return function(){this.type=n.Syntax.DebuggerStatement}}(),t.Directive=function(){return function(e,t){this.type=n.Syntax.ExpressionStatement,this.expression=e,this.directive=t}}(),t.DoWhileStatement=function(){return function(e,t){this.type=n.Syntax.DoWhileStatement,this.body=e,this.test=t}}(),t.EmptyStatement=function(){return function(){this.type=n.Syntax.EmptyStatement}}(),t.ExportAllDeclaration=function(){return function(e){this.type=n.Syntax.ExportAllDeclaration,this.source=e}}(),t.ExportDefaultDeclaration=function(){return function(e){this.type=n.Syntax.ExportDefaultDeclaration,this.declaration=e}}(),t.ExportNamedDeclaration=function(){return function(e,t,i){this.type=n.Syntax.ExportNamedDeclaration,this.declaration=e,this.specifiers=t,this.source=i}}(),t.ExportSpecifier=function(){return function(e,t){this.type=n.Syntax.ExportSpecifier,this.exported=t,this.local=e}}(),t.ExpressionStatement=function(){return function(e){this.type=n.Syntax.ExpressionStatement,this.expression=e}}(),t.ForInStatement=function(){return function(e,t,i){this.type=n.Syntax.ForInStatement,this.left=e,this.right=t,this.body=i,this.each=!1}}(),t.ForOfStatement=function(){return function(e,t,i){this.type=n.Syntax.ForOfStatement,this.left=e,this.right=t,this.body=i}}(),t.ForStatement=function(){return function(e,t,i,r){this.type=n.Syntax.ForStatement,this.init=e,this.test=t,this.update=i,this.body=r}}(),t.FunctionDeclaration=function(){return function(e,t,i,r){this.type=n.Syntax.FunctionDeclaration,this.id=e,this.params=t,this.body=i,this.generator=r,this.expression=!1,this.async=!1}}(),t.FunctionExpression=function(){return function(e,t,i,r){this.type=n.Syntax.FunctionExpression,this.id=e,this.params=t,this.body=i,this.generator=r,this.expression=!1,this.async=!1}}(),t.Identifier=function(){return function(e){this.type=n.Syntax.Identifier,this.name=e}}(),t.IfStatement=function(){return function(e,t,i){this.type=n.Syntax.IfStatement,this.test=e,this.consequent=t,this.alternate=i}}(),t.ImportDeclaration=function(){return function(e,t){this.type=n.Syntax.ImportDeclaration,this.specifiers=e,this.source=t}}(),t.ImportDefaultSpecifier=function(){return function(e){this.type=n.Syntax.ImportDefaultSpecifier,this.local=e}}(),t.ImportNamespaceSpecifier=function(){return function(e){this.type=n.Syntax.ImportNamespaceSpecifier,this.local=e}}(),t.ImportSpecifier=function(){return function(e,t){this.type=n.Syntax.ImportSpecifier,this.local=e,this.imported=t}}(),t.LabeledStatement=function(){return function(e,t){this.type=n.Syntax.LabeledStatement,this.label=e,this.body=t}}(),t.Literal=function(){return function(e,t){this.type=n.Syntax.Literal,this.value=e,this.raw=t}}(),t.MetaProperty=function(){return function(e,t){this.type=n.Syntax.MetaProperty,this.meta=e,this.property=t}}(),t.MethodDefinition=function(){return function(e,t,i,r,s){this.type=n.Syntax.MethodDefinition,this.key=e,this.computed=t,this.value=i,this.kind=r,this.static=s}}(),t.Module=function(){return function(e){this.type=n.Syntax.Program,this.body=e,this.sourceType="module"}}(),t.NewExpression=function(){return function(e,t){this.type=n.Syntax.NewExpression,this.callee=e,this.arguments=t}}(),t.ObjectExpression=function(){return function(e){this.type=n.Syntax.ObjectExpression,this.properties=e}}(),t.ObjectPattern=function(){return function(e){this.type=n.Syntax.ObjectPattern,this.properties=e}}(),t.Property=function(){return function(e,t,i,r,s,a){this.type=n.Syntax.Property,this.key=t,this.computed=i,this.value=r,this.kind=e,this.method=s,this.shorthand=a}}(),t.RegexLiteral=function(){return function(e,t,i,r){this.type=n.Syntax.Literal,this.value=e,this.raw=t,this.regex={pattern:i,flags:r}}}(),t.RestElement=function(){return function(e){this.type=n.Syntax.RestElement,this.argument=e}}(),t.ReturnStatement=function(){return function(e){this.type=n.Syntax.ReturnStatement,this.argument=e}}(),t.Script=function(){return function(e){this.type=n.Syntax.Program,this.body=e,this.sourceType="script"}}(),t.SequenceExpression=function(){return function(e){this.type=n.Syntax.SequenceExpression,this.expressions=e}}(),t.SpreadElement=function(){return function(e){this.type=n.Syntax.SpreadElement,this.argument=e}}(),t.StaticMemberExpression=function(){return function(e,t){this.type=n.Syntax.MemberExpression,this.computed=!1,this.object=e,this.property=t}}(),t.Super=function(){return function(){this.type=n.Syntax.Super}}(),t.SwitchCase=function(){return function(e,t){this.type=n.Syntax.SwitchCase,this.test=e,this.consequent=t}}(),t.SwitchStatement=function(){return function(e,t){this.type=n.Syntax.SwitchStatement,this.discriminant=e,this.cases=t}}(),t.TaggedTemplateExpression=function(){return function(e,t){this.type=n.Syntax.TaggedTemplateExpression,this.tag=e,this.quasi=t}}(),t.TemplateElement=function(){return function(e,t){this.type=n.Syntax.TemplateElement,this.value=e,this.tail=t}}(),t.TemplateLiteral=function(){return function(e,t){this.type=n.Syntax.TemplateLiteral,this.quasis=e,this.expressions=t}}(),t.ThisExpression=function(){return function(){this.type=n.Syntax.ThisExpression}}(),t.ThrowStatement=function(){return function(e){this.type=n.Syntax.ThrowStatement,this.argument=e}}(),t.TryStatement=function(){return function(e,t,i){this.type=n.Syntax.TryStatement,this.block=e,this.handler=t,this.finalizer=i}}(),t.UnaryExpression=function(){return function(e,t){this.type=n.Syntax.UnaryExpression,this.operator=e,this.argument=t,this.prefix=!0}}(),t.UpdateExpression=function(){return function(e,t,i){this.type=n.Syntax.UpdateExpression,this.operator=e,this.argument=t,this.prefix=i}}(),t.VariableDeclaration=function(){return function(e,t){this.type=n.Syntax.VariableDeclaration,this.declarations=e,this.kind=t}}(),t.VariableDeclarator=function(){return function(e,t){this.type=n.Syntax.VariableDeclarator,this.id=e,this.init=t}}(),t.WhileStatement=function(){return function(e,t){this.type=n.Syntax.WhileStatement,this.test=e,this.body=t}}(),t.WithStatement=function(){return function(e,t){this.type=n.Syntax.WithStatement,this.object=e,this.body=t}}(),t.YieldExpression=function(){return function(e,t){this.type=n.Syntax.YieldExpression,this.argument=e,this.delegate=t}}()},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=i(9),r=i(10),s=i(11),a=i(7),o=i(12),u=i(2),c=i(13),h="ArrowParameterPlaceHolder";t.Parser=function(){function e(e,t,i){void 0===t&&(t={}),this.config={range:"boolean"==typeof t.range&&t.range,loc:"boolean"==typeof t.loc&&t.loc,source:null,tokens:"boolean"==typeof t.tokens&&t.tokens,comment:"boolean"==typeof t.comment&&t.comment,tolerant:"boolean"==typeof t.tolerant&&t.tolerant},this.config.loc&&t.source&&null!==t.source&&(this.config.source=String(t.source)),this.delegate=i,this.errorHandler=new r.ErrorHandler,this.errorHandler.tolerant=this.config.tolerant,this.scanner=new o.Scanner(e,this.errorHandler),this.scanner.trackComment=this.config.comment,this.operatorPrecedence={")":0,";":0,",":0,"=":0,"]":0,"||":1,"&&":2,"|":3,"^":4,"&":5,"==":6,"!=":6,"===":6,"!==":6,"<":7,">":7,"<=":7,">=":7,"<<":8,">>":8,">>>":8,"+":9,"-":9,"*":11,"/":11,"%":11},this.lookahead={type:2,value:"",lineNumber:this.scanner.lineNumber,lineStart:0,start:0,end:0},this.hasLineTerminator=!1,this.context={isModule:!1,await:!1,allowIn:!0,allowStrictDirective:!0,allowYield:!0,firstCoverInitializedNameError:null,isAssignmentTarget:!1,isBindingElement:!1,inFunctionBody:!1,inIteration:!1,inSwitch:!1,labelSet:{},strict:!1},this.tokens=[],this.startMarker={index:0,line:this.scanner.lineNumber,column:0},this.lastMarker={index:0,line:this.scanner.lineNumber,column:0},this.nextToken(),this.lastMarker={index:this.scanner.index,line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}}return e.prototype.throwError=function(e){for(var t=[],i=1;i<arguments.length;i++)t[i-1]=arguments[i];var r=Array.prototype.slice.call(arguments,1),s=e.replace(/%(\d)/g,function(e,t){return n.assert(t<r.length,"Message reference must be in range"),r[t]}),a=this.lastMarker.index,o=this.lastMarker.line,u=this.lastMarker.column+1;throw this.errorHandler.createError(a,o,u,s)},e.prototype.tolerateError=function(e){for(var t=[],i=1;i<arguments.length;i++)t[i-1]=arguments[i];var r=Array.prototype.slice.call(arguments,1),s=e.replace(/%(\d)/g,function(e,t){return n.assert(t<r.length,"Message reference must be in range"),r[t]}),a=this.lastMarker.index,o=this.scanner.lineNumber,u=this.lastMarker.column+1;this.errorHandler.tolerateError(a,o,u,s)},e.prototype.unexpectedTokenError=function(e,t){var i,n=t||s.Messages.UnexpectedToken;if(e?(!t&&(n=2===e.type?s.Messages.UnexpectedEOS:3===e.type?s.Messages.UnexpectedIdentifier:6===e.type?s.Messages.UnexpectedNumber:8===e.type?s.Messages.UnexpectedString:10===e.type?s.Messages.UnexpectedTemplate:s.Messages.UnexpectedToken,4===e.type&&(this.scanner.isFutureReservedWord(e.value)?n=s.Messages.UnexpectedReserved:this.context.strict&&this.scanner.isStrictModeReservedWord(e.value)&&(n=s.Messages.StrictReservedWord))),i=e.value):i="ILLEGAL",n=n.replace("%0",i),e&&"number"==typeof e.lineNumber){var r=e.start,a=e.lineNumber,o=this.lastMarker.index-this.lastMarker.column,u=e.start-o+1;return this.errorHandler.createError(r,a,u,n)}var r=this.lastMarker.index,a=this.lastMarker.line,u=this.lastMarker.column+1;return this.errorHandler.createError(r,a,u,n)},e.prototype.throwUnexpectedToken=function(e,t){throw this.unexpectedTokenError(e,t)},e.prototype.tolerateUnexpectedToken=function(e,t){this.errorHandler.tolerate(this.unexpectedTokenError(e,t))},e.prototype.collectComments=function(){if(this.config.comment){var e=this.scanner.scanComments();if(e.length>0&&this.delegate)for(var t=0;t<e.length;++t){var i=e[t],n=void 0;n={type:i.multiLine?"BlockComment":"LineComment",value:this.scanner.source.slice(i.slice[0],i.slice[1])},this.config.range&&(n.range=i.range),this.config.loc&&(n.loc=i.loc);var r={start:{line:i.loc.start.line,column:i.loc.start.column,offset:i.range[0]},end:{line:i.loc.end.line,column:i.loc.end.column,offset:i.range[1]}};this.delegate(n,r)}}else this.scanner.scanComments()},e.prototype.getTokenRaw=function(e){return this.scanner.source.slice(e.start,e.end)},e.prototype.convertToken=function(e){var t={type:c.TokenName[e.type],value:this.getTokenRaw(e)};return this.config.range&&(t.range=[e.start,e.end]),this.config.loc&&(t.loc={start:{line:this.startMarker.line,column:this.startMarker.column},end:{line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}}),9===e.type&&(t.regex={pattern:e.pattern,flags:e.flags}),t},e.prototype.nextToken=function(){var e=this.lookahead;this.lastMarker.index=this.scanner.index,this.lastMarker.line=this.scanner.lineNumber,this.lastMarker.column=this.scanner.index-this.scanner.lineStart,this.collectComments(),this.scanner.index!==this.startMarker.index&&(this.startMarker.index=this.scanner.index,this.startMarker.line=this.scanner.lineNumber,this.startMarker.column=this.scanner.index-this.scanner.lineStart);var t=this.scanner.lex();return this.hasLineTerminator=e.lineNumber!==t.lineNumber,t&&this.context.strict&&3===t.type&&this.scanner.isStrictModeReservedWord(t.value)&&(t.type=4),this.lookahead=t,this.config.tokens&&2!==t.type&&this.tokens.push(this.convertToken(t)),e},e.prototype.nextRegexToken=function(){this.collectComments();var e=this.scanner.scanRegExp();return this.config.tokens&&(this.tokens.pop(),this.tokens.push(this.convertToken(e))),this.lookahead=e,this.nextToken(),e},e.prototype.createNode=function(){return{index:this.startMarker.index,line:this.startMarker.line,column:this.startMarker.column}},e.prototype.startNode=function(e,t){void 0===t&&(t=0);var i=e.start-e.lineStart,n=e.lineNumber;return i<0&&(i+=t,n--),{index:e.start,line:n,column:i}},e.prototype.finalize=function(e,t){if(this.config.range&&(t.range=[e.index,this.lastMarker.index]),this.config.loc&&(t.loc={start:{line:e.line,column:e.column},end:{line:this.lastMarker.line,column:this.lastMarker.column}},this.config.source&&(t.loc.source=this.config.source)),this.delegate){var i={start:{line:e.line,column:e.column,offset:e.index},end:{line:this.lastMarker.line,column:this.lastMarker.column,offset:this.lastMarker.index}};this.delegate(t,i)}return t},e.prototype.expect=function(e){var t=this.nextToken();(7!==t.type||t.value!==e)&&this.throwUnexpectedToken(t)},e.prototype.expectCommaSeparator=function(){if(this.config.tolerant){var e=this.lookahead;7===e.type&&","===e.value?this.nextToken():7===e.type&&";"===e.value?(this.nextToken(),this.tolerateUnexpectedToken(e)):this.tolerateUnexpectedToken(e,s.Messages.UnexpectedToken)}else this.expect(",")},e.prototype.expectKeyword=function(e){var t=this.nextToken();(4!==t.type||t.value!==e)&&this.throwUnexpectedToken(t)},e.prototype.match=function(e){return 7===this.lookahead.type&&this.lookahead.value===e},e.prototype.matchKeyword=function(e){return 4===this.lookahead.type&&this.lookahead.value===e},e.prototype.matchContextualKeyword=function(e){return 3===this.lookahead.type&&this.lookahead.value===e},e.prototype.matchAssign=function(){if(7!==this.lookahead.type)return!1;var e=this.lookahead.value;return"="===e||"*="===e||"**="===e||"/="===e||"%="===e||"+="===e||"-="===e||"<<="===e||">>="===e||">>>="===e||"&="===e||"^="===e||"|="===e},e.prototype.isolateCoverGrammar=function(e){var t=this.context.isBindingElement,i=this.context.isAssignmentTarget,n=this.context.firstCoverInitializedNameError;this.context.isBindingElement=!0,this.context.isAssignmentTarget=!0,this.context.firstCoverInitializedNameError=null;var r=e.call(this);return null!==this.context.firstCoverInitializedNameError&&this.throwUnexpectedToken(this.context.firstCoverInitializedNameError),this.context.isBindingElement=t,this.context.isAssignmentTarget=i,this.context.firstCoverInitializedNameError=n,r},e.prototype.inheritCoverGrammar=function(e){var t=this.context.isBindingElement,i=this.context.isAssignmentTarget,n=this.context.firstCoverInitializedNameError;this.context.isBindingElement=!0,this.context.isAssignmentTarget=!0,this.context.firstCoverInitializedNameError=null;var r=e.call(this);return this.context.isBindingElement=this.context.isBindingElement&&t,this.context.isAssignmentTarget=this.context.isAssignmentTarget&&i,this.context.firstCoverInitializedNameError=n||this.context.firstCoverInitializedNameError,r},e.prototype.consumeSemicolon=function(){this.match(";")?this.nextToken():this.hasLineTerminator||(2===this.lookahead.type||this.match("}")||this.throwUnexpectedToken(this.lookahead),this.lastMarker.index=this.startMarker.index,this.lastMarker.line=this.startMarker.line,this.lastMarker.column=this.startMarker.column)},e.prototype.parsePrimaryExpression=function(){var e,t,i,n=this.createNode();switch(this.lookahead.type){case 3:(this.context.isModule||this.context.await)&&"await"===this.lookahead.value&&this.tolerateUnexpectedToken(this.lookahead),e=this.matchAsyncFunction()?this.parseFunctionExpression():this.finalize(n,new a.Identifier(this.nextToken().value));break;case 6:case 8:this.context.strict&&this.lookahead.octal&&this.tolerateUnexpectedToken(this.lookahead,s.Messages.StrictOctalLiteral),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,t=this.nextToken(),i=this.getTokenRaw(t),e=this.finalize(n,new a.Literal(t.value,i));break;case 1:this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,t=this.nextToken(),i=this.getTokenRaw(t),e=this.finalize(n,new a.Literal("true"===t.value,i));break;case 5:this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,t=this.nextToken(),i=this.getTokenRaw(t),e=this.finalize(n,new a.Literal(null,i));break;case 10:e=this.parseTemplateLiteral();break;case 7:switch(this.lookahead.value){case"(":this.context.isBindingElement=!1,e=this.inheritCoverGrammar(this.parseGroupExpression);break;case"[":e=this.inheritCoverGrammar(this.parseArrayInitializer);break;case"{":e=this.inheritCoverGrammar(this.parseObjectInitializer);break;case"/":case"/=":this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,this.scanner.index=this.startMarker.index,t=this.nextRegexToken(),i=this.getTokenRaw(t),e=this.finalize(n,new a.RegexLiteral(t.regex,i,t.pattern,t.flags));break;default:e=this.throwUnexpectedToken(this.nextToken())}break;case 4:!this.context.strict&&this.context.allowYield&&this.matchKeyword("yield")?e=this.parseIdentifierName():!this.context.strict&&this.matchKeyword("let")?e=this.finalize(n,new a.Identifier(this.nextToken().value)):(this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,this.matchKeyword("function")?e=this.parseFunctionExpression():this.matchKeyword("this")?(this.nextToken(),e=this.finalize(n,new a.ThisExpression)):e=this.matchKeyword("class")?this.parseClassExpression():this.throwUnexpectedToken(this.nextToken()));break;default:e=this.throwUnexpectedToken(this.nextToken())}return e},e.prototype.parseSpreadElement=function(){var e=this.createNode();this.expect("...");var t=this.inheritCoverGrammar(this.parseAssignmentExpression);return this.finalize(e,new a.SpreadElement(t))},e.prototype.parseArrayInitializer=function(){var e=this.createNode(),t=[];for(this.expect("[");!this.match("]");)if(this.match(","))this.nextToken(),t.push(null);else if(this.match("...")){var i=this.parseSpreadElement();this.match("]")||(this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,this.expect(",")),t.push(i)}else t.push(this.inheritCoverGrammar(this.parseAssignmentExpression)),this.match("]")||this.expect(",");return this.expect("]"),this.finalize(e,new a.ArrayExpression(t))},e.prototype.parsePropertyMethod=function(e){this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var t=this.context.strict,i=this.context.allowStrictDirective;this.context.allowStrictDirective=e.simple;var n=this.isolateCoverGrammar(this.parseFunctionSourceElements);return this.context.strict&&e.firstRestricted&&this.tolerateUnexpectedToken(e.firstRestricted,e.message),this.context.strict&&e.stricted&&this.tolerateUnexpectedToken(e.stricted,e.message),this.context.strict=t,this.context.allowStrictDirective=i,n},e.prototype.parsePropertyMethodFunction=function(){var e=!1,t=this.createNode(),i=this.context.allowYield;this.context.allowYield=!0;var n=this.parseFormalParameters(),r=this.parsePropertyMethod(n);return this.context.allowYield=i,this.finalize(t,new a.FunctionExpression(null,n.params,r,e))},e.prototype.parsePropertyMethodAsyncFunction=function(){var e=this.createNode(),t=this.context.allowYield,i=this.context.await;this.context.allowYield=!1,this.context.await=!0;var n=this.parseFormalParameters(),r=this.parsePropertyMethod(n);return this.context.allowYield=t,this.context.await=i,this.finalize(e,new a.AsyncFunctionExpression(null,n.params,r))},e.prototype.parseObjectPropertyKey=function(){var e,t=this.createNode(),i=this.nextToken();switch(i.type){case 8:case 6:this.context.strict&&i.octal&&this.tolerateUnexpectedToken(i,s.Messages.StrictOctalLiteral);var n=this.getTokenRaw(i);e=this.finalize(t,new a.Literal(i.value,n));break;case 3:case 1:case 5:case 4:e=this.finalize(t,new a.Identifier(i.value));break;case 7:"["===i.value?(e=this.isolateCoverGrammar(this.parseAssignmentExpression),this.expect("]")):e=this.throwUnexpectedToken(i);break;default:e=this.throwUnexpectedToken(i)}return e},e.prototype.isPropertyKey=function(e,t){return e.type===u.Syntax.Identifier&&e.name===t||e.type===u.Syntax.Literal&&e.value===t},e.prototype.parseObjectProperty=function(e){var t,i=this.createNode(),n=this.lookahead,r=null,o=null,u=!1,c=!1,h=!1,l=!1;if(3===n.type){var p=n.value;this.nextToken(),u=this.match("["),r=(l=!this.hasLineTerminator&&"async"===p&&!this.match(":")&&!this.match("(")&&!this.match("*")&&!this.match(","))?this.parseObjectPropertyKey():this.finalize(i,new a.Identifier(p))}else this.match("*")?this.nextToken():(u=this.match("["),r=this.parseObjectPropertyKey());var d=this.qualifiedPropertyName(this.lookahead);if(3===n.type&&!l&&"get"===n.value&&d)t="get",u=this.match("["),r=this.parseObjectPropertyKey(),this.context.allowYield=!1,o=this.parseGetterMethod();else if(3===n.type&&!l&&"set"===n.value&&d)t="set",u=this.match("["),r=this.parseObjectPropertyKey(),o=this.parseSetterMethod();else if(7===n.type&&"*"===n.value&&d)t="init",u=this.match("["),r=this.parseObjectPropertyKey(),o=this.parseGeneratorMethod(),c=!0;else if(r||this.throwUnexpectedToken(this.lookahead),t="init",this.match(":")&&!l)!u&&this.isPropertyKey(r,"__proto__")&&(e.value&&this.tolerateError(s.Messages.DuplicateProtoProperty),e.value=!0),this.nextToken(),o=this.inheritCoverGrammar(this.parseAssignmentExpression);else if(this.match("("))o=l?this.parsePropertyMethodAsyncFunction():this.parsePropertyMethodFunction(),c=!0;else if(3===n.type){var p=this.finalize(i,new a.Identifier(n.value));if(this.match("=")){this.context.firstCoverInitializedNameError=this.lookahead,this.nextToken(),h=!0;var f=this.isolateCoverGrammar(this.parseAssignmentExpression);o=this.finalize(i,new a.AssignmentPattern(p,f))}else h=!0,o=p}else this.throwUnexpectedToken(this.nextToken());return this.finalize(i,new a.Property(t,r,u,o,c,h))},e.prototype.parseObjectInitializer=function(){var e=this.createNode();this.expect("{");for(var t=[],i={value:!1};!this.match("}");)t.push(this.parseObjectProperty(i)),this.match("}")||this.expectCommaSeparator();return this.expect("}"),this.finalize(e,new a.ObjectExpression(t))},e.prototype.parseTemplateHead=function(){n.assert(this.lookahead.head,"Template literal must start with a template head");var e=this.createNode(),t=this.nextToken(),i=t.value,r=t.cooked;return this.finalize(e,new a.TemplateElement({raw:i,cooked:r},t.tail))},e.prototype.parseTemplateElement=function(){10!==this.lookahead.type&&this.throwUnexpectedToken();var e=this.createNode(),t=this.nextToken(),i=t.value,n=t.cooked;return this.finalize(e,new a.TemplateElement({raw:i,cooked:n},t.tail))},e.prototype.parseTemplateLiteral=function(){var e=this.createNode(),t=[],i=[],n=this.parseTemplateHead();for(i.push(n);!n.tail;)t.push(this.parseExpression()),n=this.parseTemplateElement(),i.push(n);return this.finalize(e,new a.TemplateLiteral(i,t))},e.prototype.reinterpretExpressionAsPattern=function(e){switch(e.type){case u.Syntax.Identifier:case u.Syntax.MemberExpression:case u.Syntax.RestElement:case u.Syntax.AssignmentPattern:break;case u.Syntax.SpreadElement:e.type=u.Syntax.RestElement,this.reinterpretExpressionAsPattern(e.argument);break;case u.Syntax.ArrayExpression:e.type=u.Syntax.ArrayPattern;for(var t=0;t<e.elements.length;t++)null!==e.elements[t]&&this.reinterpretExpressionAsPattern(e.elements[t]);break;case u.Syntax.ObjectExpression:e.type=u.Syntax.ObjectPattern;for(var t=0;t<e.properties.length;t++)this.reinterpretExpressionAsPattern(e.properties[t].value);break;case u.Syntax.AssignmentExpression:e.type=u.Syntax.AssignmentPattern,delete e.operator,this.reinterpretExpressionAsPattern(e.left)}},e.prototype.parseGroupExpression=function(){var e;if(this.expect("("),this.match(")"))this.nextToken(),this.match("=>")||this.expect("=>"),e={type:h,params:[],async:!1};else{var t=this.lookahead,i=[];if(this.match("..."))e=this.parseRestElement(i),this.expect(")"),this.match("=>")||this.expect("=>"),e={type:h,params:[e],async:!1};else{var n=!1;if(this.context.isBindingElement=!0,e=this.inheritCoverGrammar(this.parseAssignmentExpression),this.match(",")){var r=[];for(this.context.isAssignmentTarget=!1,r.push(e);2!==this.lookahead.type&&this.match(",");){if(this.nextToken(),this.match(")")){this.nextToken();for(var s=0;s<r.length;s++)this.reinterpretExpressionAsPattern(r[s]);n=!0,e={type:h,params:r,async:!1}}else if(this.match("...")){this.context.isBindingElement||this.throwUnexpectedToken(this.lookahead),r.push(this.parseRestElement(i)),this.expect(")"),this.match("=>")||this.expect("=>"),this.context.isBindingElement=!1;for(var s=0;s<r.length;s++)this.reinterpretExpressionAsPattern(r[s]);n=!0,e={type:h,params:r,async:!1}}else r.push(this.inheritCoverGrammar(this.parseAssignmentExpression));if(n)break}n||(e=this.finalize(this.startNode(t),new a.SequenceExpression(r)))}if(!n){if(this.expect(")"),this.match("=>")&&(e.type===u.Syntax.Identifier&&"yield"===e.name&&(n=!0,e={type:h,params:[e],async:!1}),!n)){if(this.context.isBindingElement||this.throwUnexpectedToken(this.lookahead),e.type===u.Syntax.SequenceExpression)for(var s=0;s<e.expressions.length;s++)this.reinterpretExpressionAsPattern(e.expressions[s]);else this.reinterpretExpressionAsPattern(e);e={type:h,params:e.type===u.Syntax.SequenceExpression?e.expressions:[e],async:!1}}this.context.isBindingElement=!1}}}return e},e.prototype.parseArguments=function(){this.expect("(");var e=[];if(!this.match(")"))for(;;){var t=this.match("...")?this.parseSpreadElement():this.isolateCoverGrammar(this.parseAssignmentExpression);if(e.push(t),this.match(")")||(this.expectCommaSeparator(),this.match(")")))break}return this.expect(")"),e},e.prototype.isIdentifierName=function(e){return 3===e.type||4===e.type||1===e.type||5===e.type},e.prototype.parseIdentifierName=function(){var e=this.createNode(),t=this.nextToken();return this.isIdentifierName(t)||this.throwUnexpectedToken(t),this.finalize(e,new a.Identifier(t.value))},e.prototype.parseNewExpression=function(){var e,t=this.createNode(),i=this.parseIdentifierName();if(n.assert("new"===i.name,"New expression must start with `new`"),this.match("."))if(this.nextToken(),3===this.lookahead.type&&this.context.inFunctionBody&&"target"===this.lookahead.value){var r=this.parseIdentifierName();e=new a.MetaProperty(i,r)}else this.throwUnexpectedToken(this.lookahead);else{var s=this.isolateCoverGrammar(this.parseLeftHandSideExpression),o=this.match("(")?this.parseArguments():[];e=new a.NewExpression(s,o),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}return this.finalize(t,e)},e.prototype.parseAsyncArgument=function(){var e=this.parseAssignmentExpression();return this.context.firstCoverInitializedNameError=null,e},e.prototype.parseAsyncArguments=function(){this.expect("(");var e=[];if(!this.match(")"))for(;;){var t=this.match("...")?this.parseSpreadElement():this.isolateCoverGrammar(this.parseAsyncArgument);if(e.push(t),this.match(")")||(this.expectCommaSeparator(),this.match(")")))break}return this.expect(")"),e},e.prototype.parseLeftHandSideExpressionAllowCall=function(){var e,t=this.lookahead,i=this.matchContextualKeyword("async"),n=this.context.allowIn;for(this.context.allowIn=!0,this.matchKeyword("super")&&this.context.inFunctionBody?(e=this.createNode(),this.nextToken(),e=this.finalize(e,new a.Super),this.match("(")||this.match(".")||this.match("[")||this.throwUnexpectedToken(this.lookahead)):e=this.inheritCoverGrammar(this.matchKeyword("new")?this.parseNewExpression:this.parsePrimaryExpression);;)if(this.match(".")){this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect(".");var r=this.parseIdentifierName();e=this.finalize(this.startNode(t),new a.StaticMemberExpression(e,r))}else if(this.match("(")){var s=i&&t.lineNumber===this.lookahead.lineNumber;this.context.isBindingElement=!1,this.context.isAssignmentTarget=!1;var o=s?this.parseAsyncArguments():this.parseArguments();if(e=this.finalize(this.startNode(t),new a.CallExpression(e,o)),s&&this.match("=>")){for(var u=0;u<o.length;++u)this.reinterpretExpressionAsPattern(o[u]);e={type:h,params:o,async:!0}}}else if(this.match("[")){this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect("[");var r=this.isolateCoverGrammar(this.parseExpression);this.expect("]"),e=this.finalize(this.startNode(t),new a.ComputedMemberExpression(e,r))}else if(10===this.lookahead.type&&this.lookahead.head){var c=this.parseTemplateLiteral();e=this.finalize(this.startNode(t),new a.TaggedTemplateExpression(e,c))}else break;return this.context.allowIn=n,e},e.prototype.parseSuper=function(){var e=this.createNode();return this.expectKeyword("super"),this.match("[")||this.match(".")||this.throwUnexpectedToken(this.lookahead),this.finalize(e,new a.Super)},e.prototype.parseLeftHandSideExpression=function(){n.assert(this.context.allowIn,"callee of new expression always allow in keyword.");for(var e=this.startNode(this.lookahead),t=this.matchKeyword("super")&&this.context.inFunctionBody?this.parseSuper():this.inheritCoverGrammar(this.matchKeyword("new")?this.parseNewExpression:this.parsePrimaryExpression);;)if(this.match("[")){this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect("[");var i=this.isolateCoverGrammar(this.parseExpression);this.expect("]"),t=this.finalize(e,new a.ComputedMemberExpression(t,i))}else if(this.match(".")){this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect(".");var i=this.parseIdentifierName();t=this.finalize(e,new a.StaticMemberExpression(t,i))}else if(10===this.lookahead.type&&this.lookahead.head){var r=this.parseTemplateLiteral();t=this.finalize(e,new a.TaggedTemplateExpression(t,r))}else break;return t},e.prototype.parseUpdateExpression=function(){var e,t=this.lookahead;if(this.match("++")||this.match("--")){var i=this.startNode(t),n=this.nextToken();e=this.inheritCoverGrammar(this.parseUnaryExpression),this.context.strict&&e.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(e.name)&&this.tolerateError(s.Messages.StrictLHSPrefix),this.context.isAssignmentTarget||this.tolerateError(s.Messages.InvalidLHSInAssignment);var r=!0;e=this.finalize(i,new a.UpdateExpression(n.value,e,r)),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}else if(e=this.inheritCoverGrammar(this.parseLeftHandSideExpressionAllowCall),!this.hasLineTerminator&&7===this.lookahead.type&&(this.match("++")||this.match("--"))){this.context.strict&&e.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(e.name)&&this.tolerateError(s.Messages.StrictLHSPostfix),this.context.isAssignmentTarget||this.tolerateError(s.Messages.InvalidLHSInAssignment),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var o=this.nextToken().value,r=!1;e=this.finalize(this.startNode(t),new a.UpdateExpression(o,e,r))}return e},e.prototype.parseAwaitExpression=function(){var e=this.createNode();this.nextToken();var t=this.parseUnaryExpression();return this.finalize(e,new a.AwaitExpression(t))},e.prototype.parseUnaryExpression=function(){var e;if(this.match("+")||this.match("-")||this.match("~")||this.match("!")||this.matchKeyword("delete")||this.matchKeyword("void")||this.matchKeyword("typeof")){var t=this.startNode(this.lookahead),i=this.nextToken();e=this.inheritCoverGrammar(this.parseUnaryExpression),e=this.finalize(t,new a.UnaryExpression(i.value,e)),this.context.strict&&"delete"===e.operator&&e.argument.type===u.Syntax.Identifier&&this.tolerateError(s.Messages.StrictDelete),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}else e=this.context.await&&this.matchContextualKeyword("await")?this.parseAwaitExpression():this.parseUpdateExpression();return e},e.prototype.parseExponentiationExpression=function(){var e=this.lookahead,t=this.inheritCoverGrammar(this.parseUnaryExpression);if(t.type!==u.Syntax.UnaryExpression&&this.match("**")){this.nextToken(),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var i=t,n=this.isolateCoverGrammar(this.parseExponentiationExpression);t=this.finalize(this.startNode(e),new a.BinaryExpression("**",i,n))}return t},e.prototype.binaryPrecedence=function(e){var t,i=e.value;return 7===e.type?this.operatorPrecedence[i]||0:4===e.type&&("instanceof"===i||this.context.allowIn&&"in"===i)?7:0},e.prototype.parseBinaryExpression=function(){var e=this.lookahead,t=this.inheritCoverGrammar(this.parseExponentiationExpression),i=this.lookahead,n=this.binaryPrecedence(i);if(n>0){this.nextToken(),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;for(var r=[e,this.lookahead],s=t,o=this.isolateCoverGrammar(this.parseExponentiationExpression),u=[s,i.value,o],c=[n];!((n=this.binaryPrecedence(this.lookahead))<=0);){for(;u.length>2&&n<=c[c.length-1];){o=u.pop();var h=u.pop();c.pop(),s=u.pop(),r.pop();var l=this.startNode(r[r.length-1]);u.push(this.finalize(l,new a.BinaryExpression(h,s,o)))}u.push(this.nextToken().value),c.push(n),r.push(this.lookahead),u.push(this.isolateCoverGrammar(this.parseExponentiationExpression))}var p=u.length-1;t=u[p];for(var d=r.pop();p>1;){var f=r.pop(),m=d&&d.lineStart,l=this.startNode(f,m),h=u[p-1];t=this.finalize(l,new a.BinaryExpression(h,u[p-2],t)),p-=2,d=f}}return t},e.prototype.parseConditionalExpression=function(){var e=this.lookahead,t=this.inheritCoverGrammar(this.parseBinaryExpression);if(this.match("?")){this.nextToken();var i=this.context.allowIn;this.context.allowIn=!0;var n=this.isolateCoverGrammar(this.parseAssignmentExpression);this.context.allowIn=i,this.expect(":");var r=this.isolateCoverGrammar(this.parseAssignmentExpression);t=this.finalize(this.startNode(e),new a.ConditionalExpression(t,n,r)),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}return t},e.prototype.checkPatternParam=function(e,t){switch(t.type){case u.Syntax.Identifier:this.validateParam(e,t,t.name);break;case u.Syntax.RestElement:this.checkPatternParam(e,t.argument);break;case u.Syntax.AssignmentPattern:this.checkPatternParam(e,t.left);break;case u.Syntax.ArrayPattern:for(var i=0;i<t.elements.length;i++)null!==t.elements[i]&&this.checkPatternParam(e,t.elements[i]);break;case u.Syntax.ObjectPattern:for(var i=0;i<t.properties.length;i++)this.checkPatternParam(e,t.properties[i].value)}e.simple=e.simple&&t instanceof a.Identifier},e.prototype.reinterpretAsCoverFormalsList=function(e){var t,i=[e],n=!1;switch(e.type){case u.Syntax.Identifier:break;case h:i=e.params,n=e.async;break;default:return null}t={simple:!0,paramSet:{}};for(var r=0;r<i.length;++r){var a=i[r];a.type===u.Syntax.AssignmentPattern?a.right.type===u.Syntax.YieldExpression&&(a.right.argument&&this.throwUnexpectedToken(this.lookahead),a.right.type=u.Syntax.Identifier,a.right.name="yield",delete a.right.argument,delete a.right.delegate):n&&a.type===u.Syntax.Identifier&&"await"===a.name&&this.throwUnexpectedToken(this.lookahead),this.checkPatternParam(t,a),i[r]=a}if(this.context.strict||!this.context.allowYield)for(var r=0;r<i.length;++r){var a=i[r];a.type===u.Syntax.YieldExpression&&this.throwUnexpectedToken(this.lookahead)}if(t.message===s.Messages.StrictParamDupe){var o=this.context.strict?t.stricted:t.firstRestricted;this.throwUnexpectedToken(o,t.message)}return{simple:t.simple,params:i,stricted:t.stricted,firstRestricted:t.firstRestricted,message:t.message}},e.prototype.parseAssignmentExpression=function(){var e;if(!this.context.allowYield&&this.matchKeyword("yield"))e=this.parseYieldExpression();else{var t=this.lookahead,i=t;if(e=this.parseConditionalExpression(),3===i.type&&i.lineNumber===this.lookahead.lineNumber&&"async"===i.value&&(3===this.lookahead.type||this.matchKeyword("yield"))){var n=this.parsePrimaryExpression();this.reinterpretExpressionAsPattern(n),e={type:h,params:[n],async:!0}}if(e.type===h||this.match("=>")){this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var r=e.async,o=this.reinterpretAsCoverFormalsList(e);if(o){this.hasLineTerminator&&this.tolerateUnexpectedToken(this.lookahead),this.context.firstCoverInitializedNameError=null;var c=this.context.strict,l=this.context.allowStrictDirective;this.context.allowStrictDirective=o.simple;var p=this.context.allowYield,d=this.context.await;this.context.allowYield=!0,this.context.await=r;var f=this.startNode(t);this.expect("=>");var m=void 0;if(this.match("{")){var x=this.context.allowIn;this.context.allowIn=!0,m=this.parseFunctionSourceElements(),this.context.allowIn=x}else m=this.isolateCoverGrammar(this.parseAssignmentExpression);var D=m.type!==u.Syntax.BlockStatement;this.context.strict&&o.firstRestricted&&this.throwUnexpectedToken(o.firstRestricted,o.message),this.context.strict&&o.stricted&&this.tolerateUnexpectedToken(o.stricted,o.message),e=r?this.finalize(f,new a.AsyncArrowFunctionExpression(o.params,m,D)):this.finalize(f,new a.ArrowFunctionExpression(o.params,m,D)),this.context.strict=c,this.context.allowStrictDirective=l,this.context.allowYield=p,this.context.await=d}}else if(this.matchAssign()){if(this.context.isAssignmentTarget||this.tolerateError(s.Messages.InvalidLHSInAssignment),this.context.strict&&e.type===u.Syntax.Identifier){var y=e;this.scanner.isRestrictedWord(y.name)&&this.tolerateUnexpectedToken(i,s.Messages.StrictLHSAssignment),this.scanner.isStrictModeReservedWord(y.name)&&this.tolerateUnexpectedToken(i,s.Messages.StrictReservedWord)}this.match("=")?this.reinterpretExpressionAsPattern(e):(this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1);var g=(i=this.nextToken()).value,E=this.isolateCoverGrammar(this.parseAssignmentExpression);e=this.finalize(this.startNode(t),new a.AssignmentExpression(g,e,E)),this.context.firstCoverInitializedNameError=null}}return e},e.prototype.parseExpression=function(){var e=this.lookahead,t=this.isolateCoverGrammar(this.parseAssignmentExpression);if(this.match(",")){var i=[];for(i.push(t);2!==this.lookahead.type&&this.match(",");)this.nextToken(),i.push(this.isolateCoverGrammar(this.parseAssignmentExpression));t=this.finalize(this.startNode(e),new a.SequenceExpression(i))}return t},e.prototype.parseStatementListItem=function(){var e;if(this.context.isAssignmentTarget=!0,this.context.isBindingElement=!0,4===this.lookahead.type)switch(this.lookahead.value){case"export":this.context.isModule||this.tolerateUnexpectedToken(this.lookahead,s.Messages.IllegalExportDeclaration),e=this.parseExportDeclaration();break;case"import":this.context.isModule||this.tolerateUnexpectedToken(this.lookahead,s.Messages.IllegalImportDeclaration),e=this.parseImportDeclaration();break;case"const":e=this.parseLexicalDeclaration({inFor:!1});break;case"function":e=this.parseFunctionDeclaration();break;case"class":e=this.parseClassDeclaration();break;case"let":e=this.isLexicalDeclaration()?this.parseLexicalDeclaration({inFor:!1}):this.parseStatement();break;default:e=this.parseStatement()}else e=this.parseStatement();return e},e.prototype.parseBlock=function(){var e=this.createNode();this.expect("{");for(var t=[];!this.match("}");)t.push(this.parseStatementListItem());return this.expect("}"),this.finalize(e,new a.BlockStatement(t))},e.prototype.parseLexicalBinding=function(e,t){var i=this.createNode(),n=[],r=this.parsePattern(n,e);this.context.strict&&r.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(r.name)&&this.tolerateError(s.Messages.StrictVarName);var o=null;return"const"===e?this.matchKeyword("in")||this.matchContextualKeyword("of")||(this.match("=")?(this.nextToken(),o=this.isolateCoverGrammar(this.parseAssignmentExpression)):this.throwError(s.Messages.DeclarationMissingInitializer,"const")):(!t.inFor&&r.type!==u.Syntax.Identifier||this.match("="))&&(this.expect("="),o=this.isolateCoverGrammar(this.parseAssignmentExpression)),this.finalize(i,new a.VariableDeclarator(r,o))},e.prototype.parseBindingList=function(e,t){for(var i=[this.parseLexicalBinding(e,t)];this.match(",");)this.nextToken(),i.push(this.parseLexicalBinding(e,t));return i},e.prototype.isLexicalDeclaration=function(){var e=this.scanner.saveState();this.scanner.scanComments();var t=this.scanner.lex();return this.scanner.restoreState(e),3===t.type||7===t.type&&"["===t.value||7===t.type&&"{"===t.value||4===t.type&&"let"===t.value||4===t.type&&"yield"===t.value},e.prototype.parseLexicalDeclaration=function(e){var t=this.createNode(),i=this.nextToken().value;n.assert("let"===i||"const"===i,"Lexical declaration must be either let or const");var r=this.parseBindingList(i,e);return this.consumeSemicolon(),this.finalize(t,new a.VariableDeclaration(r,i))},e.prototype.parseBindingRestElement=function(e,t){var i=this.createNode();this.expect("...");var n=this.parsePattern(e,t);return this.finalize(i,new a.RestElement(n))},e.prototype.parseArrayPattern=function(e,t){var i=this.createNode();this.expect("[");for(var n=[];!this.match("]");)if(this.match(","))this.nextToken(),n.push(null);else{if(this.match("...")){n.push(this.parseBindingRestElement(e,t));break}n.push(this.parsePatternWithDefault(e,t)),this.match("]")||this.expect(",")}return this.expect("]"),this.finalize(i,new a.ArrayPattern(n))},e.prototype.parsePropertyPattern=function(e,t){var i,n,r=this.createNode(),s=!1,o=!1,u=!1;if(3===this.lookahead.type){var c=this.lookahead;i=this.parseVariableIdentifier();var h=this.finalize(r,new a.Identifier(c.value));if(this.match("=")){e.push(c),o=!0,this.nextToken();var l=this.parseAssignmentExpression();n=this.finalize(this.startNode(c),new a.AssignmentPattern(h,l))}else this.match(":")?(this.expect(":"),n=this.parsePatternWithDefault(e,t)):(e.push(c),o=!0,n=h)}else s=this.match("["),i=this.parseObjectPropertyKey(),this.expect(":"),n=this.parsePatternWithDefault(e,t);return this.finalize(r,new a.Property("init",i,s,n,u,o))},e.prototype.parseObjectPattern=function(e,t){var i=this.createNode(),n=[];for(this.expect("{");!this.match("}");)n.push(this.parsePropertyPattern(e,t)),this.match("}")||this.expect(",");return this.expect("}"),this.finalize(i,new a.ObjectPattern(n))},e.prototype.parsePattern=function(e,t){var i;return this.match("[")?i=this.parseArrayPattern(e,t):this.match("{")?i=this.parseObjectPattern(e,t):(this.matchKeyword("let")&&("const"===t||"let"===t)&&this.tolerateUnexpectedToken(this.lookahead,s.Messages.LetInLexicalBinding),e.push(this.lookahead),i=this.parseVariableIdentifier(t)),i},e.prototype.parsePatternWithDefault=function(e,t){var i=this.lookahead,n=this.parsePattern(e,t);if(this.match("=")){this.nextToken();var r=this.context.allowYield;this.context.allowYield=!0;var s=this.isolateCoverGrammar(this.parseAssignmentExpression);this.context.allowYield=r,n=this.finalize(this.startNode(i),new a.AssignmentPattern(n,s))}return n},e.prototype.parseVariableIdentifier=function(e){var t=this.createNode(),i=this.nextToken();return 4===i.type&&"yield"===i.value?this.context.strict?this.tolerateUnexpectedToken(i,s.Messages.StrictReservedWord):this.context.allowYield||this.throwUnexpectedToken(i):3!==i.type?this.context.strict&&4===i.type&&this.scanner.isStrictModeReservedWord(i.value)?this.tolerateUnexpectedToken(i,s.Messages.StrictReservedWord):(this.context.strict||"let"!==i.value||"var"!==e)&&this.throwUnexpectedToken(i):(this.context.isModule||this.context.await)&&3===i.type&&"await"===i.value&&this.tolerateUnexpectedToken(i),this.finalize(t,new a.Identifier(i.value))},e.prototype.parseVariableDeclaration=function(e){var t=this.createNode(),i=[],n=this.parsePattern(i,"var");this.context.strict&&n.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(n.name)&&this.tolerateError(s.Messages.StrictVarName);var r=null;return this.match("=")?(this.nextToken(),r=this.isolateCoverGrammar(this.parseAssignmentExpression)):n.type===u.Syntax.Identifier||e.inFor||this.expect("="),this.finalize(t,new a.VariableDeclarator(n,r))},e.prototype.parseVariableDeclarationList=function(e){var t={inFor:e.inFor},i=[];for(i.push(this.parseVariableDeclaration(t));this.match(",");)this.nextToken(),i.push(this.parseVariableDeclaration(t));return i},e.prototype.parseVariableStatement=function(){var e=this.createNode();this.expectKeyword("var");var t=this.parseVariableDeclarationList({inFor:!1});return this.consumeSemicolon(),this.finalize(e,new a.VariableDeclaration(t,"var"))},e.prototype.parseEmptyStatement=function(){var e=this.createNode();return this.expect(";"),this.finalize(e,new a.EmptyStatement)},e.prototype.parseExpressionStatement=function(){var e=this.createNode(),t=this.parseExpression();return this.consumeSemicolon(),this.finalize(e,new a.ExpressionStatement(t))},e.prototype.parseIfClause=function(){return this.context.strict&&this.matchKeyword("function")&&this.tolerateError(s.Messages.StrictFunction),this.parseStatement()},e.prototype.parseIfStatement=function(){var e,t=this.createNode(),i=null;this.expectKeyword("if"),this.expect("(");var n=this.parseExpression();return!this.match(")")&&this.config.tolerant?(this.tolerateUnexpectedToken(this.nextToken()),e=this.finalize(this.createNode(),new a.EmptyStatement)):(this.expect(")"),e=this.parseIfClause(),this.matchKeyword("else")&&(this.nextToken(),i=this.parseIfClause())),this.finalize(t,new a.IfStatement(n,e,i))},e.prototype.parseDoWhileStatement=function(){var e=this.createNode();this.expectKeyword("do");var t=this.context.inIteration;this.context.inIteration=!0;var i=this.parseStatement();this.context.inIteration=t,this.expectKeyword("while"),this.expect("(");var n=this.parseExpression();return!this.match(")")&&this.config.tolerant?this.tolerateUnexpectedToken(this.nextToken()):(this.expect(")"),this.match(";")&&this.nextToken()),this.finalize(e,new a.DoWhileStatement(i,n))},e.prototype.parseWhileStatement=function(){var e,t=this.createNode();this.expectKeyword("while"),this.expect("(");var i=this.parseExpression();if(!this.match(")")&&this.config.tolerant)this.tolerateUnexpectedToken(this.nextToken()),e=this.finalize(this.createNode(),new a.EmptyStatement);else{this.expect(")");var n=this.context.inIteration;this.context.inIteration=!0,e=this.parseStatement(),this.context.inIteration=n}return this.finalize(t,new a.WhileStatement(i,e))},e.prototype.parseForStatement=function(){var e,t,i,n=null,r=null,o=null,c=!0,h=this.createNode();if(this.expectKeyword("for"),this.expect("("),this.match(";"))this.nextToken();else if(this.matchKeyword("var")){n=this.createNode(),this.nextToken();var l=this.context.allowIn;this.context.allowIn=!1;var p=this.parseVariableDeclarationList({inFor:!0});if(this.context.allowIn=l,1===p.length&&this.matchKeyword("in")){var d=p[0];d.init&&(d.id.type===u.Syntax.ArrayPattern||d.id.type===u.Syntax.ObjectPattern||this.context.strict)&&this.tolerateError(s.Messages.ForInOfLoopInitializer,"for-in"),n=this.finalize(n,new a.VariableDeclaration(p,"var")),this.nextToken(),e=n,t=this.parseExpression(),n=null}else 1===p.length&&null===p[0].init&&this.matchContextualKeyword("of")?(n=this.finalize(n,new a.VariableDeclaration(p,"var")),this.nextToken(),e=n,t=this.parseAssignmentExpression(),n=null,c=!1):(n=this.finalize(n,new a.VariableDeclaration(p,"var")),this.expect(";"))}else if(this.matchKeyword("const")||this.matchKeyword("let")){n=this.createNode();var f=this.nextToken().value;if(this.context.strict||"in"!==this.lookahead.value){var l=this.context.allowIn;this.context.allowIn=!1;var p=this.parseBindingList(f,{inFor:!0});this.context.allowIn=l,1===p.length&&null===p[0].init&&this.matchKeyword("in")?(n=this.finalize(n,new a.VariableDeclaration(p,f)),this.nextToken(),e=n,t=this.parseExpression(),n=null):1===p.length&&null===p[0].init&&this.matchContextualKeyword("of")?(n=this.finalize(n,new a.VariableDeclaration(p,f)),this.nextToken(),e=n,t=this.parseAssignmentExpression(),n=null,c=!1):(this.consumeSemicolon(),n=this.finalize(n,new a.VariableDeclaration(p,f)))}else n=this.finalize(n,new a.Identifier(f)),this.nextToken(),e=n,t=this.parseExpression(),n=null}else{var m=this.lookahead,l=this.context.allowIn;if(this.context.allowIn=!1,n=this.inheritCoverGrammar(this.parseAssignmentExpression),this.context.allowIn=l,this.matchKeyword("in"))this.context.isAssignmentTarget&&n.type!==u.Syntax.AssignmentExpression||this.tolerateError(s.Messages.InvalidLHSInForIn),this.nextToken(),this.reinterpretExpressionAsPattern(n),e=n,t=this.parseExpression(),n=null;else if(this.matchContextualKeyword("of"))this.context.isAssignmentTarget&&n.type!==u.Syntax.AssignmentExpression||this.tolerateError(s.Messages.InvalidLHSInForLoop),this.nextToken(),this.reinterpretExpressionAsPattern(n),e=n,t=this.parseAssignmentExpression(),n=null,c=!1;else{if(this.match(",")){for(var x=[n];this.match(",");)this.nextToken(),x.push(this.isolateCoverGrammar(this.parseAssignmentExpression));n=this.finalize(this.startNode(m),new a.SequenceExpression(x))}this.expect(";")}}if(void 0===e&&(this.match(";")||(r=this.parseExpression()),this.expect(";"),this.match(")")||(o=this.parseExpression())),!this.match(")")&&this.config.tolerant)this.tolerateUnexpectedToken(this.nextToken()),i=this.finalize(this.createNode(),new a.EmptyStatement);else{this.expect(")");var D=this.context.inIteration;this.context.inIteration=!0,i=this.isolateCoverGrammar(this.parseStatement),this.context.inIteration=D}return void 0===e?this.finalize(h,new a.ForStatement(n,r,o,i)):c?this.finalize(h,new a.ForInStatement(e,t,i)):this.finalize(h,new a.ForOfStatement(e,t,i))},e.prototype.parseContinueStatement=function(){var e=this.createNode();this.expectKeyword("continue");var t=null;if(3===this.lookahead.type&&!this.hasLineTerminator){var i=this.parseVariableIdentifier();t=i;var n="$"+i.name;Object.prototype.hasOwnProperty.call(this.context.labelSet,n)||this.throwError(s.Messages.UnknownLabel,i.name)}return this.consumeSemicolon(),null!==t||this.context.inIteration||this.throwError(s.Messages.IllegalContinue),this.finalize(e,new a.ContinueStatement(t))},e.prototype.parseBreakStatement=function(){var e=this.createNode();this.expectKeyword("break");var t=null;if(3===this.lookahead.type&&!this.hasLineTerminator){var i=this.parseVariableIdentifier(),n="$"+i.name;Object.prototype.hasOwnProperty.call(this.context.labelSet,n)||this.throwError(s.Messages.UnknownLabel,i.name),t=i}return this.consumeSemicolon(),null!==t||this.context.inIteration||this.context.inSwitch||this.throwError(s.Messages.IllegalBreak),this.finalize(e,new a.BreakStatement(t))},e.prototype.parseReturnStatement=function(){this.context.inFunctionBody||this.tolerateError(s.Messages.IllegalReturn);var e=this.createNode();this.expectKeyword("return");var t=(this.match(";")||this.match("}")||this.hasLineTerminator||2===this.lookahead.type)&&8!==this.lookahead.type&&10!==this.lookahead.type?null:this.parseExpression();return this.consumeSemicolon(),this.finalize(e,new a.ReturnStatement(t))},e.prototype.parseWithStatement=function(){this.context.strict&&this.tolerateError(s.Messages.StrictModeWith);var e,t=this.createNode();this.expectKeyword("with"),this.expect("(");var i=this.parseExpression();return!this.match(")")&&this.config.tolerant?(this.tolerateUnexpectedToken(this.nextToken()),e=this.finalize(this.createNode(),new a.EmptyStatement)):(this.expect(")"),e=this.parseStatement()),this.finalize(t,new a.WithStatement(i,e))},e.prototype.parseSwitchCase=function(){var e,t=this.createNode();this.matchKeyword("default")?(this.nextToken(),e=null):(this.expectKeyword("case"),e=this.parseExpression()),this.expect(":");for(var i=[];!(this.match("}")||this.matchKeyword("default")||this.matchKeyword("case"));)i.push(this.parseStatementListItem());return this.finalize(t,new a.SwitchCase(e,i))},e.prototype.parseSwitchStatement=function(){var e=this.createNode();this.expectKeyword("switch"),this.expect("(");var t=this.parseExpression();this.expect(")");var i=this.context.inSwitch;this.context.inSwitch=!0;var n=[],r=!1;for(this.expect("{");!this.match("}");){var o=this.parseSwitchCase();null===o.test&&(r&&this.throwError(s.Messages.MultipleDefaultsInSwitch),r=!0),n.push(o)}return this.expect("}"),this.context.inSwitch=i,this.finalize(e,new a.SwitchStatement(t,n))},e.prototype.parseLabelledStatement=function(){var e,t=this.createNode(),i=this.parseExpression();if(i.type===u.Syntax.Identifier&&this.match(":")){this.nextToken();var n=i,r="$"+n.name;Object.prototype.hasOwnProperty.call(this.context.labelSet,r)&&this.throwError(s.Messages.Redeclaration,"Label",n.name),this.context.labelSet[r]=!0;var o=void 0;if(this.matchKeyword("class"))this.tolerateUnexpectedToken(this.lookahead),o=this.parseClassDeclaration();else if(this.matchKeyword("function")){var c=this.lookahead,h=this.parseFunctionDeclaration();this.context.strict?this.tolerateUnexpectedToken(c,s.Messages.StrictFunction):h.generator&&this.tolerateUnexpectedToken(c,s.Messages.GeneratorInLegacyContext),o=h}else o=this.parseStatement();delete this.context.labelSet[r],e=new a.LabeledStatement(n,o)}else this.consumeSemicolon(),e=new a.ExpressionStatement(i);return this.finalize(t,e)},e.prototype.parseThrowStatement=function(){var e=this.createNode();this.expectKeyword("throw"),this.hasLineTerminator&&this.throwError(s.Messages.NewlineAfterThrow);var t=this.parseExpression();return this.consumeSemicolon(),this.finalize(e,new a.ThrowStatement(t))},e.prototype.parseCatchClause=function(){var e=this.createNode();this.expectKeyword("catch"),this.expect("("),this.match(")")&&this.throwUnexpectedToken(this.lookahead);for(var t=[],i=this.parsePattern(t),n={},r=0;r<t.length;r++){var o="$"+t[r].value;Object.prototype.hasOwnProperty.call(n,o)&&this.tolerateError(s.Messages.DuplicateBinding,t[r].value),n[o]=!0}this.context.strict&&i.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(i.name)&&this.tolerateError(s.Messages.StrictCatchVariable),this.expect(")");var c=this.parseBlock();return this.finalize(e,new a.CatchClause(i,c))},e.prototype.parseFinallyClause=function(){return this.expectKeyword("finally"),this.parseBlock()},e.prototype.parseTryStatement=function(){var e=this.createNode();this.expectKeyword("try");var t=this.parseBlock(),i=this.matchKeyword("catch")?this.parseCatchClause():null,n=this.matchKeyword("finally")?this.parseFinallyClause():null;return i||n||this.throwError(s.Messages.NoCatchOrFinally),this.finalize(e,new a.TryStatement(t,i,n))},e.prototype.parseDebuggerStatement=function(){var e=this.createNode();return this.expectKeyword("debugger"),this.consumeSemicolon(),this.finalize(e,new a.DebuggerStatement)},e.prototype.parseStatement=function(){var e;switch(this.lookahead.type){case 1:case 5:case 6:case 8:case 10:case 9:e=this.parseExpressionStatement();break;case 7:var t=this.lookahead.value;e="{"===t?this.parseBlock():"("===t?this.parseExpressionStatement():";"===t?this.parseEmptyStatement():this.parseExpressionStatement();break;case 3:e=this.matchAsyncFunction()?this.parseFunctionDeclaration():this.parseLabelledStatement();break;case 4:switch(this.lookahead.value){case"break":e=this.parseBreakStatement();break;case"continue":e=this.parseContinueStatement();break;case"debugger":e=this.parseDebuggerStatement();break;case"do":e=this.parseDoWhileStatement();break;case"for":e=this.parseForStatement();break;case"function":e=this.parseFunctionDeclaration();break;case"if":e=this.parseIfStatement();break;case"return":e=this.parseReturnStatement();break;case"switch":e=this.parseSwitchStatement();break;case"throw":e=this.parseThrowStatement();break;case"try":e=this.parseTryStatement();break;case"var":e=this.parseVariableStatement();break;case"while":e=this.parseWhileStatement();break;case"with":e=this.parseWithStatement();break;default:e=this.parseExpressionStatement()}break;default:e=this.throwUnexpectedToken(this.lookahead)}return e},e.prototype.parseFunctionSourceElements=function(){var e=this.createNode();this.expect("{");var t=this.parseDirectivePrologues(),i=this.context.labelSet,n=this.context.inIteration,r=this.context.inSwitch,s=this.context.inFunctionBody;for(this.context.labelSet={},this.context.inIteration=!1,this.context.inSwitch=!1,this.context.inFunctionBody=!0;2!==this.lookahead.type&&!this.match("}");)t.push(this.parseStatementListItem());return this.expect("}"),this.context.labelSet=i,this.context.inIteration=n,this.context.inSwitch=r,this.context.inFunctionBody=s,this.finalize(e,new a.BlockStatement(t))},e.prototype.validateParam=function(e,t,i){var n="$"+i;this.context.strict?(this.scanner.isRestrictedWord(i)&&(e.stricted=t,e.message=s.Messages.StrictParamName),Object.prototype.hasOwnProperty.call(e.paramSet,n)&&(e.stricted=t,e.message=s.Messages.StrictParamDupe)):!e.firstRestricted&&(this.scanner.isRestrictedWord(i)?(e.firstRestricted=t,e.message=s.Messages.StrictParamName):this.scanner.isStrictModeReservedWord(i)?(e.firstRestricted=t,e.message=s.Messages.StrictReservedWord):Object.prototype.hasOwnProperty.call(e.paramSet,n)&&(e.stricted=t,e.message=s.Messages.StrictParamDupe)),"function"==typeof Object.defineProperty?Object.defineProperty(e.paramSet,n,{value:!0,enumerable:!0,writable:!0,configurable:!0}):e.paramSet[n]=!0},e.prototype.parseRestElement=function(e){var t=this.createNode();this.expect("...");var i=this.parsePattern(e);return this.match("=")&&this.throwError(s.Messages.DefaultRestParameter),this.match(")")||this.throwError(s.Messages.ParameterAfterRestParameter),this.finalize(t,new a.RestElement(i))},e.prototype.parseFormalParameter=function(e){for(var t=[],i=this.match("...")?this.parseRestElement(t):this.parsePatternWithDefault(t),n=0;n<t.length;n++)this.validateParam(e,t[n],t[n].value);e.simple=e.simple&&i instanceof a.Identifier,e.params.push(i)},e.prototype.parseFormalParameters=function(e){var t;if(t={simple:!0,params:[],firstRestricted:e},this.expect("("),!this.match(")"))for(t.paramSet={};2!==this.lookahead.type&&(this.parseFormalParameter(t),!this.match(")"))&&(this.expect(","),!this.match(")")););return this.expect(")"),{simple:t.simple,params:t.params,stricted:t.stricted,firstRestricted:t.firstRestricted,message:t.message}},e.prototype.matchAsyncFunction=function(){var e=this.matchContextualKeyword("async");if(e){var t=this.scanner.saveState();this.scanner.scanComments();var i=this.scanner.lex();this.scanner.restoreState(t),e=t.lineNumber===i.lineNumber&&4===i.type&&"function"===i.value}return e},e.prototype.parseFunctionDeclaration=function(e){var t,i=this.createNode(),n=this.matchContextualKeyword("async");n&&this.nextToken(),this.expectKeyword("function");var r=!n&&this.match("*");r&&this.nextToken();var o=null,u=null;if(!e||!this.match("(")){var c=this.lookahead;o=this.parseVariableIdentifier(),this.context.strict?this.scanner.isRestrictedWord(c.value)&&this.tolerateUnexpectedToken(c,s.Messages.StrictFunctionName):this.scanner.isRestrictedWord(c.value)?(u=c,t=s.Messages.StrictFunctionName):this.scanner.isStrictModeReservedWord(c.value)&&(u=c,t=s.Messages.StrictReservedWord)}var h=this.context.await,l=this.context.allowYield;this.context.await=n,this.context.allowYield=!r;var p=this.parseFormalParameters(u),d=p.params,f=p.stricted;u=p.firstRestricted,p.message&&(t=p.message);var m=this.context.strict,x=this.context.allowStrictDirective;this.context.allowStrictDirective=p.simple;var D=this.parseFunctionSourceElements();return this.context.strict&&u&&this.throwUnexpectedToken(u,t),this.context.strict&&f&&this.tolerateUnexpectedToken(f,t),this.context.strict=m,this.context.allowStrictDirective=x,this.context.await=h,this.context.allowYield=l,n?this.finalize(i,new a.AsyncFunctionDeclaration(o,d,D)):this.finalize(i,new a.FunctionDeclaration(o,d,D,r))},e.prototype.parseFunctionExpression=function(){var e,t,i=this.createNode(),n=this.matchContextualKeyword("async");n&&this.nextToken(),this.expectKeyword("function");var r=!n&&this.match("*");r&&this.nextToken();var o=null,u=this.context.await,c=this.context.allowYield;if(this.context.await=n,this.context.allowYield=!r,!this.match("(")){var h=this.lookahead;o=!this.context.strict&&!r&&this.matchKeyword("yield")?this.parseIdentifierName():this.parseVariableIdentifier(),this.context.strict?this.scanner.isRestrictedWord(h.value)&&this.tolerateUnexpectedToken(h,s.Messages.StrictFunctionName):this.scanner.isRestrictedWord(h.value)?(t=h,e=s.Messages.StrictFunctionName):this.scanner.isStrictModeReservedWord(h.value)&&(t=h,e=s.Messages.StrictReservedWord)}var l=this.parseFormalParameters(t),p=l.params,d=l.stricted;t=l.firstRestricted,l.message&&(e=l.message);var f=this.context.strict,m=this.context.allowStrictDirective;this.context.allowStrictDirective=l.simple;var x=this.parseFunctionSourceElements();return this.context.strict&&t&&this.throwUnexpectedToken(t,e),this.context.strict&&d&&this.tolerateUnexpectedToken(d,e),this.context.strict=f,this.context.allowStrictDirective=m,this.context.await=u,this.context.allowYield=c,n?this.finalize(i,new a.AsyncFunctionExpression(o,p,x)):this.finalize(i,new a.FunctionExpression(o,p,x,r))},e.prototype.parseDirective=function(){var e=this.lookahead,t=this.createNode(),i=this.parseExpression(),n=i.type===u.Syntax.Literal?this.getTokenRaw(e).slice(1,-1):null;return this.consumeSemicolon(),this.finalize(t,n?new a.Directive(i,n):new a.ExpressionStatement(i))},e.prototype.parseDirectivePrologues=function(){for(var e=null,t=[];;){var i=this.lookahead;if(8!==i.type)break;var n=this.parseDirective();t.push(n);var r=n.directive;if("string"!=typeof r)break;"use strict"===r?(this.context.strict=!0,e&&this.tolerateUnexpectedToken(e,s.Messages.StrictOctalLiteral),this.context.allowStrictDirective||this.tolerateUnexpectedToken(i,s.Messages.IllegalLanguageModeDirective)):!e&&i.octal&&(e=i)}return t},e.prototype.qualifiedPropertyName=function(e){switch(e.type){case 3:case 8:case 1:case 5:case 6:case 4:return!0;case 7:return"["===e.value}return!1},e.prototype.parseGetterMethod=function(){var e=this.createNode(),t=!1,i=this.context.allowYield;this.context.allowYield=!t;var n=this.parseFormalParameters();n.params.length>0&&this.tolerateError(s.Messages.BadGetterArity);var r=this.parsePropertyMethod(n);return this.context.allowYield=i,this.finalize(e,new a.FunctionExpression(null,n.params,r,t))},e.prototype.parseSetterMethod=function(){var e=this.createNode(),t=!1,i=this.context.allowYield;this.context.allowYield=!t;var n=this.parseFormalParameters();1!==n.params.length?this.tolerateError(s.Messages.BadSetterArity):n.params[0]instanceof a.RestElement&&this.tolerateError(s.Messages.BadSetterRestParameter);var r=this.parsePropertyMethod(n);return this.context.allowYield=i,this.finalize(e,new a.FunctionExpression(null,n.params,r,t))},e.prototype.parseGeneratorMethod=function(){var e=this.createNode(),t=!0,i=this.context.allowYield;this.context.allowYield=!0;var n=this.parseFormalParameters();this.context.allowYield=!1;var r=this.parsePropertyMethod(n);return this.context.allowYield=i,this.finalize(e,new a.FunctionExpression(null,n.params,r,t))},e.prototype.isStartOfExpression=function(){var e=!0,t=this.lookahead.value;switch(this.lookahead.type){case 7:e="["===t||"("===t||"{"===t||"+"===t||"-"===t||"!"===t||"~"===t||"++"===t||"--"===t||"/"===t||"/="===t;break;case 4:e="class"===t||"delete"===t||"function"===t||"let"===t||"new"===t||"super"===t||"this"===t||"typeof"===t||"void"===t||"yield"===t}return e},e.prototype.parseYieldExpression=function(){var e=this.createNode();this.expectKeyword("yield");var t=null,i=!1;if(!this.hasLineTerminator){var n=this.context.allowYield;this.context.allowYield=!1,(i=this.match("*"))?(this.nextToken(),t=this.parseAssignmentExpression()):this.isStartOfExpression()&&(t=this.parseAssignmentExpression()),this.context.allowYield=n}return this.finalize(e,new a.YieldExpression(t,i))},e.prototype.parseClassElement=function(e){var t=this.lookahead,i=this.createNode(),n="",r=null,o=null,u=!1,c=!1,h=!1,l=!1;if(this.match("*"))this.nextToken();else if(u=this.match("["),"static"===(r=this.parseObjectPropertyKey()).name&&(this.qualifiedPropertyName(this.lookahead)||this.match("*"))&&(t=this.lookahead,h=!0,u=this.match("["),this.match("*")?this.nextToken():r=this.parseObjectPropertyKey()),3===t.type&&!this.hasLineTerminator&&"async"===t.value){var p=this.lookahead.value;":"!==p&&"("!==p&&"*"!==p&&(l=!0,t=this.lookahead,r=this.parseObjectPropertyKey(),3===t.type&&"constructor"===t.value&&this.tolerateUnexpectedToken(t,s.Messages.ConstructorIsAsync))}var d=this.qualifiedPropertyName(this.lookahead);return 3===t.type?"get"===t.value&&d?(n="get",u=this.match("["),r=this.parseObjectPropertyKey(),this.context.allowYield=!1,o=this.parseGetterMethod()):"set"===t.value&&d&&(n="set",u=this.match("["),r=this.parseObjectPropertyKey(),o=this.parseSetterMethod()):7===t.type&&"*"===t.value&&d&&(n="init",u=this.match("["),r=this.parseObjectPropertyKey(),o=this.parseGeneratorMethod(),c=!0),!n&&r&&this.match("(")&&(n="init",o=l?this.parsePropertyMethodAsyncFunction():this.parsePropertyMethodFunction(),c=!0),n||this.throwUnexpectedToken(this.lookahead),"init"===n&&(n="method"),!u&&(h&&this.isPropertyKey(r,"prototype")&&this.throwUnexpectedToken(t,s.Messages.StaticPrototype),!h&&this.isPropertyKey(r,"constructor")&&(("method"!==n||!c||o&&o.generator)&&this.throwUnexpectedToken(t,s.Messages.ConstructorSpecialMethod),e.value?this.throwUnexpectedToken(t,s.Messages.DuplicateConstructor):e.value=!0,n="constructor")),this.finalize(i,new a.MethodDefinition(r,u,o,n,h))},e.prototype.parseClassElementList=function(){var e=[],t={value:!1};for(this.expect("{");!this.match("}");)this.match(";")?this.nextToken():e.push(this.parseClassElement(t));return this.expect("}"),e},e.prototype.parseClassBody=function(){var e=this.createNode(),t=this.parseClassElementList();return this.finalize(e,new a.ClassBody(t))},e.prototype.parseClassDeclaration=function(e){var t=this.createNode(),i=this.context.strict;this.context.strict=!0,this.expectKeyword("class");var n=e&&3!==this.lookahead.type?null:this.parseVariableIdentifier(),r=null;this.matchKeyword("extends")&&(this.nextToken(),r=this.isolateCoverGrammar(this.parseLeftHandSideExpressionAllowCall));var s=this.parseClassBody();return this.context.strict=i,this.finalize(t,new a.ClassDeclaration(n,r,s))},e.prototype.parseClassExpression=function(){var e=this.createNode(),t=this.context.strict;this.context.strict=!0,this.expectKeyword("class");var i=3===this.lookahead.type?this.parseVariableIdentifier():null,n=null;this.matchKeyword("extends")&&(this.nextToken(),n=this.isolateCoverGrammar(this.parseLeftHandSideExpressionAllowCall));var r=this.parseClassBody();return this.context.strict=t,this.finalize(e,new a.ClassExpression(i,n,r))},e.prototype.parseModule=function(){this.context.strict=!0,this.context.isModule=!0,this.scanner.isModule=!0;for(var e=this.createNode(),t=this.parseDirectivePrologues();2!==this.lookahead.type;)t.push(this.parseStatementListItem());return this.finalize(e,new a.Module(t))},e.prototype.parseScript=function(){for(var e=this.createNode(),t=this.parseDirectivePrologues();2!==this.lookahead.type;)t.push(this.parseStatementListItem());return this.finalize(e,new a.Script(t))},e.prototype.parseModuleSpecifier=function(){var e=this.createNode();8!==this.lookahead.type&&this.throwError(s.Messages.InvalidModuleSpecifier);var t=this.nextToken(),i=this.getTokenRaw(t);return this.finalize(e,new a.Literal(t.value,i))},e.prototype.parseImportSpecifier=function(){var e,t,i=this.createNode();return 3===this.lookahead.type?(t=e=this.parseVariableIdentifier(),this.matchContextualKeyword("as")&&(this.nextToken(),t=this.parseVariableIdentifier())):(t=e=this.parseIdentifierName(),this.matchContextualKeyword("as")?(this.nextToken(),t=this.parseVariableIdentifier()):this.throwUnexpectedToken(this.nextToken())),this.finalize(i,new a.ImportSpecifier(t,e))},e.prototype.parseNamedImports=function(){this.expect("{");for(var e=[];!this.match("}");)e.push(this.parseImportSpecifier()),this.match("}")||this.expect(",");return this.expect("}"),e},e.prototype.parseImportDefaultSpecifier=function(){var e=this.createNode(),t=this.parseIdentifierName();return this.finalize(e,new a.ImportDefaultSpecifier(t))},e.prototype.parseImportNamespaceSpecifier=function(){var e=this.createNode();this.expect("*"),this.matchContextualKeyword("as")||this.throwError(s.Messages.NoAsAfterImportNamespace),this.nextToken();var t=this.parseIdentifierName();return this.finalize(e,new a.ImportNamespaceSpecifier(t))},e.prototype.parseImportDeclaration=function(){this.context.inFunctionBody&&this.throwError(s.Messages.IllegalImportDeclaration);var e,t=this.createNode();this.expectKeyword("import");var i=[];if(8===this.lookahead.type)e=this.parseModuleSpecifier();else{if(this.match("{")?i=i.concat(this.parseNamedImports()):this.match("*")?i.push(this.parseImportNamespaceSpecifier()):this.isIdentifierName(this.lookahead)&&!this.matchKeyword("default")?(i.push(this.parseImportDefaultSpecifier()),this.match(",")&&(this.nextToken(),this.match("*")?i.push(this.parseImportNamespaceSpecifier()):this.match("{")?i=i.concat(this.parseNamedImports()):this.throwUnexpectedToken(this.lookahead))):this.throwUnexpectedToken(this.nextToken()),!this.matchContextualKeyword("from")){var n=this.lookahead.value?s.Messages.UnexpectedToken:s.Messages.MissingFromClause;this.throwError(n,this.lookahead.value)}this.nextToken(),e=this.parseModuleSpecifier()}return this.consumeSemicolon(),this.finalize(t,new a.ImportDeclaration(i,e))},e.prototype.parseExportSpecifier=function(){var e=this.createNode(),t=this.parseIdentifierName(),i=t;return this.matchContextualKeyword("as")&&(this.nextToken(),i=this.parseIdentifierName()),this.finalize(e,new a.ExportSpecifier(t,i))},e.prototype.parseExportDeclaration=function(){this.context.inFunctionBody&&this.throwError(s.Messages.IllegalExportDeclaration);var e,t=this.createNode();if(this.expectKeyword("export"),this.matchKeyword("default"))if(this.nextToken(),this.matchKeyword("function")){var i=this.parseFunctionDeclaration(!0);e=this.finalize(t,new a.ExportDefaultDeclaration(i))}else if(this.matchKeyword("class")){var i=this.parseClassDeclaration(!0);e=this.finalize(t,new a.ExportDefaultDeclaration(i))}else if(this.matchContextualKeyword("async")){var i=this.matchAsyncFunction()?this.parseFunctionDeclaration(!0):this.parseAssignmentExpression();e=this.finalize(t,new a.ExportDefaultDeclaration(i))}else{this.matchContextualKeyword("from")&&this.throwError(s.Messages.UnexpectedToken,this.lookahead.value);var i=this.match("{")?this.parseObjectInitializer():this.match("[")?this.parseArrayInitializer():this.parseAssignmentExpression();this.consumeSemicolon(),e=this.finalize(t,new a.ExportDefaultDeclaration(i))}else if(this.match("*")){if(this.nextToken(),!this.matchContextualKeyword("from")){var n=this.lookahead.value?s.Messages.UnexpectedToken:s.Messages.MissingFromClause;this.throwError(n,this.lookahead.value)}this.nextToken();var r=this.parseModuleSpecifier();this.consumeSemicolon(),e=this.finalize(t,new a.ExportAllDeclaration(r))}else if(4===this.lookahead.type){var i=void 0;switch(this.lookahead.value){case"let":case"const":i=this.parseLexicalDeclaration({inFor:!1});break;case"var":case"class":case"function":i=this.parseStatementListItem();break;default:this.throwUnexpectedToken(this.lookahead)}e=this.finalize(t,new a.ExportNamedDeclaration(i,[],null))}else if(this.matchAsyncFunction()){var i=this.parseFunctionDeclaration();e=this.finalize(t,new a.ExportNamedDeclaration(i,[],null))}else{var o=[],u=null,c=!1;for(this.expect("{");!this.match("}");)c=c||this.matchKeyword("default"),o.push(this.parseExportSpecifier()),this.match("}")||this.expect(",");if(this.expect("}"),this.matchContextualKeyword("from"))this.nextToken(),u=this.parseModuleSpecifier(),this.consumeSemicolon();else if(c){var n=this.lookahead.value?s.Messages.UnexpectedToken:s.Messages.MissingFromClause;this.throwError(n,this.lookahead.value)}else this.consumeSemicolon();e=this.finalize(t,new a.ExportNamedDeclaration(null,o,u))}return e},e}()},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assert=function(e,t){if(!e)throw Error("ASSERT: "+t)}},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ErrorHandler=function(){function e(){this.errors=[],this.tolerant=!1}return e.prototype.recordError=function(e){this.errors.push(e)},e.prototype.tolerate=function(e){if(this.tolerant)this.recordError(e);else throw e},e.prototype.constructError=function(e,t){var i=Error(e);try{throw i}catch(e){Object.create&&Object.defineProperty&&Object.defineProperty(i=Object.create(e),"column",{value:t})}return i},e.prototype.createError=function(e,t,i,n){var r="Line "+t+": "+n,s=this.constructError(r,i);return s.index=e,s.lineNumber=t,s.description=n,s},e.prototype.throwError=function(e,t,i,n){throw this.createError(e,t,i,n)},e.prototype.tolerateError=function(e,t,i,n){var r=this.createError(e,t,i,n);if(this.tolerant)this.recordError(r);else throw r},e}()},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Messages={BadGetterArity:"Getter must not have any formal parameters",BadSetterArity:"Setter must have exactly one formal parameter",BadSetterRestParameter:"Setter function argument must not be a rest parameter",ConstructorIsAsync:"Class constructor may not be an async method",ConstructorSpecialMethod:"Class constructor may not be an accessor",DeclarationMissingInitializer:"Missing initializer in %0 declaration",DefaultRestParameter:"Unexpected token =",DuplicateBinding:"Duplicate binding %0",DuplicateConstructor:"A class may only have one constructor",DuplicateProtoProperty:"Duplicate __proto__ fields are not allowed in object literals",ForInOfLoopInitializer:"%0 loop variable declaration may not have an initializer",GeneratorInLegacyContext:"Generator declarations are not allowed in legacy contexts",IllegalBreak:"Illegal break statement",IllegalContinue:"Illegal continue statement",IllegalExportDeclaration:"Unexpected token",IllegalImportDeclaration:"Unexpected token",IllegalLanguageModeDirective:"Illegal 'use strict' directive in function with non-simple parameter list",IllegalReturn:"Illegal return statement",InvalidEscapedReservedWord:"Keyword must not contain escaped characters",InvalidHexEscapeSequence:"Invalid hexadecimal escape sequence",InvalidLHSInAssignment:"Invalid left-hand side in assignment",InvalidLHSInForIn:"Invalid left-hand side in for-in",InvalidLHSInForLoop:"Invalid left-hand side in for-loop",InvalidModuleSpecifier:"Unexpected token",InvalidRegExp:"Invalid regular expression",LetInLexicalBinding:"let is disallowed as a lexically bound name",MissingFromClause:"Unexpected token",MultipleDefaultsInSwitch:"More than one default clause in switch statement",NewlineAfterThrow:"Illegal newline after throw",NoAsAfterImportNamespace:"Unexpected token",NoCatchOrFinally:"Missing catch or finally after try",ParameterAfterRestParameter:"Rest parameter must be last formal parameter",Redeclaration:"%0 '%1' has already been declared",StaticPrototype:"Classes may not have static property named prototype",StrictCatchVariable:"Catch variable may not be eval or arguments in strict mode",StrictDelete:"Delete of an unqualified identifier in strict mode.",StrictFunction:"In strict mode code, functions can only be declared at top level or inside a block",StrictFunctionName:"Function name may not be eval or arguments in strict mode",StrictLHSAssignment:"Assignment to eval or arguments is not allowed in strict mode",StrictLHSPostfix:"Postfix increment/decrement may not have eval or arguments operand in strict mode",StrictLHSPrefix:"Prefix increment/decrement may not have eval or arguments operand in strict mode",StrictModeWith:"Strict mode code may not include a with statement",StrictOctalLiteral:"Octal literals are not allowed in strict mode.",StrictParamDupe:"Strict mode function may not have duplicate parameter names",StrictParamName:"Parameter name eval or arguments is not allowed in strict mode",StrictReservedWord:"Use of future reserved word in strict mode",StrictVarName:"Variable name may not be eval or arguments in strict mode",TemplateOctalLiteral:"Octal literals are not allowed in template strings.",UnexpectedEOS:"Unexpected end of input",UnexpectedIdentifier:"Unexpected identifier",UnexpectedNumber:"Unexpected number",UnexpectedReserved:"Unexpected reserved word",UnexpectedString:"Unexpected string",UnexpectedTemplate:"Unexpected quasi %0",UnexpectedToken:"Unexpected token %0",UnexpectedTokenIllegal:"Unexpected token ILLEGAL",UnknownLabel:"Undefined label '%0'",UnterminatedRegExp:"Invalid regular expression: missing /"}},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=i(9),r=i(4),s=i(11);function a(e){return"0123456789abcdef".indexOf(e.toLowerCase())}function o(e){return"01234567".indexOf(e)}t.Scanner=function(){function e(e,t){this.source=e,this.errorHandler=t,this.trackComment=!1,this.isModule=!1,this.length=e.length,this.index=0,this.lineNumber=+(e.length>0),this.lineStart=0,this.curlyStack=[]}return e.prototype.saveState=function(){return{index:this.index,lineNumber:this.lineNumber,lineStart:this.lineStart}},e.prototype.restoreState=function(e){this.index=e.index,this.lineNumber=e.lineNumber,this.lineStart=e.lineStart},e.prototype.eof=function(){return this.index>=this.length},e.prototype.throwUnexpectedToken=function(e){return void 0===e&&(e=s.Messages.UnexpectedTokenIllegal),this.errorHandler.throwError(this.index,this.lineNumber,this.index-this.lineStart+1,e)},e.prototype.tolerateUnexpectedToken=function(e){void 0===e&&(e=s.Messages.UnexpectedTokenIllegal),this.errorHandler.tolerateError(this.index,this.lineNumber,this.index-this.lineStart+1,e)},e.prototype.skipSingleLineComment=function(e){var t,i,n=[];for(this.trackComment&&(n=[],t=this.index-e,i={start:{line:this.lineNumber,column:this.index-this.lineStart-e},end:{}});!this.eof();){var s=this.source.charCodeAt(this.index);if(++this.index,r.Character.isLineTerminator(s)){if(this.trackComment){i.end={line:this.lineNumber,column:this.index-this.lineStart-1};var a={multiLine:!1,slice:[t+e,this.index-1],range:[t,this.index-1],loc:i};n.push(a)}return 13===s&&10===this.source.charCodeAt(this.index)&&++this.index,++this.lineNumber,this.lineStart=this.index,n}}if(this.trackComment){i.end={line:this.lineNumber,column:this.index-this.lineStart};var a={multiLine:!1,slice:[t+e,this.index],range:[t,this.index],loc:i};n.push(a)}return n},e.prototype.skipMultiLineComment=function(){var e,t,i=[];for(this.trackComment&&(i=[],e=this.index-2,t={start:{line:this.lineNumber,column:this.index-this.lineStart-2},end:{}});!this.eof();){var n=this.source.charCodeAt(this.index);if(r.Character.isLineTerminator(n))13===n&&10===this.source.charCodeAt(this.index+1)&&++this.index,++this.lineNumber,++this.index,this.lineStart=this.index;else if(42===n){if(47===this.source.charCodeAt(this.index+1)){if(this.index+=2,this.trackComment){t.end={line:this.lineNumber,column:this.index-this.lineStart};var s={multiLine:!0,slice:[e+2,this.index-2],range:[e,this.index],loc:t};i.push(s)}return i}++this.index}else++this.index}if(this.trackComment){t.end={line:this.lineNumber,column:this.index-this.lineStart};var s={multiLine:!0,slice:[e+2,this.index],range:[e,this.index],loc:t};i.push(s)}return this.tolerateUnexpectedToken(),i},e.prototype.scanComments=function(){this.trackComment&&(e=[]);for(var e,t=0===this.index;!this.eof();){var i=this.source.charCodeAt(this.index);if(r.Character.isWhiteSpace(i))++this.index;else if(r.Character.isLineTerminator(i))++this.index,13===i&&10===this.source.charCodeAt(this.index)&&++this.index,++this.lineNumber,this.lineStart=this.index,t=!0;else if(47===i)if(47===(i=this.source.charCodeAt(this.index+1))){this.index+=2;var n=this.skipSingleLineComment(2);this.trackComment&&(e=e.concat(n)),t=!0}else if(42===i){this.index+=2;var n=this.skipMultiLineComment();this.trackComment&&(e=e.concat(n))}else break;else if(t&&45===i)if(45===this.source.charCodeAt(this.index+1)&&62===this.source.charCodeAt(this.index+2)){this.index+=3;var n=this.skipSingleLineComment(3);this.trackComment&&(e=e.concat(n))}else break;else if(60!==i||this.isModule)break;else if("!--"===this.source.slice(this.index+1,this.index+4)){this.index+=4;var n=this.skipSingleLineComment(4);this.trackComment&&(e=e.concat(n))}else break}return e},e.prototype.isFutureReservedWord=function(e){switch(e){case"enum":case"export":case"import":case"super":return!0;default:return!1}},e.prototype.isStrictModeReservedWord=function(e){switch(e){case"implements":case"interface":case"package":case"private":case"protected":case"public":case"static":case"yield":case"let":return!0;default:return!1}},e.prototype.isRestrictedWord=function(e){return"eval"===e||"arguments"===e},e.prototype.isKeyword=function(e){switch(e.length){case 2:return"if"===e||"in"===e||"do"===e;case 3:return"var"===e||"for"===e||"new"===e||"try"===e||"let"===e;case 4:return"this"===e||"else"===e||"case"===e||"void"===e||"with"===e||"enum"===e;case 5:return"while"===e||"break"===e||"catch"===e||"throw"===e||"const"===e||"yield"===e||"class"===e||"super"===e;case 6:return"return"===e||"typeof"===e||"delete"===e||"switch"===e||"export"===e||"import"===e;case 7:return"default"===e||"finally"===e||"extends"===e;case 8:return"function"===e||"continue"===e||"debugger"===e;case 10:return"instanceof"===e;default:return!1}},e.prototype.codePointAt=function(e){var t=this.source.charCodeAt(e);if(t>=55296&&t<=56319){var i=this.source.charCodeAt(e+1);i>=56320&&i<=57343&&(t=(t-55296)*1024+i-56320+65536)}return t},e.prototype.scanHexEscape=function(e){for(var t="u"===e?4:2,i=0,n=0;n<t;++n)if(!(!this.eof()&&r.Character.isHexDigit(this.source.charCodeAt(this.index))))return null;else i=16*i+a(this.source[this.index++]);return String.fromCharCode(i)},e.prototype.scanUnicodeCodePointEscape=function(){var e=this.source[this.index],t=0;for("}"===e&&this.throwUnexpectedToken();!this.eof()&&(e=this.source[this.index++],r.Character.isHexDigit(e.charCodeAt(0)));)t=16*t+a(e);return(t>1114111||"}"!==e)&&this.throwUnexpectedToken(),r.Character.fromCodePoint(t)},e.prototype.getIdentifier=function(){for(var e=this.index++;!this.eof();){var t=this.source.charCodeAt(this.index);if(92===t||t>=55296&&t<57343)return this.index=e,this.getComplexIdentifier();if(r.Character.isIdentifierPart(t))++this.index;else break}return this.source.slice(e,this.index)},e.prototype.getComplexIdentifier=function(){var e,t=this.codePointAt(this.index),i=r.Character.fromCodePoint(t);for(this.index+=i.length,92===t&&(117!==this.source.charCodeAt(this.index)&&this.throwUnexpectedToken(),++this.index,"{"===this.source[this.index]?(++this.index,e=this.scanUnicodeCodePointEscape()):null!==(e=this.scanHexEscape("u"))&&"\\"!==e&&r.Character.isIdentifierStart(e.charCodeAt(0))||this.throwUnexpectedToken(),i=e);!this.eof()&&(t=this.codePointAt(this.index),r.Character.isIdentifierPart(t));)i+=e=r.Character.fromCodePoint(t),this.index+=e.length,92===t&&(i=i.substr(0,i.length-1),117!==this.source.charCodeAt(this.index)&&this.throwUnexpectedToken(),++this.index,"{"===this.source[this.index]?(++this.index,e=this.scanUnicodeCodePointEscape()):null!==(e=this.scanHexEscape("u"))&&"\\"!==e&&r.Character.isIdentifierPart(e.charCodeAt(0))||this.throwUnexpectedToken(),i+=e);return i},e.prototype.octalToDecimal=function(e){var t="0"!==e,i=o(e);return!this.eof()&&r.Character.isOctalDigit(this.source.charCodeAt(this.index))&&(t=!0,i=8*i+o(this.source[this.index++]),"0123".indexOf(e)>=0&&!this.eof()&&r.Character.isOctalDigit(this.source.charCodeAt(this.index))&&(i=8*i+o(this.source[this.index++]))),{code:i,octal:t}},e.prototype.scanIdentifier=function(){var e,t=this.index,i=92===this.source.charCodeAt(t)?this.getComplexIdentifier():this.getIdentifier();if(3!=(e=1===i.length?3:this.isKeyword(i)?4:"null"===i?5:"true"===i||"false"===i?1:3)&&t+i.length!==this.index){var n=this.index;this.index=t,this.tolerateUnexpectedToken(s.Messages.InvalidEscapedReservedWord),this.index=n}return{type:e,value:i,lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}},e.prototype.scanPunctuator=function(){var e=this.index,t=this.source[this.index];switch(t){case"(":case"{":"{"===t&&this.curlyStack.push("{"),++this.index;break;case".":++this.index,"."===this.source[this.index]&&"."===this.source[this.index+1]&&(this.index+=2,t="...");break;case"}":++this.index,this.curlyStack.pop();break;case")":case";":case",":case"[":case"]":case":":case"?":case"~":++this.index;break;default:">>>="===(t=this.source.substr(this.index,4))?this.index+=4:"==="===(t=t.substr(0,3))||"!=="===t||">>>"===t||"<<="===t||">>="===t||"**="===t?this.index+=3:"&&"===(t=t.substr(0,2))||"||"===t||"=="===t||"!="===t||"+="===t||"-="===t||"*="===t||"/="===t||"++"===t||"--"===t||"<<"===t||">>"===t||"&="===t||"|="===t||"^="===t||"%="===t||"<="===t||">="===t||"=>"===t||"**"===t?this.index+=2:(t=this.source[this.index],"<>=!+-*%&|^/".indexOf(t)>=0&&++this.index)}return this.index===e&&this.throwUnexpectedToken(),{type:7,value:t,lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},e.prototype.scanHexLiteral=function(e){for(var t="";!this.eof()&&r.Character.isHexDigit(this.source.charCodeAt(this.index));)t+=this.source[this.index++];return 0===t.length&&this.throwUnexpectedToken(),r.Character.isIdentifierStart(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(),{type:6,value:parseInt("0x"+t,16),lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},e.prototype.scanBinaryLiteral=function(e){for(var t,i="";!this.eof()&&("0"===(t=this.source[this.index])||"1"===t);)i+=this.source[this.index++];return 0===i.length&&this.throwUnexpectedToken(),!this.eof()&&(t=this.source.charCodeAt(this.index),(r.Character.isIdentifierStart(t)||r.Character.isDecimalDigit(t))&&this.throwUnexpectedToken()),{type:6,value:parseInt(i,2),lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},e.prototype.scanOctalLiteral=function(e,t){var i="",n=!1;for(r.Character.isOctalDigit(e.charCodeAt(0))?(n=!0,i="0"+this.source[this.index++]):++this.index;!this.eof()&&r.Character.isOctalDigit(this.source.charCodeAt(this.index));)i+=this.source[this.index++];return n||0!==i.length||this.throwUnexpectedToken(),(r.Character.isIdentifierStart(this.source.charCodeAt(this.index))||r.Character.isDecimalDigit(this.source.charCodeAt(this.index)))&&this.throwUnexpectedToken(),{type:6,value:parseInt(i,8),octal:n,lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}},e.prototype.isImplicitOctalLiteral=function(){for(var e=this.index+1;e<this.length;++e){var t=this.source[e];if("8"===t||"9"===t)return!1;if(!r.Character.isOctalDigit(t.charCodeAt(0)))break}return!0},e.prototype.scanNumericLiteral=function(){var e=this.index,t=this.source[e];n.assert(r.Character.isDecimalDigit(t.charCodeAt(0))||"."===t,"Numeric literal must start with a decimal digit or a decimal point");var i="";if("."!==t){if(i=this.source[this.index++],t=this.source[this.index],"0"===i){if("x"===t||"X"===t)return++this.index,this.scanHexLiteral(e);if("b"===t||"B"===t)return++this.index,this.scanBinaryLiteral(e);if("o"===t||"O"===t||t&&r.Character.isOctalDigit(t.charCodeAt(0))&&this.isImplicitOctalLiteral())return this.scanOctalLiteral(t,e)}for(;r.Character.isDecimalDigit(this.source.charCodeAt(this.index));)i+=this.source[this.index++];t=this.source[this.index]}if("."===t){for(i+=this.source[this.index++];r.Character.isDecimalDigit(this.source.charCodeAt(this.index));)i+=this.source[this.index++];t=this.source[this.index]}if("e"===t||"E"===t)if(i+=this.source[this.index++],("+"===(t=this.source[this.index])||"-"===t)&&(i+=this.source[this.index++]),r.Character.isDecimalDigit(this.source.charCodeAt(this.index)))for(;r.Character.isDecimalDigit(this.source.charCodeAt(this.index));)i+=this.source[this.index++];else this.throwUnexpectedToken();return r.Character.isIdentifierStart(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(),{type:6,value:parseFloat(i),lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},e.prototype.scanStringLiteral=function(){var e=this.index,t=this.source[e];n.assert("'"===t||'"'===t,"String literal must starts with a quote"),++this.index;for(var i=!1,a="";!this.eof();){var o=this.source[this.index++];if(o===t){t="";break}if("\\"===o)if((o=this.source[this.index++])&&r.Character.isLineTerminator(o.charCodeAt(0)))++this.lineNumber,"\r"===o&&"\n"===this.source[this.index]&&++this.index,this.lineStart=this.index;else switch(o){case"u":if("{"===this.source[this.index])++this.index,a+=this.scanUnicodeCodePointEscape();else{var u=this.scanHexEscape(o);null===u&&this.throwUnexpectedToken(),a+=u}break;case"x":var c=this.scanHexEscape(o);null===c&&this.throwUnexpectedToken(s.Messages.InvalidHexEscapeSequence),a+=c;break;case"n":a+="\n";break;case"r":a+="\r";break;case"t":a+="	";break;case"b":a+="\b";break;case"f":a+="\f";break;case"v":a+="\v";break;case"8":case"9":a+=o,this.tolerateUnexpectedToken();break;default:if(o&&r.Character.isOctalDigit(o.charCodeAt(0))){var h=this.octalToDecimal(o);i=h.octal||i,a+=String.fromCharCode(h.code)}else a+=o}else if(r.Character.isLineTerminator(o.charCodeAt(0)))break;else a+=o}return""!==t&&(this.index=e,this.throwUnexpectedToken()),{type:8,value:a,octal:i,lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},e.prototype.scanTemplate=function(){var e="",t=!1,i=this.index,n="`"===this.source[i],a=!1,o=2;for(++this.index;!this.eof();){var u=this.source[this.index++];if("`"===u){o=1,a=!0,t=!0;break}if("$"===u){if("{"===this.source[this.index]){this.curlyStack.push("${"),++this.index,t=!0;break}e+=u}else if("\\"===u)if(u=this.source[this.index++],r.Character.isLineTerminator(u.charCodeAt(0)))++this.lineNumber,"\r"===u&&"\n"===this.source[this.index]&&++this.index,this.lineStart=this.index;else switch(u){case"n":e+="\n";break;case"r":e+="\r";break;case"t":e+="	";break;case"u":if("{"===this.source[this.index])++this.index,e+=this.scanUnicodeCodePointEscape();else{var c=this.index,h=this.scanHexEscape(u);null!==h?e+=h:(this.index=c,e+=u)}break;case"x":var l=this.scanHexEscape(u);null===l&&this.throwUnexpectedToken(s.Messages.InvalidHexEscapeSequence),e+=l;break;case"b":e+="\b";break;case"f":e+="\f";break;case"v":e+="\v";break;default:"0"===u?(r.Character.isDecimalDigit(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(s.Messages.TemplateOctalLiteral),e+="\0"):r.Character.isOctalDigit(u.charCodeAt(0))?this.throwUnexpectedToken(s.Messages.TemplateOctalLiteral):e+=u}else r.Character.isLineTerminator(u.charCodeAt(0))?(++this.lineNumber,"\r"===u&&"\n"===this.source[this.index]&&++this.index,this.lineStart=this.index,e+="\n"):e+=u}return t||this.throwUnexpectedToken(),n||this.curlyStack.pop(),{type:10,value:this.source.slice(i+1,this.index-o),cooked:e,head:n,tail:a,lineNumber:this.lineNumber,lineStart:this.lineStart,start:i,end:this.index}},e.prototype.testRegExp=function(e,t){var i="￿",n=e,r=this;t.indexOf("u")>=0&&(n=n.replace(/\\u\{([0-9a-fA-F]+)\}|\\u([a-fA-F0-9]{4})/g,function(e,t,n){var a=parseInt(t||n,16);return(a>1114111&&r.throwUnexpectedToken(s.Messages.InvalidRegExp),a<=65535)?String.fromCharCode(a):i}).replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,i));try{RegExp(n)}catch(e){this.throwUnexpectedToken(s.Messages.InvalidRegExp)}try{return new RegExp(e,t)}catch(e){return null}},e.prototype.scanRegExpBody=function(){var e=this.source[this.index];n.assert("/"===e,"Regular expression literal must start with a slash");for(var t=this.source[this.index++],i=!1,a=!1;!this.eof();)if(t+=e=this.source[this.index++],"\\"===e)e=this.source[this.index++],r.Character.isLineTerminator(e.charCodeAt(0))&&this.throwUnexpectedToken(s.Messages.UnterminatedRegExp),t+=e;else if(r.Character.isLineTerminator(e.charCodeAt(0)))this.throwUnexpectedToken(s.Messages.UnterminatedRegExp);else if(i)"]"===e&&(i=!1);else if("/"===e){a=!0;break}else"["===e&&(i=!0);return a||this.throwUnexpectedToken(s.Messages.UnterminatedRegExp),t.substr(1,t.length-2)},e.prototype.scanRegExpFlags=function(){for(var e="",t="";!this.eof();){var i=this.source[this.index];if(!r.Character.isIdentifierPart(i.charCodeAt(0)))break;if(++this.index,"\\"!==i||this.eof())t+=i,e+=i;else if("u"===(i=this.source[this.index])){++this.index;var n=this.index,s=this.scanHexEscape("u");if(null!==s)for(t+=s,e+="\\u";n<this.index;++n)e+=this.source[n];else this.index=n,t+="u",e+="\\u";this.tolerateUnexpectedToken()}else e+="\\",this.tolerateUnexpectedToken()}return t},e.prototype.scanRegExp=function(){var e=this.index,t=this.scanRegExpBody(),i=this.scanRegExpFlags(),n=this.testRegExp(t,i);return{type:9,value:"",pattern:t,flags:i,regex:n,lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},e.prototype.lex=function(){if(this.eof())return{type:2,value:"",lineNumber:this.lineNumber,lineStart:this.lineStart,start:this.index,end:this.index};var e=this.source.charCodeAt(this.index);return r.Character.isIdentifierStart(e)?this.scanIdentifier():40===e||41===e||59===e?this.scanPunctuator():39===e||34===e?this.scanStringLiteral():46===e?r.Character.isDecimalDigit(this.source.charCodeAt(this.index+1))?this.scanNumericLiteral():this.scanPunctuator():r.Character.isDecimalDigit(e)?this.scanNumericLiteral():96===e||125===e&&"${"===this.curlyStack[this.curlyStack.length-1]?this.scanTemplate():e>=55296&&e<57343&&r.Character.isIdentifierStart(this.codePointAt(this.index))?this.scanIdentifier():this.scanPunctuator()},e}()},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TokenName={},t.TokenName[1]="Boolean",t.TokenName[2]="<end>",t.TokenName[3]="Identifier",t.TokenName[4]="Keyword",t.TokenName[5]="Null",t.TokenName[6]="Numeric",t.TokenName[7]="Punctuator",t.TokenName[8]="String",t.TokenName[9]="RegularExpression",t.TokenName[10]="Template"},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.XHTMLEntities={quot:'"',amp:"&",apos:"'",gt:">",nbsp:"\xa0",iexcl:"\xa1",cent:"\xa2",pound:"\xa3",curren:"\xa4",yen:"\xa5",brvbar:"\xa6",sect:"\xa7",uml:"\xa8",copy:"\xa9",ordf:"\xaa",laquo:"\xab",not:"\xac",shy:"\xad",reg:"\xae",macr:"\xaf",deg:"\xb0",plusmn:"\xb1",sup2:"\xb2",sup3:"\xb3",acute:"\xb4",micro:"\xb5",para:"\xb6",middot:"\xb7",cedil:"\xb8",sup1:"\xb9",ordm:"\xba",raquo:"\xbb",frac14:"\xbc",frac12:"\xbd",frac34:"\xbe",iquest:"\xbf",Agrave:"\xc0",Aacute:"\xc1",Acirc:"\xc2",Atilde:"\xc3",Auml:"\xc4",Aring:"\xc5",AElig:"\xc6",Ccedil:"\xc7",Egrave:"\xc8",Eacute:"\xc9",Ecirc:"\xca",Euml:"\xcb",Igrave:"\xcc",Iacute:"\xcd",Icirc:"\xce",Iuml:"\xcf",ETH:"\xd0",Ntilde:"\xd1",Ograve:"\xd2",Oacute:"\xd3",Ocirc:"\xd4",Otilde:"\xd5",Ouml:"\xd6",times:"\xd7",Oslash:"\xd8",Ugrave:"\xd9",Uacute:"\xda",Ucirc:"\xdb",Uuml:"\xdc",Yacute:"\xdd",THORN:"\xde",szlig:"\xdf",agrave:"\xe0",aacute:"\xe1",acirc:"\xe2",atilde:"\xe3",auml:"\xe4",aring:"\xe5",aelig:"\xe6",ccedil:"\xe7",egrave:"\xe8",eacute:"\xe9",ecirc:"\xea",euml:"\xeb",igrave:"\xec",iacute:"\xed",icirc:"\xee",iuml:"\xef",eth:"\xf0",ntilde:"\xf1",ograve:"\xf2",oacute:"\xf3",ocirc:"\xf4",otilde:"\xf5",ouml:"\xf6",divide:"\xf7",oslash:"\xf8",ugrave:"\xf9",uacute:"\xfa",ucirc:"\xfb",uuml:"\xfc",yacute:"\xfd",thorn:"\xfe",yuml:"\xff",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",fnof:"ƒ",circ:"ˆ",tilde:"˜",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",bull:"•",hellip:"…",permil:"‰",prime:"′",Prime:"″",lsaquo:"‹",rsaquo:"›",oline:"‾",frasl:"⁄",euro:"€",image:"ℑ",weierp:"℘",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",lang:"⟨",rang:"⟩"}},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=i(10),r=i(12),s=i(13),a=function(){function e(){this.values=[],this.curly=this.paren=-1}return e.prototype.beforeFunctionExpression=function(e){return["(","{","[","in","typeof","instanceof","new","return","case","delete","throw","void","=","+=","-=","*=","**=","/=","%=","<<=",">>=",">>>=","&=","|=","^=",",","+","-","*","**","/","%","++","--","<<",">>",">>>","&","|","^","!","~","&&","||","?",":","===","==",">=","<=","<",">","!=","!=="].indexOf(e)>=0},e.prototype.isRegexStart=function(){var e=this.values[this.values.length-1],t=null!==e;switch(e){case"this":case"]":t=!1;break;case")":var i=this.values[this.paren-1];t="if"===i||"while"===i||"for"===i||"with"===i;break;case"}":if(t=!1,"function"===this.values[this.curly-3]){var n=this.values[this.curly-4];t=!!n&&!this.beforeFunctionExpression(n)}else if("function"===this.values[this.curly-4]){var n=this.values[this.curly-5];t=!n||!this.beforeFunctionExpression(n)}}return t},e.prototype.push=function(e){7===e.type||4===e.type?("{"===e.value?this.curly=this.values.length:"("===e.value&&(this.paren=this.values.length),this.values.push(e.value)):this.values.push(null)},e}();t.Tokenizer=function(){function e(e,t){this.errorHandler=new n.ErrorHandler,this.errorHandler.tolerant=!!t&&"boolean"==typeof t.tolerant&&t.tolerant,this.scanner=new r.Scanner(e,this.errorHandler),this.scanner.trackComment=!!t&&"boolean"==typeof t.comment&&t.comment,this.trackRange=!!t&&"boolean"==typeof t.range&&t.range,this.trackLoc=!!t&&"boolean"==typeof t.loc&&t.loc,this.buffer=[],this.reader=new a}return e.prototype.errors=function(){return this.errorHandler.errors},e.prototype.getNextToken=function(){if(0===this.buffer.length){var e=this.scanner.scanComments();if(this.scanner.trackComment)for(var t=0;t<e.length;++t){var i=e[t],n=this.scanner.source.slice(i.slice[0],i.slice[1]),r={type:i.multiLine?"BlockComment":"LineComment",value:n};this.trackRange&&(r.range=i.range),this.trackLoc&&(r.loc=i.loc),this.buffer.push(r)}if(!this.scanner.eof()){var a=void 0;this.trackLoc&&(a={start:{line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart},end:{}});var o="/"===this.scanner.source[this.scanner.index]&&this.reader.isRegexStart()?this.scanner.scanRegExp():this.scanner.lex();this.reader.push(o);var u={type:s.TokenName[o.type],value:this.scanner.source.slice(o.start,o.end)};this.trackRange&&(u.range=[o.start,o.end]),this.trackLoc&&(a.end={line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart},u.loc=a),9===o.type&&(u.regex={pattern:o.pattern,flags:o.flags}),this.buffer.push(u)}}return this.buffer.shift()},e}()}])})},9123:(e,t,i)=>{"use strict";function n(e){return"<<"===e||null===e}e.exports=new(i(3016))("tag:yaml.org,2002:merge",{kind:"scalar",resolve:n})},9379:(e,t,i)=>{"use strict";let n=i(9021),r=i(3944),s=i(5953),a=i(9666),o=i(7490),u=i(908),c=i(6929),h=i(6872),l=i(5582);function p(e,t){if(""===e)return{data:{},content:e,excerpt:"",orig:e};let i=c(e),n=p.cache[i.content];if(!t){if(n)return(i=Object.assign({},n)).orig=n.orig,i;p.cache[i.content]=i}return d(i,t)}function d(e,t){let i=s(t),n=i.delimiters[0],a="\n"+i.delimiters[1],u=e.content;i.language&&(e.language=i.language);let c=n.length;if(!l.startsWith(u,n,c))return o(e,i),e;if(u.charAt(c)===n.slice(-1))return e;let d=(u=u.slice(c)).length,f=p.language(u,i);f.name&&(e.language=f.name,u=u.slice(f.raw.length));let m=u.indexOf(a);return -1===m&&(m=d),e.matter=u.slice(0,m),""===e.matter.replace(/^\s*#[^\n]+/gm,"").trim()?(e.isEmpty=!0,e.empty=e.content,e.data={}):e.data=h(e.language,e.matter,i),m===d?e.content="":(e.content=u.slice(m+a.length),"\r"===e.content[0]&&(e.content=e.content.slice(1)),"\n"===e.content[0]&&(e.content=e.content.slice(1))),o(e,i),(!0===i.sections||"function"==typeof i.section)&&r(e,i.section),e}p.engines=u,p.stringify=function(e,t,i){return"string"==typeof e&&(e=p(e,i)),a(e,t,i)},p.read=function(e,t){let i=p(n.readFileSync(e,"utf8"),t);return i.path=e,i},p.test=function(e,t){return l.startsWith(e,s(t).delimiters[0])},p.language=function(e,t){let i=s(t).delimiters[0];p.test(e)&&(e=e.slice(i.length));let n=e.slice(0,e.search(/\r?\n/));return{raw:n,name:n?n.trim():""}},p.cache={},p.clearCache=function(){p.cache={}},e.exports=p},9666:(e,t,i)=>{"use strict";let n=i(3052),r=i(1929),s=i(5953);function a(e){return"\n"!==e.slice(-1)?e+"\n":e}e.exports=function(e,t,i){if(null==t&&null==i)switch(n(e)){case"object":t=e.data,i={};break;case"string":return e;default:throw TypeError("expected file to be a string or object")}let o=e.content,u=s(i);if(null==t){if(!u.data)return e;t=u.data}let c=e.language||u.language,h=r(c,u);if("function"!=typeof h.stringify)throw TypeError('expected "'+c+'.stringify" to be a function');t=Object.assign({},e.data,t);let l=u.delimiters[0],p=u.delimiters[1],d=h.stringify(t,i).trim(),f="";return"{}"!==d&&(f=a(l)+a(d)+a(p)),"string"==typeof e.excerpt&&""!==e.excerpt&&-1===o.indexOf(e.excerpt.trim())&&(f+=a(e.excerpt)+a(p)),f+a(o)}},9813:(e,t,i)=>{"use strict";var n=i(5167);e.exports=n.DEFAULT=new n({include:[i(6165)],explicit:[i(7651),i(4246),i(1687)]})}};