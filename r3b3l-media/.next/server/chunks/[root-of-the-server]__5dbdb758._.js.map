{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\"\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from \"next-auth/providers/credentials\"\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: \"credentials\",\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Password\", type: \"password\" }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        // For now, we'll use environment variables for admin auth\n        // In production, this should be replaced with a proper database\n        const adminEmail = process.env.ADMIN_EMAIL\n        const adminPassword = process.env.ADMIN_PASSWORD\n\n        if (credentials.email === adminEmail && credentials.password === adminPassword) {\n          return {\n            id: \"1\",\n            email: adminEmail,\n            name: \"R3B3L Admin\",\n            role: \"admin\"\n          }\n        }\n\n        return null\n      }\n    })\n  ],\n  session: {\n    strategy: \"jwt\"\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = (user as { role: string }).role\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: \"/admin/login\",\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n}\n"], "names": [], "mappings": ";;;AACA;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,0DAA0D;gBAC1D,gEAAgE;gBAChE,MAAM,aAAa,QAAQ,GAAG,CAAC,WAAW;gBAC1C,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc;gBAEhD,IAAI,YAAY,KAAK,KAAK,cAAc,YAAY,QAAQ,KAAK,eAAe;oBAC9E,OAAO;wBACL,IAAI;wBAC<PERSON>,OAAO;wBACP,MAAM;wBACN,MAAM;oBACR;gBACF;gBAEA,OAAO;YACT;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,AAAC,KAA0B,IAAI;YAC9C;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from \"next-auth\"\nimport { authOptions } from \"@/lib/auth\"\n\nconst handler = NextAuth(authOptions)\n\nexport { handler as GET, handler as POST }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,oHAAA,CAAA,cAAW", "debugId": null}}]}