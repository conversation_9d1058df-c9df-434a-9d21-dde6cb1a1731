{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/lib/posts.ts"], "sourcesContent": ["import fs from 'fs'\nimport path from 'path'\nimport matter from 'gray-matter'\n\nconst postsDirectory = path.join(process.cwd(), 'content/posts')\n\nexport interface Post {\n  slug: string\n  title: string\n  date: string\n  summary: string\n  author: string\n  badge: string\n  content: string\n}\n\nexport function getAllPosts(): Post[] {\n  try {\n    const fileNames = fs.readdirSync(postsDirectory)\n    const allPostsData = fileNames\n      .filter((fileName) => fileName.endsWith('.md'))\n      .map((fileName) => {\n        const slug = fileName.replace(/\\.md$/, '')\n        const fullPath = path.join(postsDirectory, fileName)\n        const fileContents = fs.readFileSync(fullPath, 'utf8')\n        const { data, content } = matter(fileContents)\n\n        return {\n          slug,\n          title: data.title || '',\n          date: data.date || '',\n          summary: data.summary || '',\n          author: data.author || '',\n          badge: data.badge || '',\n          content,\n        } as Post\n      })\n\n    return allPostsData.sort((a, b) => (a.date < b.date ? 1 : -1))\n  } catch (error) {\n    console.error('Error reading posts:', error)\n    return []\n  }\n}\n\nexport function getPostBySlug(slug: string): Post | null {\n  try {\n    const fullPath = path.join(postsDirectory, `${slug}.md`)\n    const fileContents = fs.readFileSync(fullPath, 'utf8')\n    const { data, content } = matter(fileContents)\n\n    return {\n      slug,\n      title: data.title || '',\n      date: data.date || '',\n      summary: data.summary || '',\n      author: data.author || '',\n      badge: data.badge || '',\n      content,\n    } as Post\n  } catch (error) {\n    console.error(`Error reading post ${slug}:`, error)\n    return null\n  }\n}\n\nexport function getAllPostSlugs(): string[] {\n  try {\n    const fileNames = fs.readdirSync(postsDirectory)\n    return fileNames\n      .filter((fileName) => fileName.endsWith('.md'))\n      .map((fileName) => fileName.replace(/\\.md$/, ''))\n  } catch (error) {\n    console.error('Error reading post slugs:', error)\n    return []\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEA,MAAM,iBAAiB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAYzC,SAAS;IACd,IAAI;QACF,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,WAAW,CAAC;QACjC,MAAM,eAAe,UAClB,MAAM,CAAC,CAAC,WAAa,SAAS,QAAQ,CAAC,QACvC,GAAG,CAAC,CAAC;YACJ,MAAM,OAAO,SAAS,OAAO,CAAC,SAAS;YACvC,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,gBAAgB;YAC3C,MAAM,eAAe,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,UAAU;YAC/C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,UAAM,AAAD,EAAE;YAEjC,OAAO;gBACL;gBACA,OAAO,KAAK,KAAK,IAAI;gBACrB,MAAM,KAAK,IAAI,IAAI;gBACnB,SAAS,KAAK,OAAO,IAAI;gBACzB,QAAQ,KAAK,MAAM,IAAI;gBACvB,OAAO,KAAK,KAAK,IAAI;gBACrB;YACF;QACF;QAEF,OAAO,aAAa,IAAI,CAAC,CAAC,GAAG,IAAO,EAAE,IAAI,GAAG,EAAE,IAAI,GAAG,IAAI,CAAC;IAC7D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,EAAE;IACX;AACF;AAEO,SAAS,cAAc,IAAY;IACxC,IAAI;QACF,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,gBAAgB,GAAG,KAAK,GAAG,CAAC;QACvD,MAAM,eAAe,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,UAAU;QAC/C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,UAAM,AAAD,EAAE;QAEjC,OAAO;YACL;YACA,OAAO,KAAK,KAAK,IAAI;YACrB,MAAM,KAAK,IAAI,IAAI;YACnB,SAAS,KAAK,OAAO,IAAI;YACzB,QAAQ,KAAK,MAAM,IAAI;YACvB,OAAO,KAAK,KAAK,IAAI;YACrB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC,EAAE;QAC7C,OAAO;IACT;AACF;AAEO,SAAS;IACd,IAAI;QACF,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,WAAW,CAAC;QACjC,OAAO,UACJ,MAAM,CAAC,CAAC,WAAa,SAAS,QAAQ,CAAC,QACvC,GAAG,CAAC,CAAC,WAAa,SAAS,OAAO,CAAC,SAAS;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,EAAE;IACX;AACF", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/articles/%5Bslug%5D/page.tsx"], "sourcesContent": ["import { notFound } from 'next/navigation'\nimport { getPostBySlug, getAllPostSlugs } from '@/lib/posts'\nimport { Metadata } from 'next'\nimport ReactMarkdown from 'react-markdown'\n\ninterface ArticlePageProps {\n  params: Promise<{ slug: string }>\n}\n\nexport async function generateStaticParams() {\n  const slugs = getAllPostSlugs()\n  return slugs.map((slug) => ({\n    slug,\n  }))\n}\n\nexport async function generateMetadata({ params }: ArticlePageProps): Promise<Metadata> {\n  const { slug } = await params\n  const post = getPostBySlug(slug)\n  \n  if (!post) {\n    return {\n      title: 'Article Not Found | R3B3L M3D14',\n    }\n  }\n\n  return {\n    title: `${post.title} | R3B3L M3D14`,\n    description: post.summary,\n    openGraph: {\n      title: post.title,\n      description: post.summary,\n      type: 'article',\n      publishedTime: post.date,\n      authors: [post.author],\n    },\n    twitter: {\n      card: 'summary_large_image',\n      title: post.title,\n      description: post.summary,\n    },\n  }\n}\n\nexport default async function ArticlePage({ params }: ArticlePageProps) {\n  const { slug } = await params\n  const post = getPostBySlug(slug)\n\n  if (!post) {\n    notFound()\n  }\n\n  return (\n    <div className=\"min-h-screen py-8\">\n      <article className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Article Header */}\n        <header className=\"mb-12\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <span className=\"flame-badge\">\n              🔥 {post.badge}\n            </span>\n            <time className=\"text-gray-400 text-sm\">\n              {new Date(post.date).toLocaleDateString('en-US', {\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric'\n              })}\n            </time>\n          </div>\n          \n          <h1 className=\"text-4xl md:text-6xl font-bold text-white mb-6 leading-tight\">\n            {post.title}\n          </h1>\n          \n          <p className=\"text-xl text-gray-300 mb-6 leading-relaxed\">\n            {post.summary}\n          </p>\n          \n          <div className=\"flex items-center justify-between pt-6 border-t border-gray-800\">\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-gray-400\">By</span>\n              <span className=\"text-white font-medium\">{post.author}</span>\n            </div>\n            <div className=\"glitch-text text-sm\">\n              NODE CERTIFIED CONTENT\n            </div>\n          </div>\n        </header>\n\n        {/* Article Content */}\n        <div className=\"prose prose-invert prose-lg max-w-none\">\n          <ReactMarkdown\n            components={{\n              h1: ({ children }) => (\n                <h1 className=\"text-3xl md:text-4xl font-bold text-white mb-6 mt-8\">\n                  {children}\n                </h1>\n              ),\n              h2: ({ children }) => (\n                <h2 className=\"text-2xl md:text-3xl font-bold text-white mb-4 mt-8\">\n                  {children}\n                </h2>\n              ),\n              h3: ({ children }) => (\n                <h3 className=\"text-xl md:text-2xl font-bold text-white mb-4 mt-6\">\n                  {children}\n                </h3>\n              ),\n              p: ({ children }) => (\n                <p className=\"text-gray-300 mb-4 leading-relaxed\">\n                  {children}\n                </p>\n              ),\n              strong: ({ children }) => (\n                <strong className=\"text-white font-bold\">\n                  {children}\n                </strong>\n              ),\n              em: ({ children }) => (\n                <em className=\"text-gray-200 italic\">\n                  {children}\n                </em>\n              ),\n              ul: ({ children }) => (\n                <ul className=\"list-disc list-inside text-gray-300 mb-4 space-y-2\">\n                  {children}\n                </ul>\n              ),\n              ol: ({ children }) => (\n                <ol className=\"list-decimal list-inside text-gray-300 mb-4 space-y-2\">\n                  {children}\n                </ol>\n              ),\n              li: ({ children }) => (\n                <li className=\"text-gray-300\">\n                  {children}\n                </li>\n              ),\n              blockquote: ({ children }) => (\n                <blockquote className=\"border-l-4 border-red-500 pl-6 py-2 my-6 bg-gray-900/50 rounded-r-lg\">\n                  <div className=\"text-gray-200 italic\">\n                    {children}\n                  </div>\n                </blockquote>\n              ),\n              code: ({ children }) => (\n                <code className=\"bg-gray-800 text-cyan-400 px-2 py-1 rounded text-sm\">\n                  {children}\n                </code>\n              ),\n              pre: ({ children }) => (\n                <pre className=\"bg-gray-900 border border-gray-700 rounded-lg p-4 overflow-x-auto mb-4\">\n                  {children}\n                </pre>\n              ),\n            }}\n          >\n            {post.content}\n          </ReactMarkdown>\n        </div>\n\n        {/* Article Footer */}\n        <footer className=\"mt-12 pt-8 border-t border-gray-800\">\n          <div className=\"bg-gray-900/50 border border-gray-800 rounded-lg p-6\">\n            <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-white mb-2\">\n                  Share this R3B3L Intelligence Drop\n                </h3>\n                <p className=\"text-gray-400 text-sm\">\n                  Help spread truth across the digital resistance network\n                </p>\n              </div>\n              <div className=\"flex space-x-4\">\n                <span className=\"flame-badge\">FLAME LICENSED</span>\n                <span className=\"glitch-text text-sm\">NODE SEALED</span>\n              </div>\n            </div>\n          </div>\n        </footer>\n      </article>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;AAEA;;;;;AAMO,eAAe;IACpB,MAAM,QAAQ,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD;IAC5B,OAAO,MAAM,GAAG,CAAC,CAAC,OAAS,CAAC;YAC1B;QACF,CAAC;AACH;AAEO,eAAe,iBAAiB,EAAE,MAAM,EAAoB;IACjE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IACvB,MAAM,OAAO,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE;IAE3B,IAAI,CAAC,MAAM;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,OAAO;QACL,OAAO,GAAG,KAAK,KAAK,CAAC,cAAc,CAAC;QACpC,aAAa,KAAK,OAAO;QACzB,WAAW;YACT,OAAO,KAAK,KAAK;YACjB,aAAa,KAAK,OAAO;YACzB,MAAM;YACN,eAAe,KAAK,IAAI;YACxB,SAAS;gBAAC,KAAK,MAAM;aAAC;QACxB;QACA,SAAS;YACP,MAAM;YACN,OAAO,KAAK,KAAK;YACjB,aAAa,KAAK,OAAO;QAC3B;IACF;AACF;AAEe,eAAe,YAAY,EAAE,MAAM,EAAoB;IACpE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IACvB,MAAM,OAAO,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE;IAE3B,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAQ,WAAU;;8BAEjB,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;;wCAAc;wCACxB,KAAK,KAAK;;;;;;;8CAEhB,8OAAC;oCAAK,WAAU;8CACb,IAAI,KAAK,KAAK,IAAI,EAAE,kBAAkB,CAAC,SAAS;wCAC/C,MAAM;wCACN,OAAO;wCACP,KAAK;oCACP;;;;;;;;;;;;sCAIJ,8OAAC;4BAAG,WAAU;sCACX,KAAK,KAAK;;;;;;sCAGb,8OAAC;4BAAE,WAAU;sCACV,KAAK,OAAO;;;;;;sCAGf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAK,WAAU;sDAA0B,KAAK,MAAM;;;;;;;;;;;;8CAEvD,8OAAC;oCAAI,WAAU;8CAAsB;;;;;;;;;;;;;;;;;;8BAOzC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,wLAAA,CAAA,UAAa;wBACZ,YAAY;4BACV,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;oCAAG,WAAU;8CACX;;;;;;4BAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;oCAAG,WAAU;8CACX;;;;;;4BAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;oCAAG,WAAU;8CACX;;;;;;4BAGL,GAAG,CAAC,EAAE,QAAQ,EAAE,iBACd,8OAAC;oCAAE,WAAU;8CACV;;;;;;4BAGL,QAAQ,CAAC,EAAE,QAAQ,EAAE,iBACnB,8OAAC;oCAAO,WAAU;8CACf;;;;;;4BAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;oCAAG,WAAU;8CACX;;;;;;4BAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;oCAAG,WAAU;8CACX;;;;;;4BAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;oCAAG,WAAU;8CACX;;;;;;4BAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;oCAAG,WAAU;8CACX;;;;;;4BAGL,YAAY,CAAC,EAAE,QAAQ,EAAE,iBACvB,8OAAC;oCAAW,WAAU;8CACpB,cAAA,8OAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;4BAIP,MAAM,CAAC,EAAE,QAAQ,EAAE,iBACjB,8OAAC;oCAAK,WAAU;8CACb;;;;;;4BAGL,KAAK,CAAC,EAAE,QAAQ,EAAE,iBAChB,8OAAC;oCAAI,WAAU;8CACZ;;;;;;wBAGP;kCAEC,KAAK,OAAO;;;;;;;;;;;8BAKjB,8OAAC;oBAAO,WAAU;8BAChB,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAc;;;;;;sDAC9B,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtD", "debugId": null}}]}