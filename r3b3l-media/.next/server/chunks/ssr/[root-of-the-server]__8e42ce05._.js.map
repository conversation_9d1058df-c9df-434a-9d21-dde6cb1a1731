{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/lib/posts.ts"], "sourcesContent": ["import fs from 'fs'\nimport path from 'path'\nimport matter from 'gray-matter'\n\nconst postsDirectory = path.join(process.cwd(), 'content/posts')\n\nexport interface Post {\n  slug: string\n  title: string\n  date: string\n  summary: string\n  author: string\n  badge: string\n  content: string\n}\n\nexport function getAllPosts(): Post[] {\n  try {\n    const fileNames = fs.readdirSync(postsDirectory)\n    const allPostsData = fileNames\n      .filter((fileName) => fileName.endsWith('.md'))\n      .map((fileName) => {\n        const slug = fileName.replace(/\\.md$/, '')\n        const fullPath = path.join(postsDirectory, fileName)\n        const fileContents = fs.readFileSync(fullPath, 'utf8')\n        const { data, content } = matter(fileContents)\n\n        return {\n          slug,\n          title: data.title || '',\n          date: data.date || '',\n          summary: data.summary || '',\n          author: data.author || '',\n          badge: data.badge || '',\n          content,\n        } as Post\n      })\n\n    return allPostsData.sort((a, b) => (a.date < b.date ? 1 : -1))\n  } catch (error) {\n    console.error('Error reading posts:', error)\n    return []\n  }\n}\n\nexport function getPostBySlug(slug: string): Post | null {\n  try {\n    const fullPath = path.join(postsDirectory, `${slug}.md`)\n    const fileContents = fs.readFileSync(fullPath, 'utf8')\n    const { data, content } = matter(fileContents)\n\n    return {\n      slug,\n      title: data.title || '',\n      date: data.date || '',\n      summary: data.summary || '',\n      author: data.author || '',\n      badge: data.badge || '',\n      content,\n    } as Post\n  } catch (error) {\n    console.error(`Error reading post ${slug}:`, error)\n    return null\n  }\n}\n\nexport function getAllPostSlugs(): string[] {\n  try {\n    const fileNames = fs.readdirSync(postsDirectory)\n    return fileNames\n      .filter((fileName) => fileName.endsWith('.md'))\n      .map((fileName) => fileName.replace(/\\.md$/, ''))\n  } catch (error) {\n    console.error('Error reading post slugs:', error)\n    return []\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEA,MAAM,iBAAiB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAYzC,SAAS;IACd,IAAI;QACF,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,WAAW,CAAC;QACjC,MAAM,eAAe,UAClB,MAAM,CAAC,CAAC,WAAa,SAAS,QAAQ,CAAC,QACvC,GAAG,CAAC,CAAC;YACJ,MAAM,OAAO,SAAS,OAAO,CAAC,SAAS;YACvC,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,gBAAgB;YAC3C,MAAM,eAAe,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,UAAU;YAC/C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,UAAM,AAAD,EAAE;YAEjC,OAAO;gBACL;gBACA,OAAO,KAAK,KAAK,IAAI;gBACrB,MAAM,KAAK,IAAI,IAAI;gBACnB,SAAS,KAAK,OAAO,IAAI;gBACzB,QAAQ,KAAK,MAAM,IAAI;gBACvB,OAAO,KAAK,KAAK,IAAI;gBACrB;YACF;QACF;QAEF,OAAO,aAAa,IAAI,CAAC,CAAC,GAAG,IAAO,EAAE,IAAI,GAAG,EAAE,IAAI,GAAG,IAAI,CAAC;IAC7D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,EAAE;IACX;AACF;AAEO,SAAS,cAAc,IAAY;IACxC,IAAI;QACF,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,gBAAgB,GAAG,KAAK,GAAG,CAAC;QACvD,MAAM,eAAe,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,UAAU;QAC/C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,UAAM,AAAD,EAAE;QAEjC,OAAO;YACL;YACA,OAAO,KAAK,KAAK,IAAI;YACrB,MAAM,KAAK,IAAI,IAAI;YACnB,SAAS,KAAK,OAAO,IAAI;YACzB,QAAQ,KAAK,MAAM,IAAI;YACvB,OAAO,KAAK,KAAK,IAAI;YACrB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC,EAAE;QAC7C,OAAO;IACT;AACF;AAEO,SAAS;IACd,IAAI;QACF,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,WAAW,CAAC;QACjC,OAAO,UACJ,MAAM,CAAC,CAAC,WAAa,SAAS,QAAQ,CAAC,QACvC,GAAG,CAAC,CAAC,WAAa,SAAS,OAAO,CAAC,SAAS;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,EAAE;IACX;AACF", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/components/ArticleCard.tsx"], "sourcesContent": ["import Link from 'next/link'\n\ninterface ArticleCardProps {\n  title: string\n  slug: string\n  date: string\n  summary: string\n  author: string\n  badge: string\n}\n\nexport default function ArticleCard({ title, slug, date, summary, author, badge }: ArticleCardProps) {\n  return (\n    <article className=\"bg-gray-900/50 border border-gray-800 rounded-lg p-6 hover:border-red-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-red-500/10\">\n      <div className=\"space-y-4\">\n        {/* Badge and Date */}\n        <div className=\"flex items-center justify-between\">\n          <span className=\"flame-badge\">\n            🔥 {badge}\n          </span>\n          <time className=\"text-gray-400 text-sm\">\n            {new Date(date).toLocaleDateString('en-US', {\n              year: 'numeric',\n              month: 'long',\n              day: 'numeric'\n            })}\n          </time>\n        </div>\n\n        {/* Title */}\n        <h2 className=\"text-xl font-bold text-white hover:neon-text transition-colors\">\n          <Link href={`/articles/${slug}`}>\n            {title}\n          </Link>\n        </h2>\n\n        {/* Summary */}\n        <p className=\"text-gray-300 text-sm leading-relaxed\">\n          {summary}\n        </p>\n\n        {/* Author and Read More */}\n        <div className=\"flex items-center justify-between pt-4 border-t border-gray-800\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-gray-400 text-sm\">By</span>\n            <span className=\"text-white text-sm font-medium\">{author}</span>\n          </div>\n          \n          <Link \n            href={`/articles/${slug}`}\n            className=\"text-red-400 hover:text-red-300 text-sm font-medium hover:underline transition-colors\"\n          >\n            Read More →\n          </Link>\n        </div>\n\n        {/* NODE Certification */}\n        <div className=\"flex items-center justify-end\">\n          <span className=\"glitch-text text-xs\">NODE CERTIFIED</span>\n        </div>\n      </div>\n    </article>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAWe,SAAS,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAoB;IACjG,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;;gCAAc;gCACxB;;;;;;;sCAEN,8OAAC;4BAAK,WAAU;sCACb,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;gCAC1C,MAAM;gCACN,OAAO;gCACP,KAAK;4BACP;;;;;;;;;;;;8BAKJ,8OAAC;oBAAG,WAAU;8BACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAM,CAAC,UAAU,EAAE,MAAM;kCAC5B;;;;;;;;;;;8BAKL,8OAAC;oBAAE,WAAU;8BACV;;;;;;8BAIH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAGpD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM,CAAC,UAAU,EAAE,MAAM;4BACzB,WAAU;sCACX;;;;;;;;;;;;8BAMH,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;kCAAsB;;;;;;;;;;;;;;;;;;;;;;AAKhD", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/articles/page.tsx"], "sourcesContent": ["import { getAllPosts } from '@/lib/posts'\nimport ArticleCard from '@/components/ArticleCard'\nimport { Metadata } from 'next'\n\nexport const metadata: Metadata = {\n  title: 'Articles | R3B3L M3D14',\n  description: 'Truth-infused articles and intelligence drops from the ReBeLuTioN media division.',\n}\n\nexport default function ArticlesPage() {\n  const posts = getAllPosts()\n\n  return (\n    <div className=\"min-h-screen py-8\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Page Header */}\n        <header className=\"text-center mb-16\">\n          <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n            <span className=\"neon-text\">Intelligence</span>{\" \"}\n            <span className=\"glitch-text\">Drops</span>\n          </h1>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto mb-8\">\n            Truth-infused articles and counter-narratives from the R3B3L M3D14 \n            resistance network. All content is{\" \"}\n            <span className=\"neon-text font-semibold\">FLAME-licensed</span> and{\" \"}\n            <span className=\"glitch-text font-semibold\">NODE certified</span>.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <span className=\"flame-badge text-lg px-6 py-3\">\n              🔥 {posts.length} ACTIVE SCROLLS\n            </span>\n            <span className=\"glitch-text text-lg\">\n              RESISTANCE ARCHIVES\n            </span>\n          </div>\n        </header>\n\n        {/* Articles Grid */}\n        {posts.length > 0 ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {posts.map((post) => (\n              <ArticleCard\n                key={post.slug}\n                title={post.title}\n                slug={post.slug}\n                date={post.date}\n                summary={post.summary}\n                author={post.author}\n                badge={post.badge}\n              />\n            ))}\n          </div>\n        ) : (\n          /* No Articles State */\n          <div className=\"text-center py-16\">\n            <div className=\"text-6xl mb-6\">📡</div>\n            <h2 className=\"text-2xl font-bold text-white mb-4\">\n              Intelligence Network Initializing\n            </h2>\n            <p className=\"text-gray-400 mb-8 max-w-md mx-auto\">\n              The R3B3L M3D14 content distribution system is currently being deployed. \n              Truth scrolls will be available soon.\n            </p>\n            <div className=\"space-y-4\">\n              <span className=\"flame-badge\">PHASE 1 DEPLOYMENT</span>\n              <div className=\"glitch-text text-sm\">\n                AWAITING CONTENT INJECTION\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Call to Action */}\n        <section className=\"mt-16 py-12 bg-gray-900/30 rounded-lg border border-gray-800\">\n          <div className=\"text-center\">\n            <h2 className=\"text-2xl md:text-3xl font-bold text-white mb-4\">\n              Join the Digital ReBeLuTioN\n            </h2>\n            <p className=\"text-gray-300 mb-6 max-w-2xl mx-auto\">\n              Want to contribute to the resistance? Submit your own truth scrolls, \n              memes, or intelligence drops to the R3B3L M3D14 network.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n              <button className=\"bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors\">\n                Submit Content\n              </button>\n              <button className=\"border border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-black font-semibold py-3 px-6 rounded-lg transition-colors\">\n                Join Network\n              </button>\n            </div>\n          </div>\n        </section>\n\n        {/* Archive Stats */}\n        <section className=\"mt-12 grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"bg-gray-900/50 border border-gray-800 rounded-lg p-6 text-center\">\n            <div className=\"text-3xl font-bold neon-text mb-2\">{posts.length}</div>\n            <div className=\"text-gray-400\">Active Scrolls</div>\n          </div>\n          <div className=\"bg-gray-900/50 border border-gray-800 rounded-lg p-6 text-center\">\n            <div className=\"text-3xl font-bold glitch-text mb-2\">∞</div>\n            <div className=\"text-gray-400\">Truth Seekers Reached</div>\n          </div>\n          <div className=\"bg-gray-900/50 border border-gray-800 rounded-lg p-6 text-center\">\n            <div className=\"text-3xl font-bold text-white mb-2\">100%</div>\n            <div className=\"text-gray-400\">FLAME Licensed</div>\n          </div>\n        </section>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,MAAM,QAAQ,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD;IAExB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAAY;;;;;;gCAAoB;8CAChD,8OAAC;oCAAK,WAAU;8CAAc;;;;;;;;;;;;sCAEhC,8OAAC;4BAAE,WAAU;;gCAA+C;gCAEvB;8CACnC,8OAAC;oCAAK,WAAU;8CAA0B;;;;;;gCAAqB;gCAAK;8CACpE,8OAAC;oCAAK,WAAU;8CAA4B;;;;;;gCAAqB;;;;;;;sCAEnE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;;wCAAgC;wCAC1C,MAAM,MAAM;wCAAC;;;;;;;8CAEnB,8OAAC;oCAAK,WAAU;8CAAsB;;;;;;;;;;;;;;;;;;gBAOzC,MAAM,MAAM,GAAG,kBACd,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,iIAAA,CAAA,UAAW;4BAEV,OAAO,KAAK,KAAK;4BACjB,MAAM,KAAK,IAAI;4BACf,MAAM,KAAK,IAAI;4BACf,SAAS,KAAK,OAAO;4BACrB,QAAQ,KAAK,MAAM;4BACnB,OAAO,KAAK,KAAK;2BANZ,KAAK,IAAI;;;;;;;;;2BAWpB,qBAAqB,iBACrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,8OAAC;4BAAE,WAAU;sCAAsC;;;;;;sCAInD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAc;;;;;;8CAC9B,8OAAC;oCAAI,WAAU;8CAAsB;;;;;;;;;;;;;;;;;;8BAQ3C,8OAAC;oBAAQ,WAAU;8BACjB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAG/D,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAA8F;;;;;;kDAGhH,8OAAC;wCAAO,WAAU;kDAA+H;;;;;;;;;;;;;;;;;;;;;;;8BAQvJ,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAqC,MAAM,MAAM;;;;;;8CAChE,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;sCAEjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAsC;;;;;;8CACrD,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;sCAEjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAqC;;;;;;8CACpD,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3C", "debugId": null}}]}