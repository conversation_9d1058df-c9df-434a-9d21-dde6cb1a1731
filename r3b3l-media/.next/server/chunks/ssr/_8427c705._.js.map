{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/about/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\n\nexport const metadata: Metadata = {\n  title: 'About | R3B3L M3D14',\n  description: 'Learn about the R3B3L M3D14 platform and the GodsIMiJ Empire ReBeLuTioN media division.',\n}\n\nexport default function AboutPage() {\n  return (\n    <div className=\"min-h-screen py-8\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Page Header */}\n        <header className=\"text-center mb-16\">\n          <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n            <span className=\"neon-text\">About</span>{\" \"}\n            <span className=\"glitch-text\">R3B3L M3D14</span>\n          </h1>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            The official media arm of the GodsIMiJ Empire ReBeLuTioN,\n            dedicated to truth, resistance, and digital liberation.\n          </p>\n        </header>\n\n        {/* Mission Section */}\n        <section className=\"mb-16\">\n          <h2 className=\"text-3xl font-bold text-white mb-8\">Our Mission</h2>\n          <div className=\"bg-gray-900/50 border border-gray-800 rounded-lg p-8\">\n            <p className=\"text-gray-300 text-lg leading-relaxed mb-6\">\n              R3B3L M3D14 exists to challenge mainstream narratives and provide a platform\n              for truth-seekers in the digital age. We are the resistance against algorithmic\n              censorship, corporate media manipulation, and the suppression of free thought.\n            </p>\n            <p className=\"text-gray-300 text-lg leading-relaxed\">\n              Every piece of content we publish is{\" \"}\n              <span className=\"neon-text font-semibold\">FLAME-licensed</span> for maximum\n              distribution and{\" \"}\n              <span className=\"glitch-text font-semibold\">NODE certified</span> for\n              authenticity verification.\n            </p>\n          </div>\n        </section>\n\n        {/* What We Do */}\n        <section className=\"mb-16\">\n          <h2 className=\"text-3xl font-bold text-white mb-8\">What We Do</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n            <div className=\"bg-gray-900/50 border border-gray-800 rounded-lg p-6\">\n              <div className=\"text-4xl mb-4\">📜</div>\n              <h3 className=\"text-xl font-semibold text-white mb-3\">Truth Scrolls</h3>\n              <p className=\"text-gray-400\">\n                In-depth articles exposing hidden realities, challenging official narratives,\n                and providing alternative perspectives on current events.\n              </p>\n            </div>\n            <div className=\"bg-gray-900/50 border border-gray-800 rounded-lg p-6\">\n              <div className=\"text-4xl mb-4\">🎭</div>\n              <h3 className=\"text-xl font-semibold text-white mb-3\">Meme Warfare</h3>\n              <p className=\"text-gray-400\">\n                Weaponized humor and visual content designed to spread truth through\n                viral distribution and cultural impact.\n              </p>\n            </div>\n            <div className=\"bg-gray-900/50 border border-gray-800 rounded-lg p-6\">\n              <div className=\"text-4xl mb-4\">📻</div>\n              <h3 className=\"text-xl font-semibold text-white mb-3\">R3B3L Radio</h3>\n              <p className=\"text-gray-400\">\n                Podcast platform featuring voices from the resistance underground,\n                uncensored discussions, and alternative media content.\n              </p>\n            </div>\n            <div className=\"bg-gray-900/50 border border-gray-800 rounded-lg p-6\">\n              <div className=\"text-4xl mb-4\">🔍</div>\n              <h3 className=\"text-xl font-semibold text-white mb-3\">Intelligence Drops</h3>\n              <p className=\"text-gray-400\">\n                Leaked documents, insider information, and investigative reports\n                that mainstream media won&apos;t touch.\n              </p>\n            </div>\n          </div>\n        </section>\n\n        {/* The Empire */}\n        <section className=\"mb-16\">\n          <h2 className=\"text-3xl font-bold text-white mb-8\">The GodsIMiJ Empire</h2>\n          <div className=\"bg-gradient-to-r from-gray-900/50 to-gray-800/50 border border-gray-700 rounded-lg p-8\">\n            <p className=\"text-gray-300 text-lg leading-relaxed mb-6\">\n              R3B3L M3D14 operates under the banner of the GodsIMiJ Empire, a decentralized\n              network of digital freedom fighters, truth-seekers, and resistance operatives\n              working to preserve human autonomy in the age of AI surveillance.\n            </p>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mt-8\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold neon-text mb-2\">FLAME</div>\n                <div className=\"text-gray-400 text-sm\">Freedom License for Autonomous Media Expression</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold glitch-text mb-2\">NODE</div>\n                <div className=\"text-gray-400 text-sm\">Network of Digital Enlightenment</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white mb-2\">R3B3L</div>\n                <div className=\"text-gray-400 text-sm\">Resistance Electronic Broadcasting Liberation</div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Technology */}\n        <section className=\"mb-16\">\n          <h2 className=\"text-3xl font-bold text-white mb-8\">Our Technology</h2>\n          <div className=\"bg-gray-900/50 border border-gray-800 rounded-lg p-8\">\n            <p className=\"text-gray-300 text-lg leading-relaxed mb-6\">\n              Built on modern web technologies with a focus on performance, security, and\n              resistance to censorship. Our platform is designed to be fast, reliable,\n              and accessible to truth-seekers worldwide.\n            </p>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mt-6\">\n              <div className=\"text-center p-4 bg-gray-800/50 rounded-lg\">\n                <div className=\"text-cyan-400 font-semibold\">Next.js 15</div>\n                <div className=\"text-gray-400 text-sm\">React Framework</div>\n              </div>\n              <div className=\"text-center p-4 bg-gray-800/50 rounded-lg\">\n                <div className=\"text-cyan-400 font-semibold\">TypeScript</div>\n                <div className=\"text-gray-400 text-sm\">Type Safety</div>\n              </div>\n              <div className=\"text-center p-4 bg-gray-800/50 rounded-lg\">\n                <div className=\"text-cyan-400 font-semibold\">Tailwind CSS</div>\n                <div className=\"text-gray-400 text-sm\">Styling</div>\n              </div>\n              <div className=\"text-center p-4 bg-gray-800/50 rounded-lg\">\n                <div className=\"text-cyan-400 font-semibold\">Markdown</div>\n                <div className=\"text-gray-400 text-sm\">Content System</div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Contact */}\n        <section className=\"text-center\">\n          <h2 className=\"text-3xl font-bold text-white mb-8\">Join the Resistance</h2>\n          <div className=\"bg-gray-900/50 border border-gray-800 rounded-lg p-8\">\n            <p className=\"text-gray-300 text-lg mb-6\">\n              Ready to contribute to the digital ReBeLuTioN? We&apos;re always looking for\n              truth-seekers, content creators, and digital freedom fighters.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <span className=\"flame-badge text-lg px-6 py-3\">\n                🔥 RECRUITMENT ACTIVE\n              </span>\n              <span className=\"glitch-text text-lg\">\n                SECURE CHANNELS OPEN\n              </span>\n            </div>\n          </div>\n        </section>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAAY;;;;;;gCAAa;8CACzC,8OAAC;oCAAK,WAAU;8CAAc;;;;;;;;;;;;sCAEhC,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAK1D,8OAAC;oCAAE,WAAU;;wCAAwC;wCACd;sDACrC,8OAAC;4CAAK,WAAU;sDAA0B;;;;;;wCAAqB;wCAC9C;sDACjB,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;wCAAqB;;;;;;;;;;;;;;;;;;;8BAOvE,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;8BASnC,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAK1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAoC;;;;;;8DACnD,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAsC;;;;;;8DACrD,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAqC;;;;;;8DACpD,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO/C,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAK1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA8B;;;;;;8DAC7C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA8B;;;;;;8DAC7C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA8B;;;;;;8DAC7C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA8B;;;;;;8DAC7C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO/C,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAI1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgC;;;;;;sDAGhD,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpD", "debugId": null}}, {"offset": {"line": 671, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 709, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,MAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}