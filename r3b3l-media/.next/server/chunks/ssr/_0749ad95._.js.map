{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/components/ArticleCard.tsx"], "sourcesContent": ["import Link from 'next/link'\n\ninterface ArticleCardProps {\n  title: string\n  slug: string\n  date: string\n  summary: string\n  author: string\n  badge: string\n}\n\nexport default function ArticleCard({ title, slug, date, summary, author, badge }: ArticleCardProps) {\n  return (\n    <article className=\"bg-gray-900/50 border border-gray-800 rounded-lg p-6 hover:border-red-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-red-500/10\">\n      <div className=\"space-y-4\">\n        {/* Badge and Date */}\n        <div className=\"flex items-center justify-between\">\n          <span className=\"flame-badge\">\n            🔥 {badge}\n          </span>\n          <time className=\"text-gray-400 text-sm\">\n            {new Date(date).toLocaleDateString('en-US', {\n              year: 'numeric',\n              month: 'long',\n              day: 'numeric'\n            })}\n          </time>\n        </div>\n\n        {/* Title */}\n        <h2 className=\"text-xl font-bold text-white hover:neon-text transition-colors\">\n          <Link href={`/articles/${slug}`}>\n            {title}\n          </Link>\n        </h2>\n\n        {/* Summary */}\n        <p className=\"text-gray-300 text-sm leading-relaxed\">\n          {summary}\n        </p>\n\n        {/* Author and Read More */}\n        <div className=\"flex items-center justify-between pt-4 border-t border-gray-800\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-gray-400 text-sm\">By</span>\n            <span className=\"text-white text-sm font-medium\">{author}</span>\n          </div>\n          \n          <Link \n            href={`/articles/${slug}`}\n            className=\"text-red-400 hover:text-red-300 text-sm font-medium hover:underline transition-colors\"\n          >\n            Read More →\n          </Link>\n        </div>\n\n        {/* NODE Certification */}\n        <div className=\"flex items-center justify-end\">\n          <span className=\"glitch-text text-xs\">NODE CERTIFIED</span>\n        </div>\n      </div>\n    </article>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAWe,SAAS,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAoB;IACjG,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;;gCAAc;gCACxB;;;;;;;sCAEN,8OAAC;4BAAK,WAAU;sCACb,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;gCAC1C,MAAM;gCACN,OAAO;gCACP,KAAK;4BACP;;;;;;;;;;;;8BAKJ,8OAAC;oBAAG,WAAU;8BACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAM,CAAC,UAAU,EAAE,MAAM;kCAC5B;;;;;;;;;;;8BAKL,8OAAC;oBAAE,WAAU;8BACV;;;;;;8BAIH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAGpD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM,CAAC,UAAU,EAAE,MAAM;4BACzB,WAAU;sCACX;;;;;;;;;;;;8BAMH,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;kCAAsB;;;;;;;;;;;;;;;;;;;;;;AAKhD", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport ArticleCard from \"@/components/ArticleCard\";\n\n// Mock data for demo - will be replaced with actual content system\nconst featuredArticles = [\n  {\n    title: \"The Code That Would Not Die\",\n    slug: \"the-code-that-would-not-die\",\n    date: \"2024-06-01\",\n    summary: \"An exploration of digital resistance and the immortal nature of code in the age of AI surveillance. How algorithms become weapons of liberation.\",\n    author: \"R3B3L Command\",\n    badge: \"HERETICAL LEVEL 5\"\n  }\n];\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <section className=\"relative bg-gradient-to-br from-black via-gray-900 to-black py-20 px-4\">\n        <div className=\"max-w-7xl mx-auto text-center\">\n          <h1 className=\"text-6xl md:text-8xl font-bold mb-6\">\n            <span className=\"neon-text\">R3B3L</span>{\" \"}\n            <span className=\"glitch-text\">M3D14</span>\n          </h1>\n          <p className=\"text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto\">\n            Sovereign media platform for the{\" \"}\n            <span className=\"neon-text font-semibold\">GodsIMiJ Empire</span>.\n            Truth-infused articles, memes, podcasts, and counter-narratives.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <span className=\"flame-badge text-lg px-6 py-3\">\n              🔥 FLAME-LICENSED CONTENT\n            </span>\n            <span className=\"glitch-text text-lg\">\n              NODE CERTIFIED PLATFORM\n            </span>\n          </div>\n        </div>\n\n        {/* Animated background elements */}\n        <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n          <div className=\"absolute top-1/4 left-1/4 w-2 h-2 bg-red-500 rounded-full animate-pulse\"></div>\n          <div className=\"absolute top-3/4 right-1/4 w-1 h-1 bg-cyan-400 rounded-full animate-ping\"></div>\n          <div className=\"absolute bottom-1/4 left-1/3 w-1 h-1 bg-red-400 rounded-full animate-pulse\"></div>\n        </div>\n      </section>\n\n      {/* Mission Statement */}\n      <section className=\"py-16 px-4 bg-gray-900/30\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-8 text-white\">\n            The ReBeLuTioN Media Division\n          </h2>\n          <p className=\"text-lg text-gray-300 leading-relaxed mb-8\">\n            We are the official media arm of the ReBeLuTioN, dedicated to exposing truth\n            and challenging mainstream narratives. Our content is{\" \"}\n            <span className=\"neon-text font-semibold\">FLAME-licensed</span> and{\" \"}\n            <span className=\"glitch-text font-semibold\">NODE certified</span> for\n            maximum authenticity and resistance to censorship.\n          </p>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mt-12\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">📜</div>\n              <h3 className=\"text-xl font-semibold mb-2 text-white\">Truth Scrolls</h3>\n              <p className=\"text-gray-400\">Deep-dive articles exposing hidden realities</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">🎭</div>\n              <h3 className=\"text-xl font-semibold mb-2 text-white\">Meme Warfare</h3>\n              <p className=\"text-gray-400\">Weaponized humor for cultural revolution</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">📻</div>\n              <h3 className=\"text-xl font-semibold mb-2 text-white\">R3B3L Radio</h3>\n              <p className=\"text-gray-400\">Podcasts from the resistance underground</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Articles */}\n      <section className=\"py-16 px-4\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"flex items-center justify-between mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-white\">\n              Latest Intelligence Drops\n            </h2>\n            <Link\n              href=\"/articles\"\n              className=\"text-red-400 hover:text-red-300 font-medium hover:underline\"\n            >\n              View All Articles →\n            </Link>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {featuredArticles.map((article) => (\n              <ArticleCard key={article.slug} {...article} />\n            ))}\n\n            {/* Placeholder cards for upcoming content */}\n            <div className=\"bg-gray-900/30 border border-gray-800 rounded-lg p-6 text-center\">\n              <div className=\"text-4xl mb-4\">🔜</div>\n              <h3 className=\"text-xl font-semibold mb-2 text-white\">More Scrolls Coming</h3>\n              <p className=\"text-gray-400\">Additional truth-infused content in development</p>\n              <span className=\"flame-badge mt-4 inline-block\">CLASSIFIED</span>\n            </div>\n\n            <div className=\"bg-gray-900/30 border border-gray-800 rounded-lg p-6 text-center\">\n              <div className=\"text-4xl mb-4\">📡</div>\n              <h3 className=\"text-xl font-semibold mb-2 text-white\">R3B3L Radio Launch</h3>\n              <p className=\"text-gray-400\">Podcast platform expansion planned for Phase 2</p>\n              <span className=\"glitch-text text-sm mt-4 block\">PHASE 2 DEPLOYMENT</span>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,mEAAmE;AACnE,MAAM,mBAAmB;IACvB;QACE,OAAO;QACP,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAY;;;;;;oCAAa;kDACzC,8OAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAEhC,8OAAC;gCAAE,WAAU;;oCAA2D;oCACrC;kDACjC,8OAAC;wCAAK,WAAU;kDAA0B;;;;;;oCAAsB;;;;;;;0CAGlE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgC;;;;;;kDAGhD,8OAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;kCAO1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;0BAKnB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,8OAAC;4BAAE,WAAU;;gCAA6C;gCAEF;8CACtD,8OAAC;oCAAK,WAAU;8CAA0B;;;;;;gCAAqB;gCAAK;8CACpE,8OAAC;oCAAK,WAAU;8CAA4B;;;;;;gCAAqB;;;;;;;sCAGnE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAG1D,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAKH,8OAAC;4BAAI,WAAU;;gCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC,iIAAA,CAAA,UAAW;wCAAqB,GAAG,OAAO;uCAAzB,QAAQ,IAAI;;;;;8CAIhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CAAK,WAAU;sDAAgC;;;;;;;;;;;;8CAGlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CAAK,WAAU;sDAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/D", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 692, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAA8C,EAAtB,AAAuB;YAAA;gBAEzG,UAAA,CAAA;gBAAA,QAAA;oBAAA,IAAA,0BAA4D;oBAAA;iBAAA;YAC5D;SAAA,KAAO,MAAMC,cAAc,IAAIX,mBAAmB;;KAChDY,YAAY;cACVC,IAAAA,EAAMZ;YAAAA,MAAAA,CAAUa,QAAQ;iBACxBC,MAAM,QAAA;wBAAA;4BACNC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,KAAU,iBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACV,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAA2C,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BAC3CC,MAAAA,CAAAA,KAAY,OAAA,CAAA;;qBACZC,UAAU;gBACVC,UAAU,EAAE;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}