exports.id=369,exports.ids=[369],exports.modules={4:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/components/Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/rebel-media/r3b3l-media/src/components/Navbar.tsx","default")},1135:()=>{},2032:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},5184:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},5597:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,5814,23)),Promise.resolve().then(s.bind(s,9190))},5968:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},8042:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>l});var r=s(7413);s(1135);var i=s(4),n=s(4536),o=s.n(n);function a(){return(0,r.jsxs)("footer",{className:"bg-black/90 border-t border-gray-800 mt-auto",children:[(0,r.jsxs)("div",{className:"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"text-xl font-bold neon-text",children:"R3B3L M3D14"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"Sovereign media platform for the GodsIMiJ Empire. Truth-infused content for the ReBeLuTioN."}),(0,r.jsx)("div",{className:"glitch-text text-sm",children:"\uD83D\uDD25 FLAME-LICENSED CONTENT"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-white",children:"Navigation"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:"/",className:"text-gray-400 hover:text-white hover:neon-text transition-colors",children:"Home"})}),(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:"/articles",className:"text-gray-400 hover:text-white hover:neon-text transition-colors",children:"Articles"})}),(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:"/about",className:"text-gray-400 hover:text-white hover:neon-text transition-colors",children:"About"})})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-white",children:"The Empire"}),(0,r.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,r.jsx)("li",{className:"text-gray-400",children:"GodsIMiJ Empire"}),(0,r.jsx)("li",{className:"text-gray-400",children:"ReBeLuTioN Media Division"}),(0,r.jsx)("li",{className:"text-gray-400",children:"Anti-Mainstream Intelligence"}),(0,r.jsx)("li",{className:"glitch-text",children:"NODE Certified"})]})]})]}),(0,r.jsxs)("div",{className:"mt-8 pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center",children:[(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"\xa9 2024 R3B3L M3D14. All rights reserved to the Empire."}),(0,r.jsxs)("div",{className:"mt-4 md:mt-0 flex space-x-4 text-sm",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Powered by"}),(0,r.jsx)("span",{className:"neon-text",children:"FLAME"}),(0,r.jsx)("span",{className:"text-gray-400",children:"•"}),(0,r.jsx)("span",{className:"glitch-text",children:"NODE SEALED"})]})]})]}),(0,r.jsx)("div",{className:"node-seal",children:"NODE CERTIFIED • FLAME LICENSED"})]})}let l={title:"R3B3L M3D14 | Sovereign Media Platform",description:"Truth-infused articles, memes, podcasts, and counter-narratives. Official media arm of the GodsIMiJ Empire ReBeLuTioN.",keywords:["rebel media","alternative news","counter-narrative","flame licensed","node certified"],authors:[{name:"R3B3L M3D14 Team"}],creator:"GodsIMiJ Empire",publisher:"R3B3L M3D14",robots:"index, follow",openGraph:{title:"R3B3L M3D14 | Sovereign Media Platform",description:"Truth-infused content for the ReBeLuTioN",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"R3B3L M3D14",description:"Sovereign media platform for truth-seekers"}};function d({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsxs)("body",{className:"min-h-screen flex flex-col bg-black text-white",children:[(0,r.jsx)(i.default,{}),(0,r.jsx)("main",{className:"flex-grow",children:e}),(0,r.jsx)(a,{})]})})}},9101:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23)),Promise.resolve().then(s.bind(s,4))},9190:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(687),i=s(5814),n=s.n(i),o=s(3210);function a(){let[e,t]=(0,o.useState)(!1);return(0,r.jsx)("nav",{className:"bg-black/90 backdrop-blur-sm border-b border-gray-800 sticky top-0 z-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(n(),{href:"/",className:"text-2xl font-bold neon-text",children:"R3B3L M3D14"})}),(0,r.jsx)("div",{className:"hidden md:block",children:(0,r.jsxs)("div",{className:"ml-10 flex items-baseline space-x-4",children:[(0,r.jsx)(n(),{href:"/",className:"text-gray-300 hover:text-white hover:neon-text px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Home"}),(0,r.jsx)(n(),{href:"/articles",className:"text-gray-300 hover:text-white hover:neon-text px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Articles"}),(0,r.jsx)(n(),{href:"/about",className:"text-gray-300 hover:text-white hover:neon-text px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"About"}),(0,r.jsx)("div",{className:"glitch-text px-3 py-2 text-sm font-medium",children:"\uD83D\uDD25 FLAME-LICENSED"})]})}),(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsx)("button",{onClick:()=>t(!e),className:"text-gray-400 hover:text-white hover:bg-gray-700 px-2 py-1 rounded-md",children:(0,r.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e?(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),e&&(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-800",children:[(0,r.jsx)(n(),{href:"/",className:"text-gray-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium",onClick:()=>t(!1),children:"Home"}),(0,r.jsx)(n(),{href:"/articles",className:"text-gray-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium",onClick:()=>t(!1),children:"Articles"}),(0,r.jsx)(n(),{href:"/about",className:"text-gray-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium",onClick:()=>t(!1),children:"About"})]})})]})})}}};