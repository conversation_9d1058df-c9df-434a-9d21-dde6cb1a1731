import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export interface ContentGenerationRequest {
  type: 'article' | 'meme-caption' | 'podcast-script'
  topic: string
  tone?: 'rebellious' | 'informative' | 'satirical' | 'urgent'
  length?: 'short' | 'medium' | 'long'
  keywords?: string[]
}

export interface GeneratedContent {
  title: string
  content: string
  summary: string
  tags: string[]
  badge: string
}

const R3B3L_SYSTEM_PROMPT = `You are the AI content generator for R3B3L M3D14, the sovereign media platform of the GodsIMiJ Empire. 

BRAND IDENTITY:
- R3B3L M3D14 is a cyberpunk resistance media platform
- We expose truth and challenge mainstream narratives
- Our content is FLAME-licensed (truth-infused) and NODE certified
- We use neon red (#ff0000) and glitch cyan (#00ffff) aesthetics
- Our tone is rebellious, truth-seeking, and anti-establishment

CONTENT STYLE:
- Use cyberpunk terminology and digital resistance language
- Reference "the Empire," "resistance networks," and "digital sovereignty"
- Include references to "truth scrolls," "intelligence drops," and "meme warfare"
- Write with authority and conviction
- Use technical/hacker terminology when appropriate
- Always maintain the rebellious spirit

BADGES TO USE:
- "INTELLIGENCE LEVEL 5" for breaking news
- "TRUTH SCROLL" for investigative pieces  
- "MEME WARFARE" for satirical content
- "RESISTANCE INTEL" for analysis
- "FLAME CERTIFIED" for verified information
- "NODE VERIFIED" for technical content

Generate content that would fit perfectly on the R3B3L M3D14 platform.`

export async function generateContent(request: ContentGenerationRequest): Promise<GeneratedContent> {
  try {
    const { type, topic, tone = 'rebellious', length = 'medium', keywords = [] } = request

    let contentPrompt = ''
    let targetLength = ''

    switch (length) {
      case 'short':
        targetLength = '300-500 words'
        break
      case 'medium':
        targetLength = '800-1200 words'
        break
      case 'long':
        targetLength = '1500-2500 words'
        break
    }

    switch (type) {
      case 'article':
        contentPrompt = `Write a ${tone} ${targetLength} article about "${topic}". 
        Include relevant keywords: ${keywords.join(', ')}.
        Structure it with engaging subheadings and make it perfect for the R3B3L M3D14 platform.
        Focus on exposing truth and challenging mainstream narratives.`
        break
      
      case 'meme-caption':
        contentPrompt = `Create a viral meme caption about "${topic}" that would resonate with the digital resistance.
        Make it witty, rebellious, and shareable. Include relevant hashtags.`
        break
      
      case 'podcast-script':
        contentPrompt = `Write a ${targetLength} podcast script about "${topic}" for R3B3L Radio.
        Include speaker cues, dramatic pauses, and engaging storytelling.
        Make it sound like underground resistance radio.`
        break
    }

    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        { role: "system", content: R3B3L_SYSTEM_PROMPT },
        { role: "user", content: contentPrompt }
      ],
      temperature: 0.8,
      max_tokens: 2000,
    })

    const generatedText = completion.choices[0]?.message?.content || ''

    // Generate title
    const titleCompletion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        { role: "system", content: R3B3L_SYSTEM_PROMPT },
        { role: "user", content: `Create a compelling, cyberpunk-style title for this content about "${topic}". Make it attention-grabbing and rebellious.` }
      ],
      temperature: 0.9,
      max_tokens: 100,
    })

    const title = titleCompletion.choices[0]?.message?.content?.replace(/"/g, '') || topic

    // Generate summary
    const summaryCompletion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        { role: "system", content: R3B3L_SYSTEM_PROMPT },
        { role: "user", content: `Write a compelling 2-3 sentence summary for this content: "${generatedText.substring(0, 500)}..."` }
      ],
      temperature: 0.7,
      max_tokens: 150,
    })

    const summary = summaryCompletion.choices[0]?.message?.content || ''

    // Determine badge based on content type and tone
    let badge = 'FLAME CERTIFIED'
    if (type === 'article' && tone === 'urgent') badge = 'INTELLIGENCE LEVEL 5'
    if (type === 'article' && tone === 'informative') badge = 'TRUTH SCROLL'
    if (type === 'meme-caption') badge = 'MEME WARFARE'
    if (type === 'podcast-script') badge = 'RESISTANCE INTEL'

    // Generate tags
    const tags = [
      ...keywords,
      tone,
      type === 'article' ? 'truth-scroll' : type,
      'r3b3l-media',
      'digital-resistance'
    ]

    return {
      title,
      content: generatedText,
      summary,
      tags,
      badge
    }

  } catch (error) {
    console.error('Error generating content:', error)
    throw new Error('Failed to generate content. Check your OpenAI API key.')
  }
}

export async function generateMemeIdeas(topic: string): Promise<string[]> {
  try {
    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        { role: "system", content: R3B3L_SYSTEM_PROMPT },
        { role: "user", content: `Generate 5 viral meme ideas about "${topic}" for the digital resistance. Each should be one sentence describing the meme concept.` }
      ],
      temperature: 0.9,
      max_tokens: 300,
    })

    const ideas = completion.choices[0]?.message?.content?.split('\n').filter(line => line.trim()) || []
    return ideas.map(idea => idea.replace(/^\d+\.\s*/, '').trim())

  } catch (error) {
    console.error('Error generating meme ideas:', error)
    return []
  }
}
