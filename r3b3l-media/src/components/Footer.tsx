import Link from 'next/link'

export default function Footer() {
  return (
    <footer className="bg-black/90 border-t border-gray-800 mt-auto">
      <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <h3 className="text-xl font-bold neon-text">R3B3L M3D14</h3>
            <p className="text-gray-400 text-sm">
              Sovereign media platform for the GodsIMiJ Empire.
              Truth-infused content for the ReBeLuTioN.
            </p>
            <div className="glitch-text text-sm">
              🔥 FLAME-LICENSED CONTENT
            </div>
          </div>

          {/* Links */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-white">Navigation</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/" className="text-gray-400 hover:text-white hover:neon-text transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link href="/articles" className="text-gray-400 hover:text-white hover:neon-text transition-colors">
                  Articles
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-gray-400 hover:text-white hover:neon-text transition-colors">
                  About
                </Link>
              </li>
            </ul>
          </div>

          {/* Empire Info */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-white">The Empire</h4>
            <ul className="space-y-2 text-sm">
              <li className="text-gray-400">GodsIMiJ Empire</li>
              <li className="text-gray-400">ReBeLuTioN Media Division</li>
              <li className="text-gray-400">Anti-Mainstream Intelligence</li>
              <li className="glitch-text">NODE Certified</li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-8 pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            © 2024 R3B3L M3D14. All rights reserved to the Empire.
          </p>
          <div className="mt-4 md:mt-0 flex space-x-4 text-sm">
            <span className="text-gray-400">Powered by</span>
            <span className="neon-text">FLAME</span>
            <span className="text-gray-400">•</span>
            <span className="glitch-text">NODE SEALED</span>
          </div>
        </div>
      </div>

      {/* NODE Seal Watermark */}
      <div className="node-seal">
        NODE CERTIFIED • FLAME LICENSED
      </div>
    </footer>
  )
}
