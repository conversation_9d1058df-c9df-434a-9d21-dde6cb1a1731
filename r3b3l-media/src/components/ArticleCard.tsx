import Link from 'next/link'

interface ArticleCardProps {
  title: string
  slug: string
  date: string
  summary: string
  author: string
  badge: string
}

export default function ArticleCard({ title, slug, date, summary, author, badge }: ArticleCardProps) {
  return (
    <article className="bg-gray-900/50 border border-gray-800 rounded-lg p-6 hover:border-red-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-red-500/10">
      <div className="space-y-4">
        {/* Badge and Date */}
        <div className="flex items-center justify-between">
          <span className="flame-badge">
            🔥 {badge}
          </span>
          <time className="text-gray-400 text-sm">
            {new Date(date).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </time>
        </div>

        {/* Title */}
        <h2 className="text-xl font-bold text-white hover:neon-text transition-colors">
          <Link href={`/articles/${slug}`}>
            {title}
          </Link>
        </h2>

        {/* Summary */}
        <p className="text-gray-300 text-sm leading-relaxed">
          {summary}
        </p>

        {/* Author and Read More */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-800">
          <div className="flex items-center space-x-2">
            <span className="text-gray-400 text-sm">By</span>
            <span className="text-white text-sm font-medium">{author}</span>
          </div>
          
          <Link 
            href={`/articles/${slug}`}
            className="text-red-400 hover:text-red-300 text-sm font-medium hover:underline transition-colors"
          >
            Read More →
          </Link>
        </div>

        {/* NODE Certification */}
        <div className="flex items-center justify-end">
          <span className="glitch-text text-xs">NODE CERTIFIED</span>
        </div>
      </div>
    </article>
  )
}
